{% extends 'base.html' %}
{% load static %}
{% block title %}{{ patient.full_name }} - نظام العيادة الطبية{% endblock %}

{% block content %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل المريض - {{ patient.name }}</title>
    <link rel="stylesheet" href="{% static 'css/style.css' %}"> {# تأكد من تعريف STATIC_URL في settings.py #}
    <style>
        body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f7f6;
    color: #333;
    direction: rtl; /* للغة العربية */
    text-align: right; /* للغة العربية */
}

.container {
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background-color: #fff;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #eee;
}

header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.patient-info-summary p {
    margin: 5px 0;
    font-size: 1.1em;
    line-height: 1.6;
}

.patient-info-summary strong {
    color: #4a6a8c;
}

.medical-section {
    margin-bottom: 40px;
    padding: 20px;
    background-color: #fcfcfc;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.medical-section h2 {
    color: #34495e;
    border-bottom: 2px solid #e0e0e0;
    padding-bottom: 10px;
    margin-bottom: 20px;
    text-align: right;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

table th, table td {
    border: 1px solid #ddd;
    padding: 12px;
    text-align: right;
}

table th {
    background-color: #5b7f9f;
    color: white;
    font-weight: bold;
}

table tr:nth-child(even) {
    background-color: #f9f9f9;
}

table tr:hover {
    background-color: #f1f1f1;
}

.imaging-gallery {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: flex-start;
}

.imaging-item {
    background-color: #fff;
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 15px;
    width: calc(33% - 20px); /* 3 items per row, adjust as needed */
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    text-align: center;
}

.imaging-item img {
    max-width: 100%;
    height: auto;
    border-radius: 5px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: transform 0.2s ease-in-out;
}

.imaging-item img:hover {
    transform: scale(1.02);
}

.imaging-item h3 {
    color: #4a6a8c;
    font-size: 1.1em;
    margin-top: 0;
    margin-bottom: 10px;
}

.imaging-item p {
    font-size: 0.9em;
    color: #555;
    line-height: 1.5;
}

/* Modal Styling */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    padding-top: 60px;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.9);
}

.modal-content {
    margin: auto;
    display: block;
    width: 80%;
    max-width: 700px;
}

.close-button {
    position: absolute;
    top: 15px;
    right: 35px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    transition: 0.3s;
    cursor: pointer;
}

.close-button:hover,
.close-button:focus {
    color: #bbb;
    text-decoration: none;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        padding: 15px;
    }
    table th, table td {
        padding: 8px;
        font-size: 0.9em;
    }
    .imaging-item {
        width: calc(50% - 20px); /* 2 items per row */
    }
}

@media (max-width: 480px) {
    .imaging-item {
        width: 100%; /* 1 item per row */
    }
    .modal-content {
        width: 95%;
    }
}
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>تفاصيل المريض: {{ patient.name }}</h1>
            <div class="patient-info-summary">
                <p><strong>تاريخ الميلاد:</strong> {{ patient.date_of_birth }}</p>
                <p><strong>الجنس:</strong> {% if patient.gender == 'male' %}ذكر{% else %}أنثى{% endif %}</p>
                <p><strong>رقم التواصل:</strong> {{ patient.contact_number|default:"غير متوفر" }}</p>
                <p><strong>العنوان:</strong> {{ patient.address|default:"غير متوفر" }}</p>
            </div>
        </header>

        <main>
            <section class="medical-section">
                <h2>فحوصات المريض</h2>
                {% if examinations %}
                <table>
                    <thead>
                        <tr>
                            <th>نوع الفحص</th>
                            <th>التاريخ</th>
                            <th>النتائج</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for exam in examinations %}
                        <tr>
                            <td>{{ exam.test_name }}</td>
                            <td>{{ exam.ordered_date }}</td>
                            <td>{{ exam.results }}</td>
                            <td>{{ exam.notes|default:"-" }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                {% else %}
                <p>لا توجد فحوصات مسجلة لهذا المريض.</p>
                {% endif %}
            </section>

            <section class="medical-section">
                <h2>تشخيصات المريض</h2>
                {% if diagnoses %}
                <table>
                    <thead>
                        <tr>
                            <th>تاريخ التشخيص</th>
                             <th> الاعراض</th>
                            <th>الوصف</th>

                        </tr>
                    </thead>
                    <tbody>
                        {% for diagnosis in diagnoses %}
                        <tr>
                            <td>{{ diagnosis.diagnosis_date }}</td>

                            <td>{{diagnosis.symptoms}}</td>

                            <td>{{ diagnosis.diagnosis }}</td>
                            <td>{{ diagnosis.icd_code|default:"-" }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                {% else %}
                <p>لا توجد تشخيصات مسجلة لهذا المريض.</p>
                {% endif %}
            </section>

            <section class="medical-section">
                <h2>وصفات طبية</h2>
                {% if prescriptions %}
                <table>
                    <thead>
                        <tr>
                            <th>تاريخ الوصفة</th>
                            <th>اسم الدواء</th>
                            <th>الجرعة</th>
                            <th>تعليمات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for prescription in prescriptions %}
                        <tr>
                            <td>{{ prescription.prescription_date }}</td>
                            <td>{{ prescription.medications }}</td>
                            <td>{{ prescription.dosage }}</td>
                            <td>{{ prescription.instructions }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                {% else %}
                <p>لا توجد وصفات طبية مسجلة لهذا المريض.</p>
                {% endif %}
            </section>

            <section class="medical-section">
                <h2>أشعة وتقارير تصوير</h2>
                {% if imaging_scans %}
                <div class="imaging-gallery">
                    {% for scan in imaging_scans %}
                    <div class="imaging-item">
                        <h3>{{ scan.rays_name }} ({{ scan.scan_date }})</h3>
                        {% if scan.image %}
                            <img src="{{ scan.image.url }}" alt="صورة أشعة {{ scan.imaging_type }}" onclick="openModal('{{ scan.image.url }}')">
                        {% else %}
                            <p>لا توجد صورة متوفرة.</p>
                        {% endif %}
                        <p><strong>التقرير:</strong> {{ scan.report|default:"لا يوجد تقرير." }}</p>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p>لا توجد صور أشعة مسجلة لهذا المريض.</p>
                {% endif %}
            </section>
        </main>
    </div>

    <div id="imageModal" class="modal">
        <span class="close-button" onclick="closeModal()">&times;</span>
        <img class="modal-content" id="modalImage">
    </div>

    <script src="{% static 'js/script.js' %}"></script>
    <script !src="">
        const imageModal = document.getElementById('imageModal');
const modalImage = document.getElementById('modalImage');

function openModal(imageUrl) {
    imageModal.style.display = 'block';
    modalImage.src = imageUrl;
}

function closeModal() {
    imageModal.style.display = 'none';
}

// إغلاق المودال عند النقر خارج الصورة
window.onclick = function(event) {
    if (event.target == imageModal) {
        imageModal.style.display = 'none';
    }
}
    </script>
</body>
</html>
{% endblock %}