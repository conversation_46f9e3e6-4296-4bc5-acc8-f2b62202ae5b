{% extends 'base.html' %}

{% block title %}قائمة الفحوصات الطبية - نظام العيادة الطبية{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="bg-gradient-danger text-white rounded-3 p-4 mb-4 shadow" style="margin-top: 50px;">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    <div class="bg-white bg-opacity-25 rounded-circle p-3 me-3">
                        <i class="bi bi-prescription2 fs-2 text-white"></i>
                    </div>
                    <div>
                        <h1 class="h2 mb-1 text-white">قائمة الفحوصات الطبية</h1>
                        <p class="mb-0 text-white-50">إدارة وعرض الفحصات الطبية للمرضى</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{% url 'lab' %}" class="btn btn-light btn-lg">
                    <i class="bi bi-plus-circle"></i>
                    <span class="d-none d-sm-inline">كتابة فحص جديدة</span>
                    <span class="d-inline d-sm-none">فحص جديدة</span>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Search Form -->
<div class="row mb-3">
    <div class="col-md-6">
        <form method="get" class="d-flex">
            <input type="text" name="search" class="form-control me-2"
                   placeholder="البحث بالمريض أو الفحص..."
                   value="{{ search_query }}">
            <button type="submit" class="btn btn-outline-secondary">
                <i class="bi bi-search"></i> بحث
            </button>
        </form>
    </div>
</div>

<!-- Prescriptions Table -->
<div class="card shadow">
    <div class="card-body">
        {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>المريض</th>
                            <th>التاريخ</th>
                            <th>الطبيب</th>
                            <th>الفحص</th>
                            <th>فئة الفحص</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for prescription in page_obj %}
                            <tr>
                                <td>
                                    <a href="{% url 'patient_detail' prescription.patient.pk %}" class="text-decoration-none">
                                        {{ prescription.patient.full_name }}
                                    </a>
                                </td>
                                <td>{{ prescription.ordered_date|date:"d/m/Y H:i" }}</td>
                                <td>{{ prescription.doctor.first_name }} {{ prescription.doctor.last_name }}</td>
                                <td>{{ prescription.test_name|truncatechars:50 }}</td>
                                <td>{{ prescription.test_category }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'lab_detail' prescription.pk %}"
                                           class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="{% url 'lab_update' prescription.pk %}"
                                           class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button onclick="printPrescription({{ prescription.pk }})"
                                                class="btn btn-sm btn-outline-success" title="طباعة">
                                            <i class="bi bi-printer"></i>
                                        </button>
                                        <a href="{% url 'lab_delete' prescription.pk %}"
                                           class="btn btn-sm btn-outline-danger" title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
                <nav aria-label="Prescription pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}">
                                    الأولى
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">
                                    السابقة
                                </a>
                            </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}">
                                        {{ num }}
                                    </a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">
                                    التالية
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}">
                                    الأخيرة
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}

        {% else %}
            <div class="text-center py-5">
                <i class="bi bi-prescription display-1 text-muted"></i>
                <h4 class="mt-3">لا توجد وصفات طبية</h4>
                <p class="text-muted">لم يتم العثور على أي وصفات طبية{% if search_query %} للبحث "{{ search_query }}"{% endif %}</p>
                <a href="{% url 'prescription_create' %}" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> كتابة أول وصفة طبية
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function printPrescription(prescriptionId) {
    // Open prescription in new window for printing
    const printWindow = window.open(`/prescriptions/${prescriptionId}/`, '_blank');
    printWindow.onload = function() {
        printWindow.print();
    };
}
</script>
{% endblock %}
