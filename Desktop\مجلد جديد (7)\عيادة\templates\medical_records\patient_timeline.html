{% extends 'control_panels/base_dashboard.html' %}
{% load static %}

{% block title %}التاريخ الطبي - {{ patient.full_name }}{% endblock %}

{% block extra_css %}
<style>
    .timeline-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .timeline-container {
        position: relative;
        padding: 20px 0;
    }

    .timeline-line {
        position: absolute;
        left: 30px;
        top: 0;
        bottom: 0;
        width: 4px;
        background: linear-gradient(to bottom, #667eea, #764ba2);
        border-radius: 2px;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 30px;
        padding-left: 80px;
        opacity: 0;
        transform: translateX(-50px);
        animation: slideIn 0.6s ease forwards;
    }

    .timeline-item:nth-child(even) {
        animation-delay: 0.2s;
    }

    .timeline-item:nth-child(odd) {
        animation-delay: 0.1s;
    }

    @keyframes slideIn {
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    .timeline-marker {
        position: absolute;
        left: -50px;
        top: 20px;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        z-index: 10;
    }

    .timeline-card {
        background: white;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border-left: 5px solid #007bff;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .timeline-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .timeline-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #667eea, #764ba2);
    }

    .record-type-diagnosis { border-left-color: #28a745; }
    .record-type-treatment { border-left-color: #17a2b8; }
    .record-type-surgery { border-left-color: #dc3545; }
    .record-type-emergency { border-left-color: #ffc107; }
    .record-type-consultation { border-left-color: #6f42c1; }
    .record-type-follow_up { border-left-color: #fd7e14; }

    .marker-diagnosis { background: #28a745; }
    .marker-treatment { background: #17a2b8; }
    .marker-surgery { background: #dc3545; }
    .marker-emergency { background: #ffc107; }
    .marker-consultation { background: #6f42c1; }
    .marker-follow_up { background: #fd7e14; }

    .record-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }

    .record-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
    }

    .record-date {
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .record-type-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        color: white;
        margin-left: 10px;
    }

    .priority-badge {
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .priority-critical { background: #dc3545; color: white; }
    .priority-high { background: #fd7e14; color: white; }
    .priority-normal { background: #28a745; color: white; }
    .priority-low { background: #6c757d; color: white; }

    .record-content {
        line-height: 1.6;
        color: #495057;
    }

    .record-meta {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #f8f9fa;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.9rem;
        color: #6c757d;
    }

    .filter-controls {
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: 0 3px 10px rgba(0,0,0,0.05);
    }

    .filter-btn {
        margin: 5px;
        padding: 8px 16px;
        border-radius: 20px;
        border: 2px solid #e9ecef;
        background: white;
        color: #6c757d;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .filter-btn.active {
        background: #667eea;
        border-color: #667eea;
        color: white;
    }

    .filter-btn:hover {
        border-color: #667eea;
        color: #667eea;
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 3px 10px rgba(0,0,0,0.05);
        transition: transform 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .attachments-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
        margin-top: 15px;
    }

    .attachment-item {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .attachment-item:hover {
        background: #e9ecef;
        transform: scale(1.05);
    }

    .attachment-icon {
        font-size: 2rem;
        margin-bottom: 10px;
        color: #667eea;
    }

    .search-box {
        position: relative;
        margin-bottom: 20px;
    }

    .search-input {
        width: 100%;
        padding: 12px 45px 12px 20px;
        border: 2px solid #e9ecef;
        border-radius: 25px;
        font-size: 1rem;
        transition: border-color 0.3s ease;
    }

    .search-input:focus {
        border-color: #667eea;
        outline: none;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .search-icon {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
    }

    .floating-add-btn {
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        font-size: 1.5rem;
        z-index: 1000;
        transition: all 0.3s ease;
    }

    .floating-add-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 8px 25px rgba(0,0,0,0.4);
        color: white;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }

    @media (max-width: 768px) {
        .timeline-item {
            padding-left: 60px;
        }

        .timeline-marker {
            left: -40px;
            width: 30px;
            height: 30px;
            font-size: 1rem;
        }

        .timeline-line {
            left: 20px;
        }

        .stats-cards {
            grid-template-columns: repeat(2, 1fr);
        }
    }
</style>
{% endblock %}

{% block breadcrumb %}
    <li class="breadcrumb-item"><a href="{% url 'patients:patient_list' %}">المرضى</a></li>
    <li class="breadcrumb-item"><a href="{% url 'patients:patient_detail' patient.pk %}">{{ patient.full_name }}</a></li>
    <li class="breadcrumb-item active">التاريخ الطبي</li>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="timeline-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2 class="mb-2">
                    <i class="fas fa-history me-3"></i>
                    التاريخ الطبي المفصل
                </h2>
                <h4 class="mb-0">{{ patient.full_name }}</h4>
                <p class="mb-0 opacity-75">رقم المريض: {{ patient.patient_id }} | العمر: {{ patient.age }} سنة</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-light btn-lg me-2" onclick="exportTimeline()">
                    <i class="fas fa-download me-2"></i>
                    تصدير التقرير
                </button>
                <button class="btn btn-light btn-lg" onclick="printTimeline()">
                    <i class="fas fa-print me-2"></i>
                    طباعة
                </button>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="stats-cards">
        <div class="stat-card">
            <div class="stat-number text-primary">{{ total_records }}</div>
            <div class="stat-label">إجمالي السجلات</div>
        </div>
        <div class="stat-card">
            <div class="stat-number text-success">{{ diagnoses_count }}</div>
            <div class="stat-label">التشخيصات</div>
        </div>
        <div class="stat-card">
            <div class="stat-number text-info">{{ treatments_count }}</div>
            <div class="stat-label">العلاجات</div>
        </div>
        <div class="stat-card">
            <div class="stat-number text-warning">{{ surgeries_count }}</div>
            <div class="stat-label">العمليات</div>
        </div>
        <div class="stat-card">
            <div class="stat-number text-danger">{{ emergencies_count }}</div>
            <div class="stat-label">الطوارئ</div>
        </div>
    </div>

    <!-- أدوات التحكم والبحث -->
    <div class="filter-controls">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="search-box">
                    <input type="text" class="search-input" id="searchInput" placeholder="البحث في السجلات الطبية...">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            <div class="col-md-6">
                <div class="text-end">
                    <button class="filter-btn active" data-filter="all">الكل</button>
                    <button class="filter-btn" data-filter="diagnosis">تشخيص</button>
                    <button class="filter-btn" data-filter="treatment">علاج</button>
                    <button class="filter-btn" data-filter="surgery">جراحة</button>
                    <button class="filter-btn" data-filter="emergency">طوارئ</button>
                    <button class="filter-btn" data-filter="consultation">استشارة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- الخط الزمني للسجلات الطبية -->
    <div class="timeline-container">
        <div class="timeline-line"></div>

        {% for record in medical_records %}
        <div class="timeline-item" data-type="{{ record.record_type|lower }}" data-priority="{{ record.priority|lower }}">
            <div class="timeline-marker marker-{{ record.record_type|lower }}">
                {% if record.record_type == 'DIAGNOSIS' %}
                    <i class="fas fa-stethoscope"></i>
                {% elif record.record_type == 'TREATMENT' %}
                    <i class="fas fa-pills"></i>
                {% elif record.record_type == 'SURGERY' %}
                    <i class="fas fa-cut"></i>
                {% elif record.record_type == 'EMERGENCY' %}
                    <i class="fas fa-ambulance"></i>
                {% elif record.record_type == 'CONSULTATION' %}
                    <i class="fas fa-user-md"></i>
                {% elif record.record_type == 'FOLLOW_UP' %}
                    <i class="fas fa-calendar-check"></i>
                {% else %}
                    <i class="fas fa-notes-medical"></i>
                {% endif %}
            </div>

            <div class="timeline-card record-type-{{ record.record_type|lower }}">
                <div class="record-header">
                    <div>
                        <h5 class="record-title">{{ record.chief_complaint }}</h5>
                        <div class="record-date">
                            <i class="fas fa-calendar me-1"></i>
                            {{ record.created_at|date:"Y-m-d H:i" }}
                        </div>
                    </div>
                    <div>
                        <span class="record-type-badge" style="background:
                            {% if record.record_type == 'DIAGNOSIS' %}#28a745
                            {% elif record.record_type == 'TREATMENT' %}#17a2b8
                            {% elif record.record_type == 'SURGERY' %}#dc3545
                            {% elif record.record_type == 'EMERGENCY' %}#ffc107
                            {% elif record.record_type == 'CONSULTATION' %}#6f42c1
                            {% elif record.record_type == 'FOLLOW_UP' %}#fd7e14
                            {% else %}#6c757d{% endif %}">
                            {{ record.get_record_type_display }}
                        </span>
                        <span class="priority-badge priority-{{ record.priority|lower }}">
                            {{ record.get_priority_display }}
                        </span>
                    </div>
                </div>

                <div class="record-content">
                    {% if record.history_of_present_illness %}
                    <div class="mb-3">
                        <strong>تاريخ المرض الحالي:</strong>
                        <p class="mb-2">{{ record.history_of_present_illness|truncatewords:30 }}</p>
                    </div>
                    {% endif %}

                    {% if record.assessment %}
                    <div class="mb-3">
                        <strong>التقييم:</strong>
                        <p class="mb-2">{{ record.assessment|truncatewords:25 }}</p>
                    </div>
                    {% endif %}

                    {% if record.plan %}
                    <div class="mb-3">
                        <strong>الخطة:</strong>
                        <p class="mb-2">{{ record.plan|truncatewords:25 }}</p>
                    </div>
                    {% endif %}
                </div>

                <!-- المرفقات -->
                {% if record.attachments.exists %}
                <div class="attachments-section">
                    <h6 class="mb-2"><i class="fas fa-paperclip me-1"></i> المرفقات ({{ record.attachments.count }})</h6>
                    <div class="attachments-grid">
                        {% for attachment in record.attachments.all|slice:":4" %}
                        <div class="attachment-item" onclick="viewAttachment('{{ attachment.file.url }}')">
                            <div class="attachment-icon">
                                {% if attachment.attachment_type == 'image' %}
                                    <i class="fas fa-image"></i>
                                {% elif attachment.attachment_type == 'document' %}
                                    <i class="fas fa-file-alt"></i>
                                {% elif attachment.attachment_type == 'xray' %}
                                    <i class="fas fa-x-ray"></i>
                                {% else %}
                                    <i class="fas fa-file"></i>
                                {% endif %}
                            </div>
                            <div class="attachment-title">{{ attachment.title|truncatechars:15 }}</div>
                        </div>
                        {% endfor %}
                        {% if record.attachments.count > 4 %}
                        <div class="attachment-item" onclick="viewAllAttachments({{ record.id }})">
                            <div class="attachment-icon">
                                <i class="fas fa-ellipsis-h"></i>
                            </div>
                            <div class="attachment-title">+{{ record.attachments.count|add:"-4" }} أخرى</div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <div class="record-meta">
                    <div>
                        <i class="fas fa-user-md me-1"></i>
                        د. {{ record.doctor.full_name }}
                        <span class="mx-2">|</span>
                        <i class="fas fa-building me-1"></i>
                        {{ record.department.name }}
                    </div>
                    <div>
                        <button class="btn btn-sm btn-outline-primary" onclick="viewFullRecord({{ record.id }})">
                            <i class="fas fa-eye me-1"></i>
                            عرض كامل
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="empty-state">
            <i class="fas fa-notes-medical"></i>
            <h4>لا توجد سجلات طبية</h4>
            <p>لم يتم إنشاء أي سجلات طبية لهذا المريض بعد</p>
            <button class="btn btn-primary" onclick="addNewRecord()">
                <i class="fas fa-plus me-2"></i>
                إضافة سجل جديد
            </button>
        </div>
        {% endfor %}
    </div>
</div>

<!-- زر عائم لإضافة سجل جديد -->
<button class="floating-add-btn" onclick="addNewRecord()" title="إضافة سجل طبي جديد">
    <i class="fas fa-plus"></i>
</button>
{% endblock %}

{% block extra_js %}
<script>
// === وظائف التاريخ الطبي التفاعلي ===

document.addEventListener('DOMContentLoaded', function() {
    initializeTimeline();
    setupFilters();
    setupSearch();
});

// تهيئة الخط الزمني
function initializeTimeline() {
    // تأثير الظهور التدريجي للعناصر
    const timelineItems = document.querySelectorAll('.timeline-item');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateX(0)';
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    timelineItems.forEach(item => {
        observer.observe(item);
    });
}

// إعداد فلاتر النوع
function setupFilters() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const timelineItems = document.querySelectorAll('.timeline-item');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // إزالة الفئة النشطة من جميع الأزرار
            filterButtons.forEach(btn => btn.classList.remove('active'));
            // إضافة الفئة النشطة للزر المحدد
            this.classList.add('active');

            const filterType = this.getAttribute('data-filter');

            timelineItems.forEach(item => {
                if (filterType === 'all' || item.getAttribute('data-type') === filterType) {
                    item.style.display = 'block';
                    setTimeout(() => {
                        item.style.opacity = '1';
                        item.style.transform = 'translateX(0)';
                    }, 100);
                } else {
                    item.style.opacity = '0';
                    item.style.transform = 'translateX(-50px)';
                    setTimeout(() => {
                        item.style.display = 'none';
                    }, 300);
                }
            });
        });
    });
}

// إعداد البحث
function setupSearch() {
    const searchInput = document.getElementById('searchInput');
    const timelineItems = document.querySelectorAll('.timeline-item');

    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();

        timelineItems.forEach(item => {
            const content = item.textContent.toLowerCase();

            if (content.includes(searchTerm)) {
                item.style.display = 'block';
                item.style.opacity = '1';
                item.style.transform = 'translateX(0)';
            } else {
                item.style.opacity = '0';
                item.style.transform = 'translateX(-50px)';
                setTimeout(() => {
                    item.style.display = 'none';
                }, 300);
            }
        });

        // إذا كان البحث فارغ، أظهر جميع العناصر
        if (searchTerm === '') {
            timelineItems.forEach(item => {
                item.style.display = 'block';
                item.style.opacity = '1';
                item.style.transform = 'translateX(0)';
            });
        }
    });
}

// عرض السجل الكامل
function viewFullRecord(recordId) {
    const url = `/medical-records/${recordId}/detail/`;
    window.open(url, '_blank', 'width=1000,height=800,scrollbars=yes,resizable=yes');
    console.log('📋 فتح السجل الطبي الكامل:', recordId);
}

// عرض المرفق
function viewAttachment(fileUrl) {
    window.open(fileUrl, '_blank');
    console.log('📎 فتح المرفق:', fileUrl);
}

// عرض جميع المرفقات
function viewAllAttachments(recordId) {
    const url = `/medical-records/${recordId}/attachments/`;
    window.open(url, '_blank', 'width=900,height=700,scrollbars=yes,resizable=yes');
    console.log('📁 فتح جميع مرفقات السجل:', recordId);
}

// إضافة سجل طبي جديد
function addNewRecord() {
    const patientId = {{ patient.id }};
    const url = `/medical-records/create/?patient_id=${patientId}`;
    window.open(url, '_blank', 'width=1200,height=900,scrollbars=yes,resizable=yes');
    console.log('➕ فتح صفحة إضافة سجل طبي جديد للمريض:', patientId);
}

// تصدير التقرير
function exportTimeline() {
    const patientId = {{ patient.id }};
    const url = `/medical-records/export-timeline/?patient_id=${patientId}`;

    // إنشاء رابط تحميل
    const link = document.createElement('a');
    link.href = url;
    link.download = `timeline_{{ patient.full_name }}_${new Date().toISOString().split('T')[0]}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    console.log('📥 تصدير التاريخ الطبي للمريض:', patientId);
}

// طباعة التقرير
function printTimeline() {
    // إخفاء العناصر غير المطلوبة للطباعة
    const elementsToHide = [
        '.floating-add-btn',
        '.filter-controls',
        '.breadcrumb',
        'nav',
        'footer'
    ];

    elementsToHide.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => el.style.display = 'none');
    });

    // طباعة الصفحة
    window.print();

    // إعادة إظهار العناصر بعد الطباعة
    setTimeout(() => {
        elementsToHide.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => el.style.display = '');
        });
    }, 1000);

    console.log('🖨️ طباعة التاريخ الطبي');
}

// تحديث الإحصائيات
function updateStats() {
    const visibleItems = document.querySelectorAll('.timeline-item[style*="display: block"], .timeline-item:not([style*="display: none"])');
    const totalVisible = visibleItems.length;

    // يمكن إضافة المزيد من الإحصائيات هنا
    console.log('📊 عدد السجلات المعروضة:', totalVisible);
}

// تأثيرات إضافية للتفاعل
document.addEventListener('DOMContentLoaded', function() {
    // تأثير hover للبطاقات
    const timelineCards = document.querySelectorAll('.timeline-card');

    timelineCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // تأثير النقر على البطاقات
    timelineCards.forEach(card => {
        card.addEventListener('click', function(e) {
            // تجنب التفعيل عند النقر على الأزرار
            if (!e.target.closest('button')) {
                const recordId = this.closest('.timeline-item').querySelector('[onclick*="viewFullRecord"]')
                    ?.getAttribute('onclick')?.match(/\d+/)?.[0];
                if (recordId) {
                    viewFullRecord(recordId);
                }
            }
        });
    });

    // تأثير الكتابة للبحث
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });

        searchInput.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    }
});

// وظائف مساعدة
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// تحسين الأداء - تحميل كسول للصور
function lazyLoadImages() {
    const images = document.querySelectorAll('img[data-src]');

    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                imageObserver.unobserve(img);
            }
        });
    });

    images.forEach(img => imageObserver.observe(img));
}

// تهيئة التحميل الكسول عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', lazyLoadImages);
</script>

<!-- CSS للطباعة -->
<style media="print">
    .floating-add-btn,
    .filter-controls,
    .breadcrumb,
    nav,
    footer {
        display: none !important;
    }

    .timeline-header {
        background: #667eea !important;
        color: white !important;
        -webkit-print-color-adjust: exact;
    }

    .timeline-card {
        break-inside: avoid;
        margin-bottom: 20px;
    }

    .timeline-line {
        background: #667eea !important;
        -webkit-print-color-adjust: exact;
    }

    body {
        font-size: 12px;
    }

    .record-title {
        font-size: 14px;
    }
</style>
{% endblock %}
