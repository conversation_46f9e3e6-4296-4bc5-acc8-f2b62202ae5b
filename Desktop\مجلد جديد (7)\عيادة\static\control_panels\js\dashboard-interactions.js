// تفاعلات متقدمة للوحات التحكم

// دالة تصدير الرسوم البيانية
function exportChart(chartId, filename = 'chart') {
    const canvas = document.getElementById(chartId);
    if (canvas) {
        const link = document.createElement('a');
        link.download = filename + '.png';
        link.href = canvas.toDataURL();
        link.click();
    }
}

// دالة طباعة الرسم البياني
function printChart(chartId) {
    const canvas = document.getElementById(chartId);
    if (canvas) {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>طباعة الرسم البياني</title>
                    <style>
                        body { margin: 0; padding: 20px; text-align: center; }
                        img { max-width: 100%; height: auto; }
                    </style>
                </head>
                <body>
                    <img src="${canvas.toDataURL()}" />
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }
}

// دالة تحديث البيانات في الوقت الفعلي
function updateDashboardData() {
    // يمكن استخدام AJAX لتحديث البيانات
    fetch('/dashboard/api/update-data/')
        .then(response => response.json())
        .then(data => {
            // تحديث الإحصائيات
            updateStatCards(data.stats);
            // تحديث الرسوم البيانية
            updateCharts(data.charts);
        })
        .catch(error => {
            console.log('خطأ في تحديث البيانات:', error);
        });
}

// دالة تحديث بطاقات الإحصائيات
function updateStatCards(stats) {
    Object.keys(stats).forEach(key => {
        const element = document.querySelector(`[data-stat="${key}"]`);
        if (element) {
            // تأثير انيميشن للتحديث
            element.style.transform = 'scale(1.1)';
            element.textContent = stats[key];
            setTimeout(() => {
                element.style.transform = 'scale(1)';
            }, 200);
        }
    });
}

// دالة تحديث الرسوم البيانية
function updateCharts(chartsData) {
    Object.keys(chartsData).forEach(chartId => {
        const chart = Chart.getChart(chartId);
        if (chart) {
            chart.data = chartsData[chartId];
            chart.update('active');
        }
    });
}

// دالة إضافة تنبيهات ذكية
function addSmartNotifications() {
    // تنبيه للمخزون المنخفض
    const lowStockCount = parseInt(document.querySelector('[data-stat="low_stock"]')?.textContent || 0);
    if (lowStockCount > 0) {
        showNotification('تحذير', `يوجد ${lowStockCount} دواء بمخزون منخفض`, 'warning');
    }
    
    // تنبيه للحالات الحرجة
    const criticalCases = parseInt(document.querySelector('[data-stat="critical_cases"]')?.textContent || 0);
    if (criticalCases > 0) {
        showNotification('طوارئ', `يوجد ${criticalCases} حالة حرجة تحتاج تدخل فوري`, 'danger');
    }
    
    // تنبيه للفواتير المتأخرة
    const overdueInvoices = parseInt(document.querySelector('[data-stat="overdue_invoices"]')?.textContent || 0);
    if (overdueInvoices > 0) {
        showNotification('تذكير', `يوجد ${overdueInvoices} فاتورة متأخرة السداد`, 'info');
    }
}

// دالة عرض التنبيهات
function showNotification(title, message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
    
    notification.innerHTML = `
        <strong>${title}:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// دالة البحث السريع
function initQuickSearch() {
    const searchInput = document.getElementById('quickSearch');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase();
            const searchableElements = document.querySelectorAll('[data-searchable]');
            
            searchableElements.forEach(element => {
                const text = element.textContent.toLowerCase();
                const parent = element.closest('.table-row, .list-group-item, .card');
                
                if (text.includes(query) || query === '') {
                    parent.style.display = '';
                } else {
                    parent.style.display = 'none';
                }
            });
        });
    }
}

// دالة تفعيل الاختصارات
function initKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl + K للبحث السريع
        if (e.ctrlKey && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.getElementById('quickSearch');
            if (searchInput) {
                searchInput.focus();
            }
        }
        
        // Ctrl + R لتحديث البيانات
        if (e.ctrlKey && e.key === 'r') {
            e.preventDefault();
            updateDashboardData();
        }
        
        // Ctrl + P للطباعة
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            window.print();
        }
    });
}

// دالة تفعيل الوضع المظلم
function toggleDarkMode() {
    const body = document.body;
    const isDark = body.classList.toggle('dark-mode');
    
    // حفظ التفضيل
    localStorage.setItem('darkMode', isDark);
    
    // تحديث الرسوم البيانية للوضع المظلم
    if (isDark) {
        Chart.defaults.color = '#fff';
        Chart.defaults.borderColor = '#444';
        Chart.defaults.backgroundColor = '#333';
    } else {
        Chart.defaults.color = '#495057';
        Chart.defaults.borderColor = '#dee2e6';
        Chart.defaults.backgroundColor = '#fff';
    }
    
    // إعادة رسم جميع الرسوم البيانية
    Chart.instances.forEach(chart => {
        chart.update();
    });
}

// دالة تحميل تفضيلات المستخدم
function loadUserPreferences() {
    // تحميل الوضع المظلم
    const darkMode = localStorage.getItem('darkMode') === 'true';
    if (darkMode) {
        document.body.classList.add('dark-mode');
    }
    
    // تحميل تفضيلات أخرى
    const autoRefresh = localStorage.getItem('autoRefresh') !== 'false';
    if (autoRefresh) {
        // تفعيل التحديث التلقائي
        setInterval(updateDashboardData, 300000); // كل 5 دقائق
    }
}

// دالة تهيئة جميع التفاعلات
function initDashboardInteractions() {
    // تحميل التفضيلات
    loadUserPreferences();
    
    // تفعيل البحث السريع
    initQuickSearch();
    
    // تفعيل الاختصارات
    initKeyboardShortcuts();
    
    // إضافة التنبيهات الذكية
    setTimeout(addSmartNotifications, 2000);
    
    // تفعيل التولتيب
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // تفعيل البوبوفر
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

// تشغيل التفاعلات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', initDashboardInteractions);

// تصدير الدوال للاستخدام العام
window.DashboardInteractions = {
    exportChart,
    printChart,
    updateDashboardData,
    showNotification,
    toggleDarkMode,
    addSmartNotifications
};
