# Generated by Django 4.2.7 on 2025-06-08 00:33

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("patients", "0003_alter_patient_national_id"),
        ("departments", "0001_initial"),
        ("staff", "0003_salarystructure_payroll"),
    ]

    operations = [
        migrations.CreateModel(
            name="MedicalRecord",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("record_id", models.CharField(max_length=20, unique=True)),
                (
                    "record_type",
                    models.CharField(
                        choices=[
                            ("CONSULTATION", "استشارة"),
                            ("DIAGNOSIS", "تشخيص"),
                            ("TREATMENT", "علاج"),
                            ("SURGERY", "جراحة"),
                            ("EMERGENCY", "طوارئ"),
                            ("FOLLOW_UP", "متابعة"),
                            ("DISCHARGE", "خروج"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "priority",
                    models.Char<PERSON>ield(
                        choices=[
                            ("LOW", "منخفض"),
                            ("NORMAL", "عادي"),
                            ("HIGH", "عالي"),
                            ("URGENT", "عاجل"),
                            ("CRITICAL", "حرج"),
                        ],
                        default="NORMAL",
                        max_length=20,
                    ),
                ),
                ("chief_complaint", models.TextField()),
                ("history_of_present_illness", models.TextField()),
                ("past_medical_history", models.TextField(blank=True)),
                ("family_history", models.TextField(blank=True)),
                ("social_history", models.TextField(blank=True)),
                ("physical_examination", models.TextField()),
                ("assessment", models.TextField()),
                ("plan", models.TextField()),
                ("is_confidential", models.BooleanField(default=False)),
                ("is_completed", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "department",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="departments.department",
                    ),
                ),
                (
                    "doctor",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="staff.staff"
                    ),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="patients.patient",
                    ),
                ),
            ],
            options={
                "verbose_name": "سجل طبي",
                "verbose_name_plural": "السجلات الطبية",
                "ordering": ["-created_at"],
            },
        ),
    ]
