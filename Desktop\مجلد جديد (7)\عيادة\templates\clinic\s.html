{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% load static %}

{% block content %}

<section class="page-header py-5 bg-primary text-white">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="display-4 fw-bold"> اضافة وصفه طبية</h1>
                <p class="lead">نحن هنا للإجابة على استفساراتك وتقديم المساعدة</p>
            </div>
        </div>
    </div>
</section>
<section class="contact-form-section py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mb-4 mb-lg-0">
                <div class="card p-4">
                    <h3 class="mb-4"> بيانات الدواء</h3>
<form method="post">
    {% csrf_token %}
   {{form.patient}}
    <div class="row">
        <div class="col-md-6 mb-3">
    <div class="form-group">
         {{ form.name.label_tag }}
        {% comment %} <label>اسم العلاج:</label> {% endcomment %}
        {{ form.name }}
    </div>
    </div>
    <div class="col-md-6 mb-3">
    <div class="form-group">
        {{ form.dosage.label_tag }}
        {% comment %} <label>اسم العلاج:</label> {% endcomment %}
        {{ form.dosage }}
    </div>
    </div>
    <div class="col-md-6 mb-3">
    <div class="form-group">
        {{ form.frequency.label_tag }}
        {% comment %} <label>اسم العلاج:</label> {% endcomment %}
        {{ form.frequency }}
    </div>
    </div>
    <div class="col-md-6 mb-3">
    <div class="form-group">
        {{ form.start_date.label_tag }}
        {% comment %} <label>اسم العلاج:</label> {% endcomment %}
        {{ form.start_date }}
    </div>
    </div>
    <div class="col-md-6 mb-3">
    <div class="form-group">
        {{ form.end_date.label_tag }}
        {% comment %} <label>اسم العلاج:</label> {% endcomment %}
        {{ form.end_date }}
    </div>
    </div>

    <div class="form-group">
        {% comment %} <label>أوقات الاستخدام:</label> {% endcomment %}
        <div id="times-container">
            {% for value, label in form.times.field.choices %}
                <div class="time-item">
                    <label>
                        <input type="checkbox" name="times" value="{{ value }}">
                        {{ label }}
                    </label>
                    <input type="text"
                           name="notes_{{ value }}"
                           class="form-control notes-input"
                           placeholder="ملاحظات {{ label }}"
                           style="display: none;">
                </div>
            {% endfor %}
        </div>
    </div>

    <button type="submit" class="btn btn-primary">حفظ </button>
</form>
</div>
</div>
</div>
</div>
</div>
<script>
document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        const notesInput = this.closest('.time-item').querySelector('.notes-input');
        notesInput.style.display = this.checked ? 'block' : 'none';
    });
});
</script>
{% endblock %}









class TimeForm(forms.ModelForm):
    class Meta:
        model = TimeChoice
        fields = ['time', 'notes']
        widgets = {
            'time': forms.Select(attrs={'class': 'time-select form-control-lg'}),
            'notes': forms.TextInput(attrs={'class': 'time-notes form-control-lg', 'placeholder': 'ملاحظات'})
        }

class MedicationForm(forms.ModelForm):
    times = forms.MultipleChoiceField(
        choices=TimeChoice.TIME_CHOICES,
        widget=forms.CheckboxSelectMultiple,
        label='أوقات الاستخدام'
    )
    class Meta:
        model = Medication
        fields = ['name', 'dosage', 'frequency', 'start_date', 'end_date']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control form-control-lg'}),
            'dosage': forms.TextInput(attrs={'class': 'form-control form-control-lg'}),
            'frequency': forms.NumberInput(attrs={'class': 'form-control form-control-lg'}),
            # 'dosage_time': forms.TextInput(attrs={'class': 'form-control form-control-lg'}),
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-lg'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-lg'}),
            # 'start_date': forms.DateInput(attrs={'type': 'datetime-local'}),
            # 'end_date': forms.DateInput(attrs={'type': 'datetime-local'}),
        }
        labels = {
            'name': 'اسم الدواء',
            'dosage': 'جرعة',
            'frequency': 'تردد',
            'start_date': 'تاريخ البدء',
            'end_date': 'تاريخ الانتهاء',
        }
