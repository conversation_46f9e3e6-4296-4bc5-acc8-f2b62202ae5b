# Generated by Django 4.2.7 on 2025-06-27 23:26

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("clinic", "0009_medicalrecord"),
    ]

    operations = [
        migrations.CreateModel(
            name="Ray<PERSON>",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "rays_id",
                    models.CharField(
                        max_length=20, unique=True, verbose_name="رقم الاشعة"
                    ),
                ),
                (
                    "rays_name",
                    models.CharField(
                        choices=[
                            ("X-Ray", "أشعة سينية"),
                            ("CT Scan", "أشعة مقطعية"),
                            ("MRI", "رنين مغناطيسي"),
                            ("Ultrasound", "موجات فوق صوتية"),
                            ("Mammography", "تصوير الثدي"),
                            ("Nuclear Medicine", "طب نووي"),
                        ],
                        max_length=200,
                        verbose_name="اسم اشعة",
                    ),
                ),
                (
                    "part",
                    models.CharField(
                        max_length=100, verbose_name="الجزء المراد تصويرة"
                    ),
                ),
                (
                    "UseOfSeed",
                    models.CharField(max_length=20, verbose_name="استخدام الصبغة"),
                ),
                (
                    "linical",
                    models.TextField(blank=True, verbose_name="المؤشر السريري"),
                ),
                (
                    "instructions",
                    models.TextField(blank=True, verbose_name="تعليمات خاصة"),
                ),
                (
                    "ordered_date",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="تاريخ الطلب"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "doctor",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="الطبيب الطالب",
                    ),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="clinic.patient",
                        verbose_name="المريض",
                    ),
                ),
            ],
            options={
                "verbose_name": "الاشعة",
                "verbose_name_plural": "الاشعة",
                "ordering": ["-ordered_date"],
            },
        ),
    ]
