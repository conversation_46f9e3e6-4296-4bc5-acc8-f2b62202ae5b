{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }} - نظام العيادة الطبية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom" style="margin-top: 50px;">
    <h1 class="h2">
        <i class="bi bi-clipboard{% if diagnosis %}-gear{% else %}-plus{% endif %}"></i>
        {{ title }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'diagnosis_list' %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right"></i> العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clipboard-pulse"></i> بيانات التشخيص
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    {% crispy form %}
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i> إرشادات التشخيص
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="bi bi-lightbulb"></i> نصائح مهمة:</h6>
                    <ul class="mb-0">
                        <li>اكتب الأعراض بتفصيل واضح</li>
                        <li>حدد التشخيص بدقة</li>
                        <li>ضع خطة علاج شاملة</li>
                        <li>حدد موعد المتابعة إذا لزم الأمر</li>
                        <li>أضف أي ملاحظات مهمة</li>
                    </ul>
                </div>

                <div class="alert alert-warning">
                    <h6><i class="bi bi-exclamation-triangle"></i> تذكير:</h6>
                    <p class="mb-0">
                        التشخيص الطبي مسؤولية كبيرة. تأكد من دقة المعلومات
                        واتباع البروتوكولات الطبية المعتمدة.
                    </p>
                </div>
            </div>
        </div>

        {% if diagnosis %}
            <div class="card shadow mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history"></i> معلومات التشخيص
                    </h5>
                </div>
                <div class="card-body">
                    <p><strong>تاريخ التشخيص:</strong><br>
                       {{ diagnosis.diagnosis_date|date:"d/m/Y H:i" }}</p>
                    <p><strong>الطبيب المشخص:</strong><br>
                       {{ diagnosis.doctor.first_name }} {{ diagnosis.doctor.last_name }}</p>
                    {% if diagnosis.follow_up_date %}
                        <p><strong>موعد المتابعة:</strong><br>
                           {{ diagnosis.follow_up_date|date:"d/m/Y" }}</p>
                    {% endif %}
                </div>
            </div>
        {% endif %}

        <!-- Quick Actions -->
        <div class="card shadow mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning"></i> إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">

                        <a href="{% url 'prescription_create' %}?patient_id={{ diagnosis.patient.id }}"
                           class="btn btn-outline-success btn-sm">
                            <i class="bi bi-prescription2"></i> كتابة وصفة طبية
                        </a>
                        <a href="{% url 'appointment_create' %}?patient_id={{ diagnosis.patient.id }}"
                           class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-calendar-plus"></i> حجز موعد متابعة
                        </a>

                     <a href="{% url 'rays' %}" class="btn btn-light btn-sm">
                        <i class="bi bi-camera"></i> إضافة أشعة
                    </a>
                    <a href="{% url 'lab' %}" class="btn btn-light btn-sm">
                        <i class="bi bi-clipboard-data"></i> إضافة فحص مخبري
                    </a>
                    <a href="{% url 'payment_create' %}" class="btn btn-light btn-sm">
                        <i class="bi bi-credit-card"></i> تسجيل دفعة
                    </a>

                </div>

        </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-expand textareas
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(textarea => {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    });

    // Validate follow-up date
    const followUpInput = document.querySelector('input[name="follow_up_date"]');
    if (followUpInput) {
        const today = new Date().toISOString().split('T')[0];
        followUpInput.min = today;

        followUpInput.addEventListener('change', function(e) {
            const selectedDate = new Date(e.target.value);
            const currentDate = new Date();

            if (selectedDate < currentDate) {
                alert('تاريخ المتابعة يجب أن يكون في المستقبل');
                e.target.focus();
            }
        });
    }

    // Character counter for text fields
    const textFields = document.querySelectorAll('textarea[name="symptoms"], textarea[name="diagnosis"], textarea[name="treatment_plan"]');
    textFields.forEach(field => {
        const maxLength = 1000;
        const counter = document.createElement('small');
        counter.className = 'text-muted';
        field.parentNode.appendChild(counter);

        function updateCounter() {
            const remaining = maxLength - field.value.length;
            counter.textContent = `${field.value.length}/${maxLength} حرف`;

            if (remaining < 50) {
                counter.className = 'text-warning';
            } else if (remaining < 0) {
                counter.className = 'text-danger';
            } else {
                counter.className = 'text-muted';
            }
        }

        field.addEventListener('input', updateCounter);
        updateCounter();
    });
});
</script>
{% endblock %}
