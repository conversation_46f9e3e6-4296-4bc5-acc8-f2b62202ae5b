{% extends 'base.html' %}

{% block title %}تفاصيل الفحص الطبية - نظام العيادة الطبية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap align-items-start pt-3 pb-2 mb-3 border-bottom" style="margin-top: 50px;">
    <h1 class="h2 mb-2 mb-md-0">
        <i class="bi bi-prescription2"></i>
        تفاصيل الفحص الطبية
    </h1>
    <div class="btn-toolbar">

        {% if not prescription.results %}
                <a href="{% url 'lab_test_result_create' prescription.pk %}" class="btn btn-primaryn btn-sm">
                    <i class="bi bi-clipboard-data"></i>
                    <span class="d-none d-lg-inline">إضافة النتائج</span>
                </a>
            {% else %}
                <a href="{% url 'lab_test_result_update' prescription.pk %}" class="btn btn-warning btn-sm">
                    <i class="bi bi-pencil-square"></i>
                    <span class="d-none d-lg-inline">تحديث النتائج</span>
                </a>
            {% endif %}

        <div class="btn-group me-2 mb-2 mb-md-0">
            <a href="{% url 'lab_update' prescription.pk %}" class="btn btn-warning btn-sm">
                <i class="bi bi-pencil"></i>
                <span class="d-none d-lg-inline">تعديل الطلب</span>
            </a>
            <button onclick="printPrescription()" class="btn btn-success btn-sm">
                <i class="bi bi-printer"></i>
                <span class="d-none d-lg-inline">طباعة</span>
            </button>
            <a href="{% url 'lab_delete' prescription.pk %}" class="btn btn-danger btn-sm">
                <i class="bi bi-trash"></i>
                <span class="d-none d-lg-inline">حذف</span>
            </a>
        </div>
        <a href="{% url 'record_list' %}" class="btn btn-outline-secondary btn-sm">
            <i class="bi bi-arrow-right"></i>
            <span class="d-none d-sm-inline">العودة للقائمة</span>
            <span class="d-inline d-sm-none">عودة</span>
        </a>
    </div>

</div>

<div class="row">
    <!-- Prescription Details -->
    <div class="col-lg-8 mb-4">
        <!-- Prescription Header -->
        <div class="card shadow mb-4" id="prescription-content">
            <div class="card-header bg-primary text-white">
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-hospital"></i> نظام العيادة الطبية
                        </h5>
                        <small>فحص طبية</small>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <p class="mb-0"><strong>التاريخ:</strong> {{ prescription.ordered_date|date:"d/m/Y" }}</p>
                        <p class="mb-0"><strong>الوقت:</strong> {{ prescription.ordered_date|time:"H:i" }}</p>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Patient Information -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-primary">معلومات المريض:</h6>
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td><strong>الاسم:</strong></td>
                                <td>{{ prescription.patient.full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>العمر:</strong></td>
                                <td>{{ prescription.patient.age }} سنة</td>
                            </tr>
                            <tr>
                                <td><strong>الجنس:</strong></td>
                                <td>{{ prescription.patient.get_gender_display }}</td>
                            </tr>
                            <tr>
                                <td><strong>الهاتف:</strong></td>
                                <td>{{ prescription.patient.phone }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">معلومات الطبيب:</h6>
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td><strong>الطبيب:</strong></td>
                                <td>{{ prescription.doctor.first_name }} {{ prescription.doctor.last_name }}</td>
                            </tr>
                            {% if prescription.diagnosis %}
                            <tr>
                                <td><strong>التشخيص:</strong></td>
                                <td>
                                    <a href="{% url 'diagnosis_detail' prescription.diagnosis.pk %}" class="text-decoration-none">
                                        {{ prescription.diagnosis.diagnosis|truncatechars:50 }}
                                    </a>
                                </td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>

                <!-- Medications -->
                <div class="mb-4">
                    <h6 class="text-primary border-bottom pb-2">
                        <i class="bi bi-capsule"></i> الفحص:
                    </h6>
                    <div class="bg-light p-3 rounded">
                        <pre class="mb-0" style="white-space: pre-wrap; font-family: inherit;">{{ prescription.test_name }}</pre>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="mb-4">
                    <h6 class="text-primary border-bottom pb-2">
                        <i class="bi bi-info-circle"></i> تعليمات الفحص:
                    </h6>
                    <div class="bg-light p-3 rounded">
                        <pre class="mb-0" style="white-space: pre-wrap; font-family: inherit;">{{ prescription.instructions }}</pre>
                    </div>
                </div>

                <!-- Lab Test Results -->
                {% if prescription.results %}
                <div class="mb-4">
                    <h6 class="text-success border-bottom pb-2">
                        <i class="bi bi-clipboard-data"></i> نتائج الفحص:
                        {% if prescription.is_abnormal %}
                            <span class="badge bg-danger ms-2">غير طبيعي</span>
                        {% else %}
                            <span class="badge bg-success ms-2">طبيعي</span>
                        {% endif %}
                    </h6>

                    <!-- Test Results -->
                    <div class="bg-{% if prescription.is_abnormal %}danger{% else %}success{% endif %} bg-opacity-10 p-3 rounded mb-3">
                        <h6 class="text-{% if prescription.is_abnormal %}danger{% else %}success{% endif %}">
                            <i class="bi bi-file-text"></i> النتائج:
                        </h6>
                        <pre class="mb-0" style="white-space: pre-wrap; font-family: inherit;">{{ prescription.results }}</pre>
                    </div>

                    <!-- Normal Range -->
                    {% if prescription.normal_range %}
                    <div class="bg-info bg-opacity-10 p-3 rounded mb-3">
                        <h6 class="text-info">
                            <i class="bi bi-bar-chart"></i> المعدل الطبيعي:
                        </h6>
                        <p class="mb-0">{{ prescription.normal_range }}</p>
                    </div>
                    {% endif %}

                    <!-- Test Dates and Technician -->
                    <div class="row">
                        {% if prescription.sample_collected_date %}
                        <div class="col-md-4">
                            <h6 class="text-primary">تاريخ أخذ العينة:</h6>
                            <p class="bg-light p-2 rounded">{{ prescription.sample_collected_date|date:"d/m/Y H:i" }}</p>
                        </div>
                        {% endif %}

                        {% if prescription.result_date %}
                        <div class="col-md-4">
                            <h6 class="text-primary">تاريخ النتيجة:</h6>
                            <p class="bg-light p-2 rounded">{{ prescription.result_date|date:"d/m/Y H:i" }}</p>
                        </div>
                        {% endif %}

                        {% if prescription.technician %}
                        <div class="col-md-4">
                            <h6 class="text-primary">فني المختبر:</h6>
                            <p class="bg-light p-2 rounded">{{ prescription.technician.first_name }} {{ prescription.technician.last_name }}</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% else %}
                <div class="mb-4">
                    <div class="alert alert-warning" role="alert">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>لم يتم إدخال النتائج بعد</strong>
                        <br>
                        <small>يرجى إضافة نتائج الفحص عند توفرها</small>
                    </div>
                </div>
                {% endif %}

                <!-- Duration and Notes -->
                <div class="row">
<!--                    <div class="col-md-6">-->
<!--                        <h6 class="text-primary">مدة العلاج:</h6>-->
<!--                        <p class="bg-light p-2 rounded">{{ prescription.duration }}</p>-->
<!--                    </div>-->
                    {% if prescription.notes %}
                    <div class="col-md-6">
                        <h6 class="text-primary">ملاحظات إضافية:</h6>
                        <p class="bg-light p-2 rounded">{{ prescription.notes }}</p>
                    </div>
                    {% endif %}
                </div>

                <!-- Footer -->
                <div class="text-center mt-4 pt-3 border-top">
                    <small class="text-muted">
                        هذه الفحص صادرة من نظام العيادة الطبية - {{ prescription.ordered_date|date:"d/m/Y H:i" }}
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Prescription Info -->
        <div class="card shadow mb-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i> معلومات الفحص
                </h5>
            </div>
            <div class="card-body">
                <p><strong>تاريخ الإنشاء:</strong><br>
                   {{ prescription.ordered_date|date:"d/m/Y H:i" }}</p>
                <p><strong>الطبيب:</strong><br>
                   {{ prescription.doctor.first_name }} {{ prescription.doctor.last_name }}</p>
                <p><strong>المريض:</strong><br>
                   <a href="{% url 'patient_detail' prescription.patient.pk %}" class="text-decoration-none">
                       {{ prescription.patient.full_name }}
                   </a>
                </p>
                {% if prescription.diagnosis %}
                    <p><strong>التشخيص المرتبط:</strong><br>
                       <a href="{% url 'diagnosis_detail' prescription.diagnosis.pk %}" class="text-decoration-none">
                           {{ prescription.diagnosis.diagnosis|truncatechars:30 }}
                       </a>
                    </p>
                {% endif %}
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card shadow mb-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning"></i> إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'appointment_create' %}?patient_id={{ prescription.patient.id }}"
                       class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-calendar-plus"></i> حجز موعد متابعة
                    </a>
                    <a href="{% url 'diagnosis_create' %}?patient_id={{ prescription.patient.id }}"
                       class="btn btn-outline-success btn-sm">
                        <i class="bi bi-clipboard-plus"></i> إضافة تشخيص جديد
                    </a>
                    <a href="{% url 'prescription_create' %}?patient_id={{ prescription.patient.id }}"
                       class="btn btn-outline-warning btn-sm">
                        <i class="bi bi-prescription2"></i> وصفة جديدة
                    </a>
                </div>
            </div>
        </div>

        <!-- Patient Allergies Warning -->
        {% if prescription.patient.allergies %}
        <div class="card shadow border-warning">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="bi bi-exclamation-triangle"></i> تحذير - حساسيات المريض
                </h5>
            </div>
            <div class="card-body">
                <p class="text-danger mb-0">{{ prescription.patient.allergies }}</p>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function printPrescription() {
    // Hide non-printable elements
    const sidebar = document.querySelector('.col-lg-4');
    const toolbar = document.querySelector('.btn-toolbar');
    const borderBottom = document.querySelector('.border-bottom');

    if (sidebar) sidebar.style.display = 'none';
    if (toolbar) toolbar.style.display = 'none';
    if (borderBottom) borderBottom.style.display = 'none';

    // Print
    window.print();

    // Restore elements
    if (sidebar) sidebar.style.display = 'block';
    if (toolbar) toolbar.style.display = 'flex';
    if (borderBottom) borderBottom.style.display = 'flex';
}

// Print styles
const printStyles = `
    @media print {
        .btn-toolbar, .col-lg-4, .border-bottom { display: none !important; }
        .col-lg-8 { width: 100% !important; }
        .card { border: 1px solid #000 !important; box-shadow: none !important; }
        .card-header { background-color: #f8f9fa !important; color: #000 !important; }
        body { font-size: 12pt; }
        .h2, .h5, .h6 { font-size: 14pt; }
        pre { font-size: 11pt; }
    }
`;

// Add print styles to head
const styleSheet = document.createElement('style');
styleSheet.textContent = printStyles;
document.head.appendChild(styleSheet);
</script>
{% endblock %}
