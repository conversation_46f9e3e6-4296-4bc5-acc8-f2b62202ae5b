# تعليمات تحويل نظام العيادة الطبية إلى برنامج EXE

## المتطلبات الأساسية

1. **Python 3.8 أو أحدث**
   - تحميل من: https://www.python.org/downloads/
   - تأكد من إضافة Python إلى PATH أثناء التثبيت

2. **pip (مدير الحزم)**
   - يأتي مع Python تلقائياً

## خطوات البناء

### الطريقة الأولى: استخدام ملف Batch (الأسهل)

1. افتح Command Prompt كمدير
2. انتقل إلى مجلد المشروع:
   ```cmd
   cd "Desktop\مجلد جديد (7)\schoole"
   ```
3. شغل ملف البناء:
   ```cmd
   build_exe.bat
   ```

### الطريقة الثانية: البناء اليدوي

1. **تثبيت المكتبات:**
   ```cmd
   pip install -r requirements.txt
   ```

2. **إعداد قاعدة البيانات:**
   ```cmd
   python manage.py makemigrations
   python manage.py migrate
   ```

3. **بناء البرنامج:**
   ```cmd
   pyinstaller clinic_system.spec --clean --noconfirm
   ```

## النتيجة

- سيتم إنشاء مجلد `dist` يحتوي على `ClinicSystem.exe`
- حجم الملف سيكون حوالي 100-200 ميجابايت
- البرنامج مستقل ولا يحتاج Python مثبت على الجهاز المستهدف

## تشغيل البرنامج

1. انقر مرتين على `ClinicSystem.exe`
2. انتظر حتى يظهر نافذة الكونسول
3. سيفتح المتصفح تلقائياً على العنوان: http://127.0.0.1:8000
4. استخدم بيانات المدير:
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`

## حل المشاكل الشائعة

### مشكلة: "Python is not recognized"
**الحل:** تأكد من تثبيت Python وإضافته إلى PATH

### مشكلة: "pip is not recognized"
**الحل:** 
```cmd
python -m pip install -r requirements.txt
```

### مشكلة: "ModuleNotFoundError"
**الحل:** تأكد من تثبيت جميع المكتبات:
```cmd
pip install Django pyinstaller django-crispy-forms crispy-bootstrap5
```

### مشكلة: البرنامج لا يعمل على جهاز آخر
**الحل:** تأكد من:
- نسخ مجلد `dist` كاملاً
- تشغيل البرنامج كمدير إذا لزم الأمر
- التأكد من عدم وجود برامج مكافحة فيروسات تحجب البرنامج

## ملاحظات مهمة

1. **الأمان:** البرنامج يستخدم قاعدة بيانات SQLite محلية
2. **النسخ الاحتياطي:** انسخ ملف `db.sqlite3` دورياً
3. **التحديثات:** لتحديث البرنامج، أعد بناءه من الكود المحدث
4. **الشبكة:** البرنامج يعمل محلياً فقط (localhost)

## توزيع البرنامج

لتوزيع البرنامج على أجهزة أخرى:

1. انسخ مجلد `dist` كاملاً
2. أو استخدم برنامج ضغط لإنشاء ملف ZIP
3. أرفق ملف التعليمات للمستخدمين

## تخصيص البرنامج

لتخصيص البرنامج:

1. **تغيير الأيقونة:** أضف ملف `.ico` وعدل `clinic_system.spec`
2. **تغيير اسم البرنامج:** عدل `name='ClinicSystem'` في الملف spec
3. **إضافة ملفات:** أضف المسارات في قسم `datas`

## الدعم الفني

في حالة وجود مشاكل:
1. تحقق من ملف `error.log` إن وجد
2. شغل البرنامج من Command Prompt لرؤية الأخطاء
3. تأكد من صحة ملفات المشروع
