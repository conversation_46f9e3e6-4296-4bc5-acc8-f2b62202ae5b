/* تنسيقات محسنة للرسوم البيانية */

/* حاوي الرسم البياني */
.chart-container {
    position: relative;
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-bottom: 20px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.chart-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

/* عنوان الرسم البياني */
.chart-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.chart-title i {
    color: #667eea;
    font-size: 1.1rem;
}

/* منطقة الرسم البياني */
.chart-wrapper {
    position: relative;
    height: 300px;
    margin-bottom: 15px;
}

.chart-wrapper.small {
    height: 200px;
}

.chart-wrapper.large {
    height: 400px;
}

/* تحميل الرسم البياني */
.chart-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    color: #6c757d;
}

.chart-loading .spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* إحصائيات الرسم البياني */
.chart-stats {
    display: flex;
    justify-content: space-around;
    padding: 15px 0;
    border-top: 1px solid #e9ecef;
    margin-top: 15px;
}

.chart-stat {
    text-align: center;
    flex: 1;
}

.chart-stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
}

.chart-stat-label {
    font-size: 0.85rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* أزرار التحكم في الرسم البياني */
.chart-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.chart-control-btn {
    padding: 6px 12px;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 6px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #495057;
}

.chart-control-btn:hover {
    background: #f8f9fa;
    border-color: #667eea;
    color: #667eea;
}

.chart-control-btn.active {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

/* مؤشرات الرسم البياني */
.chart-indicators {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.chart-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #495057;
}

.chart-indicator-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

/* رسوم بيانية متجاوبة */
@media (max-width: 768px) {
    .chart-container {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .chart-wrapper {
        height: 250px;
    }
    
    .chart-title {
        font-size: 1.1rem;
    }
    
    .chart-stats {
        flex-direction: column;
        gap: 10px;
    }
    
    .chart-stat {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f1f3f4;
    }
    
    .chart-stat:last-child {
        border-bottom: none;
    }
    
    .chart-controls {
        justify-content: center;
    }
}

/* تأثيرات خاصة للرسوم البيانية */
.chart-container.revenue {
    background: linear-gradient(135deg, #667eea10 0%, #764ba210 100%);
    border-left: 4px solid #667eea;
}

.chart-container.financial {
    background: linear-gradient(135deg, #28a74510 0%, #20c99710 100%);
    border-left: 4px solid #28a745;
}

.chart-container.emergency {
    background: linear-gradient(135deg, #dc354510 0%, #c8233310 100%);
    border-left: 4px solid #dc3545;
}

.chart-container.pharmacy {
    background: linear-gradient(135deg, #ffc10710 0%, #e0a80010 100%);
    border-left: 4px solid #ffc107;
}

/* تنسيق خاص للرسوم الدائرية */
.doughnut-chart-container {
    position: relative;
}

.doughnut-center-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    pointer-events: none;
}

.doughnut-center-value {
    font-size: 2rem;
    font-weight: bold;
    color: #2c3e50;
    line-height: 1;
}

.doughnut-center-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 5px;
}

/* تأثيرات الانيميشن */
.chart-container.animate-in {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تنسيق الأزرار المخصصة */
.chart-export-btn {
    position: absolute;
    top: 15px;
    left: 15px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 6px 10px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.chart-export-btn:hover {
    background: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* تنسيق التولتيب المخصص */
.custom-tooltip {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    font-size: 0.9rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    backdrop-filter: blur(10px);
}

/* تنسيق الشبكة للرسوم البيانية */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.charts-grid.two-columns {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
}

.charts-grid.three-columns {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* تنسيق خاص للطباعة */
@media print {
    .chart-container {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .chart-controls,
    .chart-export-btn {
        display: none;
    }
}
