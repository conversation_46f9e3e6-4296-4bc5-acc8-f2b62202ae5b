{% extends 'base.html' %}

{% block title %}حذف الدفعة - نظام العيادة الطبية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom" style="margin-top: 50px;">
    <h1 class="h2">
        <i class="bi bi-credit-card-2-back text-danger"></i>
        حذف الدفعة
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'payment_detail' payment.pk %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right"></i> العودة
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-exclamation-triangle"></i> تأكيد حذف الدفعة
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <h6><i class="bi bi-exclamation-triangle"></i> تحذير مهم:</h6>
                    <p class="mb-0">
                        هذا الإجراء لا يمكن التراجع عنه. سيتم حذف الدفعة نهائياً من النظام وقد يؤثر على التقارير المالية.
                    </p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6>تفاصيل الدفعة المراد حذفها:</h6>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>رقم الإيصال:</strong></td>
                                <td>{{ payment.receipt_number }}</td>
                            </tr>
                            <tr>
                                <td><strong>المريض:</strong></td>
                                <td>{{ payment.patient.full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>المبلغ:</strong></td>
                                <td class="text-success fw-bold">{{ payment.amount }} ريال</td>
                            </tr>
                            <tr>
                                <td><strong>طريقة الدفع:</strong></td>
                                <td>{{ payment.get_payment_method_display }}</td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    {% if payment.status == 'paid' %}
                                        <span class="badge bg-success">{{ payment.get_status_display }}</span>
                                    {% elif payment.status == 'pending' %}
                                        <span class="badge bg-warning">{{ payment.get_status_display }}</span>
                                    {% elif payment.status == 'cancelled' %}
                                        <span class="badge bg-danger">{{ payment.get_status_display }}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ payment.get_status_display }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الدفع:</strong></td>
                                <td>{{ payment.payment_date|date:"d/m/Y H:i" }}</td>
                            </tr>
                        </table>
                    </div>

                    <div class="col-md-6">
                        <h6>وصف الخدمة:</h6>
                        <div class="bg-light p-3 rounded mb-3">
                            {{ payment.description }}
                        </div>

                        {% if payment.notes %}
                        <h6>ملاحظات:</h6>
                        <div class="bg-light p-3 rounded">
                            {{ payment.notes }}
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Warning for paid payments -->
                {% if payment.status == 'paid' %}
                <div class="alert alert-warning mt-3">
                    <h6><i class="bi bi-exclamation-triangle"></i> تحذير إضافي:</h6>
                    <p class="mb-0">
                        هذه الدفعة مكتملة ومدفوعة. حذفها قد يؤثر على:
                    </p>
                    <ul class="mb-0 mt-2">
                        <li>التقارير المالية الشهرية</li>
                        <li>حسابات المريض</li>
                        <li>سجلات المراجعة</li>
                    </ul>
                </div>
                {% endif %}

                <hr>

                <div class="text-center">
                    <p class="text-muted mb-4">
                        هل أنت متأكد من رغبتك في حذف هذه الدفعة؟
                    </p>

                    <form method="post" class="d-inline">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-danger me-3">
                            <i class="bi bi-trash"></i> نعم، احذف الدفعة
                        </button>
                    </form>

                    <a href="{% url 'payment_detail' payment.pk %}" class="btn btn-secondary">
                        <i class="bi bi-x-circle"></i> إلغاء
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add confirmation dialog
    const deleteForm = document.querySelector('form');
    if (deleteForm) {
        deleteForm.addEventListener('submit', function(e) {
            const paymentAmount = '{{ payment.amount }}';
            const paymentStatus = '{{ payment.status }}';

            let confirmMessage = 'هل أنت متأكد من رغبتك في حذف هذه الدفعة؟ هذا الإجراء لا يمكن التراجع عنه.';

            if (paymentStatus === 'paid') {
                confirmMessage += '\n\nتحذير: هذه الدفعة مكتملة وقد يؤثر حذفها على التقارير المالية.';
            }

            if (!confirm(confirmMessage)) {
                e.preventDefault();
            }
        });
    }

    // Highlight important information
    const amountCell = document.querySelector('.text-success.fw-bold');
    if (amountCell) {
        amountCell.style.fontSize = '1.2em';
    }
});
</script>
{% endblock %}
