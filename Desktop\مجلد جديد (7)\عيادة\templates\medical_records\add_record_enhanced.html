{% extends 'control_panels/base_dashboard.html' %}
{% load static %}

{% block title %}إضافة سجل طبي جديد{% endblock %}

{% block extra_css %}
<style>
    .medical-record-container {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .record-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-bottom: 30px;
        overflow: hidden;
    }
    
    .record-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 30px;
        text-align: center;
    }
    
    .record-title {
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 10px;
    }
    
    .form-section {
        background: white;
        border-radius: 10px;
        padding: 25px;
        margin: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    
    .section-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #28a745;
        display: flex;
        align-items: center;
    }
    
    .section-title i {
        margin-right: 10px;
        color: #28a745;
    }
    
    .form-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 12px 15px;
        transition: all 0.3s ease;
        font-size: 1rem;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
    
    .form-label {
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
        display: block;
    }
    
    .patient-search-container {
        position: relative;
    }
    
    .search-results {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ced4da;
        border-top: none;
        border-radius: 0 0 8px 8px;
        max-height: 250px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .search-result-item {
        padding: 12px 15px;
        border-bottom: 1px solid #f8f9fa;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }
    
    .search-result-item:hover {
        background-color: #f8f9fa;
    }
    
    .search-result-item:last-child {
        border-bottom: none;
    }
    
    .patient-name {
        font-weight: 600;
        color: #333;
        margin-bottom: 3px;
    }
    
    .patient-details {
        font-size: 0.9rem;
        color: #666;
    }
    
    .selected-patient {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        margin-top: 10px;
        transition: all 0.3s ease;
    }
    
    .selected-patient.has-patient {
        background: #d4edda;
        border-color: #28a745;
    }
    
    .priority-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .priority-normal {
        background: #e9ecef;
        color: #495057;
    }
    
    .priority-high {
        background: #fff3cd;
        color: #856404;
    }
    
    .priority-urgent {
        background: #f8d7da;
        color: #721c24;
    }
    
    .priority-critical {
        background: #dc3545;
        color: white;
    }
    
    .form-actions {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 25px;
        margin: 20px;
        text-align: center;
        border: 2px dashed #28a745;
    }
    
    .btn-primary-action {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin: 0 10px;
    }
    
    .btn-primary-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(40, 167, 69, 0.3);
        color: white;
    }
    
    .btn-secondary-action {
        background: #6c757d;
        color: white;
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin: 0 10px;
    }
    
    .btn-draft {
        background: #ffc107;
        color: #212529;
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin: 0 10px;
    }
    
    .form-check-custom {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        transition: all 0.3s ease;
    }
    
    .form-check-custom:hover {
        border-color: #28a745;
    }
    
    .form-check-input:checked {
        background-color: #28a745;
        border-color: #28a745;
    }
    
    .loading-spinner {
        display: none;
        text-align: center;
        padding: 20px;
    }
    
    .spinner-border {
        color: #28a745;
    }
    
    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }
        
        .form-actions .btn {
            display: block;
            width: 100%;
            margin: 10px 0;
        }
        
        .search-results {
            position: relative;
            border: 1px solid #ced4da;
            border-radius: 8px;
            margin-top: 10px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="medical-record-container">
    <div class="container-fluid">
        <!-- رأس الصفحة -->
        <div class="record-card">
            <div class="record-header">
                <h1 class="record-title">
                    <i class="fas fa-file-medical-alt me-3"></i>
                    إضافة سجل طبي جديد
                </h1>
                <p>إنشاء سجل طبي شامل للمريض</p>
            </div>
        </div>
        
        <form method="post" id="medicalRecordForm">
            {% csrf_token %}
            
            <!-- اختيار المريض -->
            <div class="form-section">
                <h4 class="section-title">
                    <i class="fas fa-user"></i>
                    اختيار المريض
                </h4>
                
                <div class="form-row">
                    <div class="patient-search-container">
                        <label class="form-label">البحث عن المريض *</label>
                        <input type="text" class="form-control" id="patient_search" 
                               placeholder="ابحث بالاسم، رقم المريض، أو رقم الهوية..." autocomplete="off">
                        <input type="hidden" id="selected_patient_id" name="patient_id" required>
                        <div id="patient_results" class="search-results"></div>
                        <div class="loading-spinner" id="searchLoading">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">جاري البحث...</span>
                            </div>
                            <span class="ms-2">جاري البحث...</span>
                        </div>
                    </div>
                </div>
                
                <div class="selected-patient" id="selected_patient_info">
                    <p class="text-muted mb-0">لم يتم اختيار مريض بعد</p>
                </div>
            </div>
            
            <!-- تفاصيل السجل -->
            <div class="form-section">
                <h4 class="section-title">
                    <i class="fas fa-clipboard-list"></i>
                    تفاصيل السجل
                </h4>
                
                <div class="form-row">
                    <div>
                        <label class="form-label">نوع السجل *</label>
                        <select class="form-select" name="record_type" required>
                            <option value="">اختر نوع السجل</option>
                            <option value="CONSULTATION">استشارة</option>
                            <option value="DIAGNOSIS">تشخيص</option>
                            <option value="TREATMENT">علاج</option>
                            <option value="SURGERY">جراحة</option>
                            <option value="EMERGENCY">طوارئ</option>
                            <option value="FOLLOW_UP">متابعة</option>
                            <option value="DISCHARGE">خروج</option>
                        </select>
                    </div>
                    <div>
                        <label class="form-label">الأولوية *</label>
                        <select class="form-select" name="priority" required>
                            <option value="NORMAL">عادي</option>
                            <option value="LOW">منخفض</option>
                            <option value="HIGH">عالي</option>
                            <option value="URGENT">عاجل</option>
                            <option value="CRITICAL">حرج</option>
                        </select>
                    </div>
                    <div>
                        <label class="form-label">القسم *</label>
                        <select class="form-select" name="department_id" required>
                            <option value="">اختر القسم</option>
                            {% for dept in departments %}
                                <option value="{{ dept.id }}">{{ dept.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- المحتوى الطبي -->
            <div class="form-section">
                <h4 class="section-title">
                    <i class="fas fa-stethoscope"></i>
                    المحتوى الطبي
                </h4>
                
                <div class="form-row">
                    <div>
                        <label class="form-label">الشكوى الرئيسية *</label>
                        <textarea class="form-control" name="chief_complaint" rows="3" required 
                                  placeholder="اذكر الشكوى الرئيسية للمريض"></textarea>
                    </div>
                    <div>
                        <label class="form-label">تاريخ المرض الحالي *</label>
                        <textarea class="form-control" name="history_of_present_illness" rows="3" required 
                                  placeholder="تفاصيل تاريخ المرض الحالي"></textarea>
                    </div>
                </div>
                
                <div class="form-row">
                    <div>
                        <label class="form-label">التاريخ الطبي السابق</label>
                        <textarea class="form-control" name="past_medical_history" rows="3" 
                                  placeholder="التاريخ الطبي السابق للمريض"></textarea>
                    </div>
                    <div>
                        <label class="form-label">التاريخ العائلي</label>
                        <textarea class="form-control" name="family_history" rows="3" 
                                  placeholder="التاريخ المرضي للعائلة"></textarea>
                    </div>
                </div>
                
                <div class="form-row">
                    <div>
                        <label class="form-label">التاريخ الاجتماعي</label>
                        <textarea class="form-control" name="social_history" rows="2" 
                                  placeholder="التدخين، الكحول، المهنة، إلخ"></textarea>
                    </div>
                </div>
            </div>
            
            <!-- الفحص السريري -->
            <div class="form-section">
                <h4 class="section-title">
                    <i class="fas fa-search"></i>
                    الفحص السريري
                </h4>
                
                <div class="form-row">
                    <div>
                        <label class="form-label">الفحص السريري *</label>
                        <textarea class="form-control" name="physical_examination" rows="4" required 
                                  placeholder="نتائج الفحص السريري"></textarea>
                    </div>
                </div>
            </div>
            
            <!-- التقييم والخطة -->
            <div class="form-section">
                <h4 class="section-title">
                    <i class="fas fa-clipboard-check"></i>
                    التقييم والخطة
                </h4>
                
                <div class="form-row">
                    <div>
                        <label class="form-label">التقييم *</label>
                        <textarea class="form-control" name="assessment" rows="4" required 
                                  placeholder="التقييم الطبي"></textarea>
                    </div>
                    <div>
                        <label class="form-label">الخطة العلاجية *</label>
                        <textarea class="form-control" name="plan" rows="4" required 
                                  placeholder="الخطة العلاجية المقترحة"></textarea>
                    </div>
                </div>
            </div>
            
            <!-- خيارات إضافية -->
            <div class="form-section">
                <h4 class="section-title">
                    <i class="fas fa-cog"></i>
                    خيارات إضافية
                </h4>
                
                <div class="form-row">
                    <div class="form-check-custom">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_confidential" name="is_confidential">
                            <label class="form-check-label" for="is_confidential">
                                <i class="fas fa-lock me-2"></i>
                                سجل سري (محدود الوصول)
                            </label>
                        </div>
                    </div>
                    <div class="form-check-custom">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_completed" name="is_completed">
                            <label class="form-check-label" for="is_completed">
                                <i class="fas fa-check-circle me-2"></i>
                                السجل مكتمل (لا يمكن تعديله)
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- أزرار الإجراءات -->
            <div class="form-actions">
                <button type="submit" class="btn-primary-action">
                    <i class="fas fa-save me-2"></i>
                    حفظ السجل الطبي
                </button>
                <button type="button" class="btn-draft" onclick="saveDraft()">
                    <i class="fas fa-file-alt me-2"></i>
                    حفظ كمسودة
                </button>
                <a href="{% url 'medical_records:record_list' %}" class="btn-secondary-action">
                    <i class="fas fa-times me-2"></i>
                    إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let searchTimeout;

document.getElementById('patient_search').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    const query = this.value.trim();
    
    if (query.length < 2) {
        hideSearchResults();
        return;
    }
    
    showLoading();
    searchTimeout = setTimeout(() => {
        searchPatients(query);
    }, 300);
});

function showLoading() {
    document.getElementById('searchLoading').style.display = 'block';
    document.getElementById('patient_results').style.display = 'none';
}

function hideLoading() {
    document.getElementById('searchLoading').style.display = 'none';
}

function searchPatients(query) {
    fetch(`/patients/search-ajax/?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            hideLoading();
            displaySearchResults(data.patients);
        })
        .catch(error => {
            hideLoading();
            console.error('خطأ في البحث:', error);
            document.getElementById('patient_results').innerHTML = '<div class="search-result-item text-danger">حدث خطأ في البحث</div>';
            document.getElementById('patient_results').style.display = 'block';
        });
}

function displaySearchResults(patients) {
    const resultsDiv = document.getElementById('patient_results');
    
    if (patients.length === 0) {
        resultsDiv.innerHTML = '<div class="search-result-item text-muted">لا توجد نتائج</div>';
    } else {
        resultsDiv.innerHTML = patients.map(patient => `
            <div class="search-result-item" onclick="selectPatient(${patient.id}, '${patient.full_name}', '${patient.patient_id}', '${patient.age}', '${patient.gender}')">
                <div class="patient-name">${patient.full_name}</div>
                <div class="patient-details">رقم المريض: ${patient.patient_id} | العمر: ${patient.age} | الجنس: ${patient.gender}</div>
            </div>
        `).join('');
    }
    
    resultsDiv.style.display = 'block';
}

function selectPatient(id, name, patientId, age, gender) {
    document.getElementById('selected_patient_id').value = id;
    document.getElementById('patient_search').value = name;
    
    const selectedDiv = document.getElementById('selected_patient_info');
    selectedDiv.innerHTML = `
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <div class="patient-name">${name}</div>
                <div class="patient-details">رقم المريض: ${patientId} | العمر: ${age} | الجنس: ${gender}</div>
            </div>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearPatientSelection()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    selectedDiv.classList.add('has-patient');
    
    hideSearchResults();
}

function clearPatientSelection() {
    document.getElementById('selected_patient_id').value = '';
    document.getElementById('patient_search').value = '';
    document.getElementById('selected_patient_info').classList.remove('has-patient');
    document.getElementById('selected_patient_info').innerHTML = '<p class="text-muted mb-0">لم يتم اختيار مريض بعد</p>';
    document.getElementById('patient_search').focus();
}

function hideSearchResults() {
    document.getElementById('patient_results').style.display = 'none';
}

// إخفاء النتائج عند النقر خارجها
document.addEventListener('click', function(event) {
    if (!event.target.closest('.patient-search-container')) {
        hideSearchResults();
    }
});

function saveDraft() {
    const form = document.getElementById('medicalRecordForm');
    const formData = new FormData(form);
    formData.append('is_draft', 'true');
    
    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم حفظ المسودة بنجاح');
        } else {
            alert('حدث خطأ في حفظ المسودة');
        }
    });
}

// التحقق من صحة النموذج
document.getElementById('medicalRecordForm').addEventListener('submit', function(event) {
    const patientId = document.getElementById('selected_patient_id').value;
    
    if (!patientId) {
        event.preventDefault();
        alert('يرجى اختيار مريض');
        document.getElementById('patient_search').focus();
        return false;
    }
});
</script>
{% endblock %}
