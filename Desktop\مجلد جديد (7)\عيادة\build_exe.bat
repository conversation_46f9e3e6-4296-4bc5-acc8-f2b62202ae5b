@echo off
chcp 65001 >nul
echo ========================================
echo    نظام العيادة الطبية - بناء البرنامج
echo    Medical Clinic System - Build EXE
echo ========================================
echo.

echo 🔄 التحقق من Python...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    pause
    exit /b 1
)

echo.
echo 🔄 تثبيت المكتبات المطلوبة...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المكتبات
    pause
    exit /b 1
)

echo.
echo 🔄 إعداد قاعدة البيانات...
python manage.py makemigrations
python manage.py migrate

echo.
echo 🔄 جمع الملفات الثابتة...
python manage.py collectstatic --noinput

echo.
echo 🔄 بناء ملف EXE...
pyinstaller clinic_system.spec --clean --noconfirm

if %errorlevel% neq 0 (
    echo ❌ فشل في بناء البرنامج
    pause
    exit /b 1
)

echo.
echo ✅ تم بناء البرنامج بنجاح!
echo 📁 الملف موجود في: dist\ClinicSystem.exe
echo.

echo 🔄 اختبار البرنامج...
if exist "dist\ClinicSystem.exe" (
    echo ✅ الملف موجود وجاهز للاستخدام
    echo.
    echo هل تريد تشغيل البرنامج الآن؟ (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        echo 🚀 تشغيل البرنامج...
        start "" "dist\ClinicSystem.exe"
    )
) else (
    echo ❌ لم يتم العثور على الملف المبني
)

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
