{% extends 'base.html' %}

{% load static %}

{% block title %}إضافة دراسة إشعاعية{% endblock %}



    <style>


        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 2px 10px;
            transition: all 0.3s;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(-5px);
        }

        .main-content {
            margin-right: 250px;
            padding: 20px;
            transition: all 0.3s;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
            transition: transform 0.3s ease;
            margin-bottom: 20px;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-bottom: 15px;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .table-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .page-header {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .navbar-brand {
            font-weight: bold;
            font-size: 1.3rem;
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .main-content {
                margin-right: 0;
            }
        }

        .quick-action {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s;
            text-decoration: none;
            color: #495057;
            display: block;
            margin-bottom: 10px;
        }

        .quick-action:hover {
            border-color: #667eea;
            color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }

        .quick-action i {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }
    </style>
<style>
    .add-radiology-container {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        min-height: 100vh;
        padding: 20px 0;
    }

    .radiology-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-bottom: 30px;
        overflow: hidden;
        max-width: 1000px;
        margin: 0 auto;
    }

    .radiology-header {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        color: white;
        padding: 30px;
        text-align: center;
    }

    .radiology-title {
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 10px;
    }

    .form-section {
        background: white;
        border-radius: 10px;
        padding: 30px;
        margin: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .section-header {
        margin-bottom: 25px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e9ecef;
    }

    .section-title {
        color: #495057;
        font-size: 1.3rem;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }

    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 12px 15px;
        transition: all 0.3s ease;
        font-size: 1rem;
    }

    .form-control:focus, .form-select:focus {
        border-color: #6f42c1;
        box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.25);
    }

    .required {
        color: #dc3545;
    }

    .radiology-type-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 15px;
    }

    .radiology-type-card {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
    }

    .radiology-type-card:hover {
        border-color: #6f42c1;
        background: #f8f9fa;
    }

    .radiology-type-card.selected {
        border-color: #6f42c1;
        background: #f3e5f5;
    }

    .radiology-icon {
        font-size: 2rem;
        color: #6f42c1;
        margin-bottom: 10px;
    }

    .radiology-name {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }

    .radiology-desc {
        font-size: 0.875rem;
        color: #6c757d;
    }

    .form-actions {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e9ecef;
        text-align: center;
    }

    .btn-primary-action {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 25px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-size: 1.1rem;
    }

    .btn-primary-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(111, 66, 193, 0.3);
        color: white;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 25px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-size: 1.1rem;
        margin-left: 15px;
    }

    .btn-secondary:hover {
        background: #5a6268;
        color: white;
        transform: translateY(-2px);
    }

    @media (max-width: 768px) {
        .radiology-type-grid {
            grid-template-columns: 1fr;
        }

        .form-actions .btn-primary-action,
        .form-actions .btn-secondary {
            display: block;
            width: 100%;
            margin: 10px 0;
        }
    }
</style>


{% block content %}
<div class="add-radiology-container">
    <div class="container-fluid">
        <!-- رأس الصفحة -->
        <div class="radiology-card">
            <div class="radiology-header">
                <h1 class="radiology-title">
                    <i class="fas fa-x-ray me-3"></i>
                    طلب دراسة إشعاعية
                </h1>
                <p>{% if record %}للسجل الطبي: {{ record.record_id }}{% else %}طلب مستقل{% endif %}</p>
            </div>
        </div>

        <!-- نموذج طلب الدراسة الإشعاعية -->
        <div class="radiology-card">
            <div class="form-section">
                <form method="post" id="radiologyForm">
                    {% csrf_token %}

                    <!-- نوع الدراسة الإشعاعية -->
                    <div class="section-header">
                        <h3 class="section-title">
                            <i class="fas fa-radiation"></i>
                            نوع الدراسة الإشعاعية
                        </h3>
                    </div>

                    <div class="radiology-type-grid">
                        <div class="radiology-type-card" onclick="selectRadiologyType('XRAY', this)">
                            <div class="radiology-icon">
                                <i class="fas fa-x-ray"></i>
                            </div>
                            <div class="radiology-name">أشعة سينية</div>
                            <div class="radiology-desc">X-Ray</div>
                        </div>

                        <div class="radiology-type-card" onclick="selectRadiologyType('CT', this)">
                            <div class="radiology-icon">
                                <i class="fas fa-circle-notch"></i>
                            </div>
                            <div class="radiology-name">أشعة مقطعية</div>
                            <div class="radiology-desc">CT Scan</div>
                        </div>

                        <div class="radiology-type-card" onclick="selectRadiologyType('MRI', this)">
                            <div class="radiology-icon">
                                <i class="fas fa-magnet"></i>
                            </div>
                            <div class="radiology-name">رنين مغناطيسي</div>
                            <div class="radiology-desc">MRI</div>
                        </div>

                        <div class="radiology-type-card" onclick="selectRadiologyType('ULTRASOUND', this)">
                            <div class="radiology-icon">
                                <i class="fas fa-wave-square"></i>
                            </div>
                            <div class="radiology-name">موجات فوق صوتية</div>
                            <div class="radiology-desc">Ultrasound</div>
                        </div>

                        <div class="radiology-type-card" onclick="selectRadiologyType('MAMMOGRAPHY', this)">
                            <div class="radiology-icon">
                                <i class="fas fa-female"></i>
                            </div>
                            <div class="radiology-name">تصوير الثدي</div>
                            <div class="radiology-desc">Mammography</div>
                        </div>

                        <div class="radiology-type-card" onclick="selectRadiologyType('NUCLEAR', this)">
                            <div class="radiology-icon">
                                <i class="fas fa-atom"></i>
                            </div>
                            <div class="radiology-name">طب نووي</div>
                            <div class="radiology-desc">Nuclear Medicine</div>
                        </div>
                    </div>

                    <input type="hidden" id="selected_radiology_type" name="study_type" required>

                    <!-- تفاصيل الطلب -->
                    <div class="section-header mt-4">
                        <h3 class="section-title">
                            <i class="fas fa-clipboard-list"></i>
                            تفاصيل الطلب
                        </h3>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="study_name" class="form-label">اسم الدراسة <span class="required">*</span></label>
                            <input type="text" class="form-control" id="study_name" name="study_name" required
                                   placeholder="مثال: أشعة سينية على الصدر">
                        </div>
                        <div class="col-md-6">
                            <label for="body_part" class="form-label">الجزء المراد تصويره <span class="required">*</span></label>
                            <input type="text" class="form-control" id="body_part" name="body_part" required
                                   placeholder="مثال: الصدر، البطن، الرأس">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="priority" class="form-label">الأولوية <span class="required">*</span></label>
                            <select class="form-select" id="priority" name="priority" required>
                                <option value="ROUTINE">روتيني</option>
                                <option value="URGENT">عاجل</option>
                                <option value="STAT">فوري</option>
                                <option value="EMERGENCY">طوارئ</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="contrast" class="form-label">استخدام الصبغة</label>
                            <select class="form-select" id="contrast" name="contrast">
                                <option value="NO">بدون صبغة</option>
                                <option value="ORAL">صبغة فموية</option>
                                <option value="IV">صبغة وريدية</option>
                                <option value="BOTH">كلاهما</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="clinical_indication" class="form-label">المؤشر السريري <span class="required">*</span></label>
                        <textarea class="form-control" id="clinical_indication" name="clinical_indication"
                                  rows="3" required placeholder="سبب طلب الدراسة الإشعاعية والأعراض السريرية"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="clinical_history" class="form-label">التاريخ السريري</label>
                        <textarea class="form-control" id="clinical_history" name="clinical_history"
                                  rows="3" placeholder="التاريخ المرضي ذو الصلة"></textarea>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="requested_by" class="form-label">طلب بواسطة</label>
                            <input type="text" class="form-control" id="requested_by" name="requested_by"
                                   value="{{ request.user.get_full_name }}" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="department" class="form-label">القسم الطالب</label>
                            <select class="form-select" id="department" name="department_id">
                                <option value="">اختر القسم</option>
                                {% for dept in departments %}
                                    <option value="{{ dept.id }}">{{ dept.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="preferred_date" class="form-label">التاريخ المفضل</label>
                            <input type="date" class="form-control" id="preferred_date" name="preferred_date">
                        </div>
                        <div class="col-md-6">
                            <label for="preferred_time" class="form-label">الوقت المفضل</label>
                            <select class="form-select" id="preferred_time" name="preferred_time">
                                <option value="">أي وقت</option>
                                <option value="MORNING">صباحاً</option>
                                <option value="AFTERNOON">بعد الظهر</option>
                                <option value="EVENING">مساءً</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="special_instructions" class="form-label">تعليمات خاصة</label>
                        <textarea class="form-control" id="special_instructions" name="special_instructions"
                                  rows="2" placeholder="أي تعليمات خاصة لقسم الأشعة"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="patient_preparation" class="form-label">تحضير المريض</label>
                        <textarea class="form-control" id="patient_preparation" name="patient_preparation"
                                  rows="2" placeholder="تعليمات تحضير المريض قبل الفحص"></textarea>
                    </div>

                    <!-- إجراءات النموذج -->
                    <div class="form-actions">
                        <button type="submit" class="btn-primary-action">
                            <i class="fas fa-paper-plane"></i>
                            إرسال طلب الدراسة
                        </button>
                        <a href="{% if record %}{% url 'record_detail' record.id %}{% else %}{% url 'medical_records:record_list' %}{% endif %}" class="btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// اختيار نوع الدراسة الإشعاعية
function selectRadiologyType(type, element) {
    // إزالة التحديد من جميع البطاقات
    document.querySelectorAll('.radiology-type-card').forEach(card => {
        card.classList.remove('selected');
    });

    // تحديد البطاقة المختارة
    element.classList.add('selected');
    document.getElementById('selected_radiology_type').value = type;
}

// التحقق من صحة النموذج
document.getElementById('radiologyForm').addEventListener('submit', function(event) {
    const radiologyType = document.getElementById('selected_radiology_type').value;

    if (!radiologyType) {
        event.preventDefault();
        alert('يرجى اختيار نوع الدراسة الإشعاعية');
        return false;
    }
});
</script>
{% endblock %}
