{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }} - نظام العيادة الطبية{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="d-flex justify-content-between flex-wrap align-items-start pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2 mb-2 mb-md-0">
            <i class="bi bi-clipboard-data"></i>
            {{ title }}
        </h1>
        <div class="btn-toolbar">
            <a href="{% url 'lab_detail' lab_test.pk %}" class="btn btn-outline-secondary btn-sm">
                <i class="bi bi-arrow-right"></i>
                <span class="d-none d-sm-inline">العودة للفحص</span>
                <span class="d-inline d-sm-none">عودة</span>
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Form Section -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clipboard-data"></i>
                        نتائج الفحص المخبري
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <!-- Status and Technician -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.status.id_for_label }}" class="form-label">
                                    <i class="bi bi-flag"></i> حالة الفحص
                                </label>
                                {{ form.status }}
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.technician.id_for_label }}" class="form-label">
                                    <i class="bi bi-person-badge"></i> فني المختبر
                                </label>
                                {{ form.technician }}
                            </div>
                        </div>

                        <!-- Dates -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.sample_collected_date.id_for_label }}" class="form-label">
                                    <i class="bi bi-calendar-check"></i> تاريخ أخذ العينة
                                </label>
                                {{ form.sample_collected_date }}
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.result_date.id_for_label }}" class="form-label">
                                    <i class="bi bi-calendar-event"></i> تاريخ النتيجة
                                </label>
                                {{ form.result_date }}
                            </div>
                        </div>

                        <!-- Results -->
                        <div class="mb-3">
                            <label for="{{ form.results.id_for_label }}" class="form-label">
                                <i class="bi bi-file-text"></i> نتائج الفحص
                            </label>
                            {{ form.results }}
                            <div class="form-text">أدخل النتائج التفصيلية للفحص</div>
                        </div>

                        <!-- Normal Range and Abnormal Flag -->
                        <div class="row mb-3">
                            <div class="col-md-8">
                                <label for="{{ form.normal_range.id_for_label }}" class="form-label">
                                    <i class="bi bi-bar-chart"></i> المعدل الطبيعي
                                </label>
                                {{ form.normal_range }}
                            </div>
                            <div class="col-md-4">
                                <div class="form-check mt-4">
                                    {{ form.is_abnormal }}
                                    <label class="form-check-label text-danger" for="{{ form.is_abnormal.id_for_label }}">
                                        <i class="bi bi-exclamation-triangle"></i> نتيجة غير طبيعية
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="bi bi-check-circle"></i>
                                حفظ النتائج
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Lab Test Info Sidebar -->
        <div class="col-lg-4">
            <!-- Test Information -->
            <div class="card shadow mb-3">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle"></i> معلومات الفحص
                    </h5>
                </div>
                <div class="card-body">
                    <p><strong>رقم الفحص:</strong><br>
                       <span class="badge bg-primary">{{ lab_test.test_id }}</span></p>
                    
                    <p><strong>نوع الفحص:</strong><br>
                       {{ lab_test.test_name }}</p>
                    
                    <p><strong>فئة الفحص:</strong><br>
                       <span class="badge bg-secondary">{{ lab_test.get_test_category_display }}</span></p>
                    
                    <p><strong>الحالة الحالية:</strong><br>
                       <span class="badge bg-{{ lab_test.get_status_color }}">
                           {{ lab_test.get_status_display }}
                       </span></p>
                    
                    <p><strong>تاريخ الطلب:</strong><br>
                       {{ lab_test.ordered_date|date:"d/m/Y H:i" }}</p>
                </div>
            </div>

            <!-- Patient Information -->
            <div class="card shadow mb-3">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-person"></i> معلومات المريض
                    </h5>
                </div>
                <div class="card-body">
                    <p><strong>الاسم:</strong><br>
                       <a href="{% url 'patient_detail' lab_test.patient.pk %}" class="text-decoration-none">
                           {{ lab_test.patient.full_name }}
                       </a></p>
                    
                    <p><strong>العمر:</strong><br>
                       {{ lab_test.patient.age }} سنة</p>
                    
                    <p><strong>الجنس:</strong><br>
                       {{ lab_test.patient.get_gender_display }}</p>
                    
                    <p><strong>الهاتف:</strong><br>
                       {{ lab_test.patient.phone }}</p>
                </div>
            </div>

            <!-- Doctor Information -->
            <div class="card shadow mb-3">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-person-badge"></i> الطبيب الطالب
                    </h5>
                </div>
                <div class="card-body">
                    <p><strong>الطبيب:</strong><br>
                       {{ lab_test.doctor.first_name }} {{ lab_test.doctor.last_name }}</p>
                    
                    {% if lab_test.instructions %}
                    <p><strong>تعليمات خاصة:</strong><br>
                       <small class="text-muted">{{ lab_test.instructions|truncatechars:100 }}</small></p>
                    {% endif %}
                </div>
            </div>

            <!-- Patient Allergies Warning -->
            {% if lab_test.patient.allergies %}
            <div class="card shadow border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-exclamation-triangle"></i> تحذير - حساسيات المريض
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-danger mb-0">{{ lab_test.patient.allergies }}</p>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-fill result date when status changes to completed
document.getElementById('{{ form.status.id_for_label }}').addEventListener('change', function() {
    if (this.value === 'completed') {
        const now = new Date();
        const dateString = now.toISOString().slice(0, 16);
        document.getElementById('{{ form.result_date.id_for_label }}').value = dateString;
    }
});

// Highlight abnormal results
document.getElementById('{{ form.is_abnormal.id_for_label }}').addEventListener('change', function() {
    const resultsField = document.getElementById('{{ form.results.id_for_label }}');
    if (this.checked) {
        resultsField.classList.add('border-danger');
        resultsField.classList.remove('border-success');
    } else {
        resultsField.classList.add('border-success');
        resultsField.classList.remove('border-danger');
    }
});
</script>
{% endblock %}
