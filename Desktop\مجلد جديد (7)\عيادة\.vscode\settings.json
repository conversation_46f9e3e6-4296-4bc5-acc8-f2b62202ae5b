{"python.terminal.activateEnvInCurrentTerminal": true, "python.testing.pytestEnabled": true, "files.autoSave": "after<PERSON>elay", "python.languageServer": "<PERSON><PERSON><PERSON>", "git.autofetch": true, "diffEditor.renderSideBySide": true, "diffEditor.ignoreTrimWhitespace": true, "gitlens.currentLine.enabled": false, "gitlens.hovers.enabled": false, "gitlens.hovers.currentLine.over": "line", "gitlens.codeLens.enabled": false, "gitlens.defaultDateStyle": "absolute", "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "python.analysis.completeFunctionParens": true, "githubIssues.issueBranchTitle": "feature/${issueNumber}_${sanitizedIssueTitle}"}