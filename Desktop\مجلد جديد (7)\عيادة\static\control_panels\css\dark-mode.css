/* الوضع المظلم للوحات التحكم */

/* متغيرات الألوان للوضع المظلم */
:root {
    --dark-bg-primary: #1a1a1a;
    --dark-bg-secondary: #2d2d2d;
    --dark-bg-tertiary: #3a3a3a;
    --dark-text-primary: #ffffff;
    --dark-text-secondary: #b0b0b0;
    --dark-border: #404040;
    --dark-shadow: rgba(0, 0, 0, 0.3);
}

/* الوضع المظلم الأساسي */
body.dark-mode {
    background-color: var(--dark-bg-primary);
    color: var(--dark-text-primary);
}

/* الشريط الجانبي في الوضع المظلم */
body.dark-mode .sidebar {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

body.dark-mode .sidebar .nav-link {
    color: rgba(255,255,255,0.9);
}

body.dark-mode .sidebar .nav-link:hover,
body.dark-mode .sidebar .nav-link.active {
    background-color: rgba(255,255,255,0.15);
    color: white;
}

/* البطاقات في الوضع المظلم */
body.dark-mode .stat-card,
body.dark-mode .table-card,
body.dark-mode .chart-container {
    background-color: var(--dark-bg-secondary);
    border: 1px solid var(--dark-border);
    box-shadow: 0 5px 15px var(--dark-shadow);
}

body.dark-mode .stat-card:hover,
body.dark-mode .table-card:hover,
body.dark-mode .chart-container:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.4);
}

/* النصوص في الوضع المظلم */
body.dark-mode .stat-number,
body.dark-mode .chart-title,
body.dark-mode h1, body.dark-mode h2, body.dark-mode h3,
body.dark-mode h4, body.dark-mode h5, body.dark-mode h6 {
    color: var(--dark-text-primary);
}

body.dark-mode .stat-label,
body.dark-mode .text-muted,
body.dark-mode .chart-stat-label {
    color: var(--dark-text-secondary);
}

/* الجداول في الوضع المظلم */
body.dark-mode .table {
    color: var(--dark-text-primary);
}

body.dark-mode .table-light {
    background-color: var(--dark-bg-tertiary);
    border-color: var(--dark-border);
}

body.dark-mode .table td,
body.dark-mode .table th {
    border-color: var(--dark-border);
}

body.dark-mode .table-hover tbody tr:hover {
    background-color: var(--dark-bg-tertiary);
}

/* الأزرار في الوضع المظلم */
body.dark-mode .btn-outline-primary {
    color: #667eea;
    border-color: #667eea;
}

body.dark-mode .btn-outline-primary:hover {
    background-color: #667eea;
    border-color: #667eea;
    color: white;
}

body.dark-mode .quick-action {
    background-color: var(--dark-bg-tertiary);
    border-color: var(--dark-border);
    color: var(--dark-text-primary);
}

body.dark-mode .quick-action:hover {
    background-color: var(--dark-bg-secondary);
    border-color: #667eea;
    color: #667eea;
}

/* النماذج في الوضع المظلم */
body.dark-mode .form-control {
    background-color: var(--dark-bg-tertiary);
    border-color: var(--dark-border);
    color: var(--dark-text-primary);
}

body.dark-mode .form-control:focus {
    background-color: var(--dark-bg-tertiary);
    border-color: #667eea;
    color: var(--dark-text-primary);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* التنبيهات في الوضع المظلم */
body.dark-mode .alert {
    border-color: var(--dark-border);
}

body.dark-mode .alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    border-color: #ffc107;
    color: #ffc107;
}

body.dark-mode .alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-color: #dc3545;
    color: #dc3545;
}

body.dark-mode .alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-color: #28a745;
    color: #28a745;
}

body.dark-mode .alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    border-color: #17a2b8;
    color: #17a2b8;
}

/* الشارات في الوضع المظلم */
body.dark-mode .badge {
    color: white;
}

/* القوائم في الوضع المظلم */
body.dark-mode .list-group-item {
    background-color: var(--dark-bg-secondary);
    border-color: var(--dark-border);
    color: var(--dark-text-primary);
}

body.dark-mode .list-group-item-action:hover {
    background-color: var(--dark-bg-tertiary);
}

/* أشرطة التقدم في الوضع المظلم */
body.dark-mode .progress {
    background-color: var(--dark-bg-tertiary);
}

/* الحدود في الوضع المظلم */
body.dark-mode .border,
body.dark-mode .border-top,
body.dark-mode .border-bottom,
body.dark-mode .border-start,
body.dark-mode .border-end {
    border-color: var(--dark-border) !important;
}

/* الرسوم البيانية في الوضع المظلم */
body.dark-mode .chart-container {
    background-color: var(--dark-bg-secondary);
}

body.dark-mode .chart-control-btn {
    background-color: var(--dark-bg-tertiary);
    border-color: var(--dark-border);
    color: var(--dark-text-primary);
}

body.dark-mode .chart-control-btn:hover {
    background-color: var(--dark-bg-secondary);
    border-color: #667eea;
    color: #667eea;
}

body.dark-mode .chart-control-btn.active {
    background-color: #667eea;
    border-color: #667eea;
    color: white;
}

/* زر تبديل الوضع المظلم */
.dark-mode-toggle {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 1000;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.dark-mode-toggle:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.2);
}

body.dark-mode .dark-mode-toggle {
    background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
}

/* تأثيرات الانتقال */
body,
.sidebar,
.stat-card,
.table-card,
.chart-container,
.btn,
.form-control,
.alert,
.list-group-item {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* تحسينات للطباعة في الوضع المظلم */
@media print {
    body.dark-mode {
        background-color: white !important;
        color: black !important;
    }
    
    body.dark-mode .stat-card,
    body.dark-mode .table-card,
    body.dark-mode .chart-container {
        background-color: white !important;
        color: black !important;
        border: 1px solid #dee2e6 !important;
    }
    
    body.dark-mode .dark-mode-toggle {
        display: none !important;
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .dark-mode-toggle {
        bottom: 10px;
        left: 10px;
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}
