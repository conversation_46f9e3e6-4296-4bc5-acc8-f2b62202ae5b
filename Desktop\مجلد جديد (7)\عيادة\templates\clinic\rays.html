{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }} - نظام العيادة الطبية{% endblock %}

{% block extra_css %}
<style>
.rays-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    transition: all 0.3s ease;
}

.rays-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.priority-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 20px;
}

.priority-normal { background-color: #28a745; }
.priority-urgent { background-color: #ffc107; }
.priority-stat { background-color: #dc3545; }

.form-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 1.5rem;
}

.info-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: none;
    color: white;
}

.quick-actions-card {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border: none;
    color: white;
}

.rays-types-card {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border: none;
    color: #333;
}
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom" style="margin-top: 50px;">
    <h1 class="h2">
        <i class="bi bi-camera"></i>
        {{ title }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'rays_list' %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right"></i> العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card rays-card">
            <div class="card-header form-section">
                <h5 class="card-title mb-0">
                    <i class="bi bi-camera"></i> إضافة طلب أشعة جديد
                </h5>
            </div>
            <div class="card-body">
                <form method="post" id="raysForm">
                    {% csrf_token %}
                    {% crispy form %}
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card rays-card info-card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i> إرشادات الأشعة والتصوير
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-light">
                    <h6><i class="bi bi-lightbulb text-warning"></i> نصائح مهمة:</h6>
                    <ul class="mb-0 small">
                        <li>تأكد من اختيار المريض الصحيح</li>
                        <li>حدد نوع الأشعة المطلوبة بدقة</li>
                        <li>اكتب المعلومات السريرية بوضوح</li>
                        <li>حدد الأولوية حسب الحالة</li>
                        <li>تأكد من عدم وجود حساسية للصبغة</li>
                    </ul>
                </div>

                <div class="alert alert-light">
                    <h6><i class="bi bi-exclamation-triangle text-warning"></i> تذكير:</h6>
                    <p class="mb-0 small">
                        تأكد من إعطاء المريض التعليمات اللازمة قبل التصوير،
                        خاصة إذا كان يتطلب تحضير خاص أو صيام.
                    </p>
                </div>
            </div>
        </div>

        <!-- أنواع الأشعة الشائعة -->
        <div class="card rays-card rays-types-card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-collection"></i> أنواع الأشعة الشائعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 mb-2">
                        <span class="badge bg-primary">أشعة سينية</span>
                    </div>
                    <div class="col-6 mb-2">
                        <span class="badge bg-success">أشعة مقطعية</span>
                    </div>
                    <div class="col-6 mb-2">
                        <span class="badge bg-warning">رنين مغناطيسي</span>
                    </div>
                    <div class="col-6 mb-2">
                        <span class="badge bg-info">موجات صوتية</span>
                    </div>
                    <div class="col-6 mb-2">
                        <span class="badge bg-secondary">ماموجرام</span>
                    </div>
                    <div class="col-6 mb-2">
                        <span class="badge bg-dark">أخرى</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- إجراءات سريعة -->
        <div class="card rays-card quick-actions-card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning"></i> إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'rays_list' %}" class="btn btn-light btn-sm">
                        <i class="bi bi-list-ul"></i> عرض جميع الأشعة
                    </a>
                    <a href="{% url 'lab' %}" class="btn btn-light btn-sm">
                        <i class="bi bi-clipboard-data"></i> إضافة فحص مخبري
                    </a>
                    <a href="{% url 'prescription_create' %}" class="btn btn-light btn-sm">
                        <i class="bi bi-prescription2"></i> كتابة وصفة طبية
                    </a>
                    <a href="{% url 'payment_create' %}" class="btn btn-light btn-sm">
                        <i class="bi bi-credit-card"></i> تسجيل دفعة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-expand textareas
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(textarea => {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    });

    // Validate expected date
    const expectedDateInput = document.querySelector('input[name="expected_date"]');
    if (expectedDateInput) {
        const today = new Date().toISOString().split('T')[0];
        expectedDateInput.min = today;

        expectedDateInput.addEventListener('change', function(e) {
            const selectedDate = new Date(e.target.value);
            const currentDate = new Date();

            if (selectedDate < currentDate) {
                alert('التاريخ المتوقع للتصوير يجب أن يكون في المستقبل');
                e.target.focus();
            }
        });
    }

    // Priority selection enhancement
    const prioritySelect = document.querySelector('select[name="priority"]');
    if (prioritySelect) {
        prioritySelect.addEventListener('change', function(e) {
            const priority = e.target.value;
            const badge = document.createElement('span');
            badge.className = `badge priority-${priority} ms-2`;

            // Remove existing badge
            const existingBadge = e.target.parentNode.querySelector('.badge');
            if (existingBadge) {
                existingBadge.remove();
            }

            // Add new badge
            switch(priority) {
                case 'normal':
                    badge.textContent = 'عادي';
                    break;
                case 'urgent':
                    badge.textContent = 'عاجل';
                    break;
                case 'stat':
                    badge.textContent = 'طارئ';
                    break;
            }

            e.target.parentNode.appendChild(badge);
        });

        // Trigger initial badge
        prioritySelect.dispatchEvent(new Event('change'));
    }

    // Contrast allergy warning
    const contrastAllergyCheckbox = document.querySelector('input[name="contrast_allergy"]');
    if (contrastAllergyCheckbox) {
        contrastAllergyCheckbox.addEventListener('change', function(e) {
            if (e.target.checked) {
                const alert = document.createElement('div');
                alert.className = 'alert alert-danger mt-2';
                alert.innerHTML = '<i class="bi bi-exclamation-triangle"></i> تحذير: المريض لديه حساسية من الصبغة - تجنب استخدام الصبغة';

                // Remove existing alert
                const existingAlert = e.target.closest('.form-group').querySelector('.alert');
                if (existingAlert) {
                    existingAlert.remove();
                }

                e.target.closest('.form-group').appendChild(alert);
            } else {
                const existingAlert = e.target.closest('.form-group').querySelector('.alert');
                if (existingAlert) {
                    existingAlert.remove();
                }
            }
        });
    }

    // Preparation requirement alert
    const preparationCheckbox = document.querySelector('input[name="preparation_required"]');
    if (preparationCheckbox) {
        preparationCheckbox.addEventListener('change', function(e) {
            if (e.target.checked) {
                const alert = document.createElement('div');
                alert.className = 'alert alert-info mt-2';
                alert.innerHTML = '<i class="bi bi-info-circle"></i> تذكير: يجب إعلام المريض بالتحضير المطلوب قبل التصوير';

                // Remove existing alert
                const existingAlert = e.target.closest('.form-group').querySelector('.alert');
                if (existingAlert) {
                    existingAlert.remove();
                }

                e.target.closest('.form-group').appendChild(alert);
            } else {
                const existingAlert = e.target.closest('.form-group').querySelector('.alert');
                if (existingAlert) {
                    existingAlert.remove();
                }
            }
        });
    }

    // Form validation
    const form = document.getElementById('raysForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            const patient = document.querySelector('select[name="patient"]').value;
            const raysName = document.querySelector('select[name="rays_name"]').value;
            const part = document.querySelector('input[name="part"]').value;

            if (!patient || !raysName || !part) {
                e.preventDefault();
                alert('يرجى ملء جميع الحقول المطلوبة');
                return false;
            }

            // Show loading state
            const submitBtn = form.querySelector('input[type="submit"]');
            submitBtn.value = 'جاري الحفظ...';
            submitBtn.disabled = true;
        });
    }

    // Character counter for clinical info and instructions
    const clinicalField = document.querySelector('textarea[name="linical"]');
    const instructionsField = document.querySelector('textarea[name="instructions"]');

    [clinicalField, instructionsField].forEach(field => {
        if (field) {
            const maxLength = field.name === 'linical' ? 300 : 500;
            const counter = document.createElement('small');
            counter.className = 'text-muted';
            field.parentNode.appendChild(counter);

            function updateCounter() {
                const remaining = maxLength - field.value.length;
                counter.textContent = `${field.value.length}/${maxLength} حرف`;

                if (remaining < 50) {
                    counter.className = 'text-warning';
                } else if (remaining < 0) {
                    counter.className = 'text-danger';
                } else {
                    counter.className = 'text-muted';
                }
            }

            field.addEventListener('input', updateCounter);
            updateCounter();
        }
    });

    // Add animation to cards
    const cards = document.querySelectorAll('.rays-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
