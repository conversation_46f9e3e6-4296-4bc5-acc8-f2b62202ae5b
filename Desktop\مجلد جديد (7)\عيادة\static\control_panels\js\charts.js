// ملف الرسوم البيانية المحسن لنظام إدارة المستشفى

// إعدادات الألوان والتدرجات
const chartColors = {
    primary: '#667eea',
    secondary: '#764ba2',
    success: '#28a745',
    danger: '#dc3545',
    warning: '#ffc107',
    info: '#17a2b8',
    light: '#f8f9fa',
    dark: '#343a40'
};

const gradients = {
    blue: ['#667eea', '#764ba2'],
    green: ['#11998e', '#38ef7d'],
    orange: ['#ffecd2', '#fcb69f'],
    pink: ['#a8edea', '#fed6e3'],
    red: ['#ff6b6b', '#ee5a24'],
    purple: ['#a8c0ff', '#3f2b96']
};

// إعدادات عامة للرسوم البيانية
Chart.defaults.font.family = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
Chart.defaults.font.size = 12;
Chart.defaults.color = '#495057';

// دالة إنشاء تدرج لوني
function createGradient(ctx, colorArray, direction = 'vertical') {
    const gradient = direction === 'vertical' 
        ? ctx.createLinearGradient(0, 0, 0, 400)
        : ctx.createLinearGradient(0, 0, 400, 0);
    
    gradient.addColorStop(0, colorArray[0]);
    gradient.addColorStop(1, colorArray[1]);
    return gradient;
}

// دالة إنشاء تدرج شفاف
function createTransparentGradient(ctx, color, opacity = 0.1) {
    const gradient = ctx.createLinearGradient(0, 0, 0, 400);
    gradient.addColorStop(0, color + Math.round(opacity * 255).toString(16).padStart(2, '0'));
    gradient.addColorStop(1, color + '00');
    return gradient;
}

// رسم بياني للإيرادات الشهرية (محسن)
function createRevenueChart(canvasId, data = null) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) return;
    
    const defaultData = {
        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
        datasets: [{
            label: 'الإيرادات (ريال)',
            data: [120000, 150000, 180000, 140000, 200000, 175000],
            borderColor: chartColors.primary,
            backgroundColor: createTransparentGradient(ctx.getContext('2d'), chartColors.primary, 0.1),
            borderWidth: 3,
            tension: 0.4,
            fill: true,
            pointBackgroundColor: chartColors.primary,
            pointBorderColor: '#fff',
            pointBorderWidth: 2,
            pointRadius: 6,
            pointHoverRadius: 8,
            pointHoverBackgroundColor: chartColors.secondary,
            pointHoverBorderColor: '#fff',
            pointHoverBorderWidth: 3
        }]
    };

    return new Chart(ctx, {
        type: 'line',
        data: data || defaultData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: chartColors.primary,
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: false,
                    callbacks: {
                        label: function(context) {
                            return 'الإيرادات: ' + context.parsed.y.toLocaleString() + ' ريال';
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#6c757d'
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.05)',
                        borderDash: [5, 5]
                    },
                    ticks: {
                        color: '#6c757d',
                        callback: function(value) {
                            return (value / 1000) + 'ك ريال';
                        }
                    }
                }
            },
            elements: {
                point: {
                    hoverRadius: 8
                }
            },
            animation: {
                duration: 2000,
                easing: 'easeInOutQuart'
            }
        }
    });
}

// رسم بياني دائري لطرق الدفع (محسن)
function createPaymentMethodsChart(canvasId, data = null) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) return;
    
    const defaultData = {
        labels: ['نقدي', 'بطاقة ائتمان', 'تحويل بنكي', 'تأمين صحي'],
        datasets: [{
            data: [40, 30, 20, 10],
            backgroundColor: [
                chartColors.primary,
                chartColors.success,
                chartColors.warning,
                chartColors.info
            ],
            borderWidth: 0,
            hoverBorderWidth: 3,
            hoverBorderColor: '#fff'
        }]
    };

    return new Chart(ctx, {
        type: 'doughnut',
        data: data || defaultData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '60%',
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        pointStyle: 'circle',
                        font: {
                            size: 11
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return label + ': ' + percentage + '%';
                        }
                    }
                }
            },
            animation: {
                animateRotate: true,
                duration: 2000
            }
        }
    });
}

// رسم بياني عمودي للمواعيد حسب القسم (محسن)
function createAppointmentsByDepartmentChart(canvasId, data = null) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) return;
    
    const defaultData = {
        labels: ['الطوارئ', 'الباطنة', 'الجراحة', 'الأطفال', 'النساء والولادة'],
        datasets: [{
            label: 'عدد المواعيد',
            data: [45, 32, 28, 25, 30],
            backgroundColor: [
                chartColors.danger,
                chartColors.primary,
                chartColors.success,
                chartColors.warning,
                chartColors.info
            ],
            borderRadius: 8,
            borderSkipped: false,
            hoverBackgroundColor: [
                chartColors.danger + 'CC',
                chartColors.primary + 'CC',
                chartColors.success + 'CC',
                chartColors.warning + 'CC',
                chartColors.info + 'CC'
            ]
        }]
    };

    return new Chart(ctx, {
        type: 'bar',
        data: data || defaultData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            return 'المواعيد: ' + context.parsed.y;
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#6c757d',
                        maxRotation: 45
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.05)',
                        borderDash: [5, 5]
                    },
                    ticks: {
                        color: '#6c757d',
                        stepSize: 5
                    }
                }
            },
            animation: {
                duration: 1500,
                easing: 'easeOutBounce'
            }
        }
    });
}

// رسم بياني خطي للمرضى الجدد (محسن)
function createNewPatientsChart(canvasId, data = null) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) return;
    
    const defaultData = {
        labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
        datasets: [{
            label: 'مرضى جدد',
            data: [12, 19, 15, 25, 22, 18, 8],
            borderColor: chartColors.success,
            backgroundColor: createTransparentGradient(ctx.getContext('2d'), chartColors.success, 0.1),
            borderWidth: 3,
            tension: 0.3,
            fill: true,
            pointBackgroundColor: chartColors.success,
            pointBorderColor: '#fff',
            pointBorderWidth: 2,
            pointRadius: 5,
            pointHoverRadius: 7
        }]
    };

    return new Chart(ctx, {
        type: 'line',
        data: data || defaultData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    cornerRadius: 8
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.05)'
                    }
                }
            },
            animation: {
                duration: 1500
            }
        }
    });
}

// رسم بياني للمخزون (للصيدلية)
function createInventoryChart(canvasId, data = null) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) return;
    
    const defaultData = {
        labels: ['متوفر', 'مخزون منخفض', 'منتهي الصلاحية', 'نفد المخزون'],
        datasets: [{
            data: [65, 20, 10, 5],
            backgroundColor: [
                chartColors.success,
                chartColors.warning,
                chartColors.danger,
                chartColors.dark
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    };

    return new Chart(ctx, {
        type: 'pie',
        data: data || defaultData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 15,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ' + percentage + '%';
                        }
                    }
                }
            },
            animation: {
                duration: 2000
            }
        }
    });
}

// رسم بياني للحالات الطارئة
function createEmergencyCasesChart(canvasId, data = null) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) return;
    
    const defaultData = {
        labels: ['حرجة', 'متوسطة', 'بسيطة'],
        datasets: [{
            data: [5, 15, 25],
            backgroundColor: [
                chartColors.danger,
                chartColors.warning,
                chartColors.success
            ],
            borderWidth: 0
        }]
    };

    return new Chart(ctx, {
        type: 'doughnut',
        data: data || defaultData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '50%',
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// دالة تحديث الرسوم البيانية
function updateChart(chart, newData) {
    if (chart && newData) {
        chart.data = newData;
        chart.update('active');
    }
}

// دالة تدمير الرسم البياني
function destroyChart(chart) {
    if (chart) {
        chart.destroy();
    }
}

// تصدير الدوال للاستخدام العام
window.ChartUtils = {
    createRevenueChart,
    createPaymentMethodsChart,
    createAppointmentsByDepartmentChart,
    createNewPatientsChart,
    createInventoryChart,
    createEmergencyCasesChart,
    updateChart,
    destroyChart,
    chartColors,
    gradients
};
