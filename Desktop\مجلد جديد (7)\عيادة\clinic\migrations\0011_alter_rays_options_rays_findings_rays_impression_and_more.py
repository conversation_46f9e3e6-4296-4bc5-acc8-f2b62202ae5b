# Generated by Django 4.2.7 on 2025-07-07 00:03

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("clinic", "0010_rays"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="rays",
            options={
                "ordering": ["-ordered_date"],
                "verbose_name": "أشعة",
                "verbose_name_plural": "الأشعة",
            },
        ),
        migrations.AddField(
            model_name="rays",
            name="findings",
            field=models.TextField(blank=True, verbose_name="الموجودات"),
        ),
        migrations.AddField(
            model_name="rays",
            name="impression",
            field=models.TextField(blank=True, verbose_name="الانطباع التشخيصي"),
        ),
        migrations.AddField(
            model_name="rays",
            name="is_abnormal",
            field=models.BooleanField(default=False, verbose_name="غير طبيعي"),
        ),
        migrations.AddField(
            model_name="rays",
            name="is_urgent",
            field=models.BooleanField(default=False, verbose_name="عاجل"),
        ),
        migrations.AddField(
            model_name="rays",
            name="radiologist",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="radiology_reports",
                to=settings.AUTH_USER_MODEL,
                verbose_name="أخصائي الأشعة",
            ),
        ),
        migrations.AddField(
            model_name="rays",
            name="radiologist_report",
            field=models.TextField(blank=True, verbose_name="تقرير أخصائي الأشعة"),
        ),
        migrations.AddField(
            model_name="rays",
            name="recommendations",
            field=models.TextField(blank=True, verbose_name="التوصيات"),
        ),
        migrations.AddField(
            model_name="rays",
            name="result_date",
            field=models.DateTimeField(
                blank=True, null=True, verbose_name="تاريخ النتيجة"
            ),
        ),
        migrations.AddField(
            model_name="rays",
            name="results",
            field=models.TextField(blank=True, verbose_name="نتائج الأشعة"),
        ),
        migrations.AddField(
            model_name="rays",
            name="scan_date",
            field=models.DateTimeField(
                blank=True, null=True, verbose_name="تاريخ التصوير"
            ),
        ),
        migrations.AddField(
            model_name="rays",
            name="status",
            field=models.CharField(
                choices=[
                    ("ordered", "مطلوب"),
                    ("in_progress", "قيد التنفيذ"),
                    ("completed", "مكتمل"),
                    ("cancelled", "ملغي"),
                ],
                default="ordered",
                max_length=15,
                verbose_name="الحالة",
            ),
        ),
        migrations.AlterField(
            model_name="rays",
            name="UseOfSeed",
            field=models.CharField(
                choices=[("yes", "نعم"), ("no", "لا"), ("not_applicable", "غير مطبق")],
                default="no",
                max_length=20,
                verbose_name="استخدام الصبغة",
            ),
        ),
        migrations.AlterField(
            model_name="rays",
            name="part",
            field=models.CharField(max_length=100, verbose_name="الجزء المراد تصويره"),
        ),
        migrations.AlterField(
            model_name="rays",
            name="rays_id",
            field=models.CharField(
                max_length=20, unique=True, verbose_name="رقم الأشعة"
            ),
        ),
        migrations.AlterField(
            model_name="rays",
            name="rays_name",
            field=models.CharField(
                choices=[
                    ("X-Ray", "أشعة سينية"),
                    ("CT Scan", "أشعة مقطعية"),
                    ("MRI", "رنين مغناطيسي"),
                    ("Ultrasound", "موجات فوق صوتية"),
                    ("Mammography", "تصوير الثدي"),
                    ("Nuclear Medicine", "طب نووي"),
                ],
                max_length=200,
                verbose_name="نوع الأشعة",
            ),
        ),
    ]
