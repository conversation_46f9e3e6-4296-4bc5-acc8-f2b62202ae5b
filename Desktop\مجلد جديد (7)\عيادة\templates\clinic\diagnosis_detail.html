{% extends 'base.html' %}

{% block title %}تفاصيل التشخيص - نظام العيادة الطبية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap align-items-start pt-3 pb-2 mb-3 border-bottom" style="margin-top: 50px;">
    <h1 class="h2 mb-2 mb-md-0">
        <i class="bi bi-clipboard-pulse"></i>
        تفاصيل التشخيص
    </h1>
    <div class="btn-toolbar">
        <div class="btn-group me-2 mb-2 mb-md-0">
            <a href="{% url 'diagnosis_update' diagnosis.pk %}" class="btn btn-warning btn-sm">
                <i class="bi bi-pencil"></i>
                <span class="d-none d-lg-inline">تعديل</span>
            </a>
            <a href="{% url 'diagnosis_delete' diagnosis.pk %}" class="btn btn-danger btn-sm">
                <i class="bi bi-trash"></i>
                <span class="d-none d-lg-inline">حذف</span>
            </a>
        </div>
        <a href="{% url 'diagnosis_list' %}" class="btn btn-outline-secondary btn-sm">
            <i class="bi bi-arrow-right"></i>
            <span class="d-none d-sm-inline">العودة للقائمة</span>
            <span class="d-inline d-sm-none">عودة</span>
        </a>
    </div>
</div>

<div class="row">
    <!-- Diagnosis Details -->
    <div class="col-lg-8 mb-4">
        <!-- Header Information -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i> معلومات التشخيص
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>المريض:</strong></td>
                                <td>
                                    <a href="{% url 'patient_detail' diagnosis.patient.pk %}" class="text-decoration-none">
                                        {{ diagnosis.patient.full_name }}
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ التشخيص:</strong></td>
                                <td>{{ diagnosis.diagnosis_date|date:"d/m/Y H:i" }}</td>
                            </tr>
                            <tr>
                                <td><strong>الطبيب:</strong></td>
                                <td>{{ diagnosis.doctor.first_name }} {{ diagnosis.doctor.last_name }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            {% if diagnosis.appointment %}
                            <tr>
                                <td><strong>الموعد المرتبط:</strong></td>
                                <td>
                                    <a href="{% url 'appointment_detail' diagnosis.appointment.pk %}" class="text-decoration-none">
                                        {{ diagnosis.appointment.appointment_date|date:"d/m/Y H:i" }}
                                    </a>
                                </td>
                            </tr>
                            {% endif %}
                            {% if diagnosis.follow_up_date %}
                            <tr>
                                <td><strong>موعد المتابعة:</strong></td>
                                <td>{{ diagnosis.follow_up_date|date:"d/m/Y" }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Symptoms -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-thermometer"></i> الأعراض
                </h5>
            </div>
            <div class="card-body">
                <div class="bg-light p-3 rounded">
                    <pre style="white-space: pre-wrap; font-family: inherit;">{{ diagnosis.symptoms }}</pre>
                </div>
            </div>
        </div>

        <!-- Diagnosis -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clipboard-check"></i> التشخيص
                </h5>
            </div>
            <div class="card-body">
                <div class="bg-light p-3 rounded">
                    <pre style="white-space: pre-wrap; font-family: inherit;">{{ diagnosis.diagnosis }}</pre>
                </div>
            </div>
        </div>

        <!-- Treatment Plan -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-heart-pulse"></i> خطة العلاج
                </h5>
            </div>
            <div class="card-body">
                <div class="bg-light p-3 rounded">
                    <pre style="white-space: pre-wrap; font-family: inherit;">{{ diagnosis.treatment_plan }}</pre>
                </div>
            </div>
        </div>

        <!-- Notes -->
        {% if diagnosis.notes %}
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-sticky"></i> ملاحظات إضافية
                </h5>
            </div>
            <div class="card-body">
                <div class="bg-light p-3 rounded">
                    <pre style="white-space: pre-wrap; font-family: inherit;">{{ diagnosis.notes }}</pre>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Patient Info -->
        <div class="card shadow mb-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person-badge"></i> معلومات المريض
                </h5>
            </div>
            <div class="card-body">
                <p><strong>الاسم:</strong><br>
                   <a href="{% url 'patient_detail' diagnosis.patient.pk %}" class="text-decoration-none">
                       {{ diagnosis.patient.full_name }}
                   </a>
                </p>
                <p><strong>العمر:</strong> {{ diagnosis.patient.age }} سنة</p>
                <p><strong>الجنس:</strong> {{ diagnosis.patient.get_gender_display }}</p>
                <p><strong>الهاتف:</strong> {{ diagnosis.patient.phone }}</p>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card shadow mb-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning"></i> إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'prescription_create' %}?patient_id={{ diagnosis.patient.id }}&diagnosis_id={{ diagnosis.id }}"
                       class="btn btn-outline-success btn-sm">
                        <i class="bi bi-prescription2"></i> كتابة وصفة طبية
                    </a>
                    <a href="{% url 'appointment_create' %}?patient_id={{ diagnosis.patient.id }}"
                       class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-calendar-plus"></i> حجز موعد متابعة
                    </a>
                    <a href="{% url 'diagnosis_create' %}?patient_id={{ diagnosis.patient.id }}"
                       class="btn btn-outline-info btn-sm">
                        <i class="bi bi-clipboard-plus"></i> تشخيص جديد
                    </a>
                </div>
            </div>
        </div>

        <!-- Follow-up Reminder -->
        {% if diagnosis.follow_up_date %}
        <div class="card shadow border-warning mb-3">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="bi bi-calendar-event"></i> تذكير المتابعة
                </h5>
            </div>
            <div class="card-body">
                <p><strong>موعد المتابعة:</strong><br>
                   {{ diagnosis.follow_up_date|date:"d/m/Y" }}</p>
                {% if diagnosis.follow_up_date|date:"Y-m-d" <= today|date:"Y-m-d" %}
                    <div class="alert alert-warning mb-0">
                        <small><i class="bi bi-exclamation-triangle"></i> حان وقت المتابعة!</small>
                    </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Related Prescriptions -->
        <div class="card shadow mb-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-prescription2"></i> الوصفات المرتبطة
                </h5>
            </div>
            <div class="card-body">
                {% with prescriptions=diagnosis.prescription_set.all %}
                    {% if prescriptions %}
                        {% for prescription in prescriptions %}
                            <div class="border-bottom pb-2 mb-2">
                                <a href="{% url 'prescription_detail' prescription.pk %}" class="text-decoration-none">
                                    <small class="text-muted">{{ prescription.prescription_date|date:"d/m/Y" }}</small><br>
                                    {{ prescription.medications|truncatechars:50 }}
                                </a>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted mb-0">لا توجد وصفات مرتبطة بهذا التشخيص</p>
                    {% endif %}
                {% endwith %}
            </div>
        </div>

        <!-- Medical History -->
        {% if diagnosis.patient.medical_history %}
        <div class="card shadow border-info">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-heart-pulse"></i> التاريخ المرضي
                </h5>
            </div>
            <div class="card-body">
                <p class="mb-0">{{ diagnosis.patient.medical_history|truncatechars:100 }}</p>
                <a href="{% url 'patient_detail' diagnosis.patient.pk %}" class="btn btn-sm btn-outline-info mt-2">
                    عرض التفاصيل الكاملة
                </a>
            </div>
        </div>
        {% endif %}

        <!-- Allergies Warning -->
        {% if diagnosis.patient.allergies %}
        <div class="card shadow border-danger mt-3">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-exclamation-triangle"></i> تحذير - حساسيات
                </h5>
            </div>
            <div class="card-body">
                <p class="text-danger mb-0">{{ diagnosis.patient.allergies }}</p>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
