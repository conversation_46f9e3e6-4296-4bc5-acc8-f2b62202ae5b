{% extends 'base.html' %}

{% block title %}تفاصيل الموعد - نظام العيادة الطبية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap align-items-start pt-3 pb-2 mb-3 border-bottom" style="margin-top: 50px;">
    <h1 class="h2 mb-2 mb-md-0">
        <i class="bi bi-calendar-event"></i>
        تفاصيل الموعد
    </h1>
    <div class="btn-toolbar">
        <div class="btn-group me-2 mb-2 mb-md-0">
            <a href="{% url 'appointment_update' appointment.pk %}" class="btn btn-warning btn-sm">
                <i class="bi bi-pencil"></i>
                <span class="d-none d-lg-inline">تعديل</span>
            </a>
            <a href="{% url 'appointment_delete' appointment.pk %}" class="btn btn-danger btn-sm">
                <i class="bi bi-trash"></i>
                <span class="d-none d-lg-inline">حذف</span>
            </a>
        </div>
        <a href="{% url 'appointment_list' %}" class="btn btn-outline-secondary btn-sm">
            <i class="bi bi-arrow-right"></i>
            <span class="d-none d-sm-inline">العودة للقائمة</span>
            <span class="d-inline d-sm-none">عودة</span>
        </a>
    </div>
</div>

<div class="row">
    <!-- Appointment Details -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-calendar-check"></i> معلومات الموعد
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>المريض:</strong></td>
                                <td>
                                    <a href="{% url 'patient_detail' appointment.patient.pk %}" class="text-decoration-none">
                                        {{ appointment.patient.full_name }}
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>التاريخ:</strong></td>
                                <td>{{ appointment.appointment_date|date:"d/m/Y" }}</td>
                            </tr>
                            <tr>
                                <td><strong>الوقت:</strong></td>
                                <td>{{ appointment.appointment_date|time:"H:i" }}</td>
                            </tr>
                            <tr>
                                <td><strong>المدة:</strong></td>
                                <td>{{ appointment.duration }} دقيقة</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>الطبيب:</strong></td>
                                <td>{{ appointment.doctor.first_name }} {{ appointment.doctor.last_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    {% if appointment.status == 'scheduled' %}
                                        <span class="badge bg-primary fs-6">{{ appointment.get_status_display }}</span>
                                    {% elif appointment.status == 'completed' %}
                                        <span class="badge bg-success fs-6">{{ appointment.get_status_display }}</span>
                                    {% elif appointment.status == 'cancelled' %}
                                        <span class="badge bg-danger fs-6">{{ appointment.get_status_display }}</span>
                                    {% else %}
                                        <span class="badge bg-secondary fs-6">{{ appointment.get_status_display }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الإنشاء:</strong></td>
                                <td>{{ appointment.created_at|date:"d/m/Y H:i" }}</td>
                            </tr>
                            <tr>
                                <td><strong>آخر تحديث:</strong></td>
                                <td>{{ appointment.updated_at|date:"d/m/Y H:i" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reason for Visit -->
        <div class="card shadow mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-chat-text"></i> سبب الزيارة
                </h5>
            </div>
            <div class="card-body">
                <p class="mb-0">{{ appointment.reason }}</p>
            </div>
        </div>

        <!-- Notes -->
        {% if appointment.notes %}
        <div class="card shadow mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-sticky"></i> ملاحظات
                </h5>
            </div>
            <div class="card-body">
                <p class="mb-0">{{ appointment.notes }}</p>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Patient Info -->
        <div class="card shadow mb-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person-badge"></i> معلومات المريض
                </h5>
            </div>
            <div class="card-body">
                <p><strong>الاسم:</strong><br>
                   <a href="{% url 'patient_detail' appointment.patient.pk %}" class="text-decoration-none">
                       {{ appointment.patient.full_name }}
                   </a>
                </p>
                <p><strong>العمر:</strong> {{ appointment.patient.age }} سنة</p>
                <p><strong>الجنس:</strong> {{ appointment.patient.get_gender_display }}</p>
                <p><strong>الهاتف:</strong> {{ appointment.patient.phone }}</p>
                {% if appointment.patient.email %}
                    <p><strong>البريد الإلكتروني:</strong> {{ appointment.patient.email }}</p>
                {% endif %}
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card shadow mb-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning"></i> إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if appointment.status == 'scheduled' %}
                        <a href="{% url 'diagnosis_create' %}?patient_id={{ appointment.patient.id }}&appointment_id={{ appointment.id }}"
                           class="btn btn-outline-success btn-sm">
                            <i class="bi bi-clipboard-plus"></i> إضافة تشخيص
                        </a>
                        <a href="{% url 'prescription_create' %}?patient_id={{ appointment.patient.id }}"
                           class="btn btn-outline-warning btn-sm">
                            <i class="bi bi-prescription2"></i> كتابة وصفة طبية
                        </a>
                    {% endif %}
                    <a href="{% url 'appointment_create' %}?patient_id={{ appointment.patient.id }}"
                       class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-calendar-plus"></i> حجز موعد جديد
                    </a>
                </div>
            </div>
        </div>

        <!-- Appointment Status Actions -->
        {% if appointment.status == 'scheduled' %}
        <div class="card shadow border-warning">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock"></i> إجراءات الموعد
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted mb-3">يمكنك تغيير حالة الموعد:</p>
                <div class="d-grid gap-2">
                    <button onclick="updateAppointmentStatus('completed')" class="btn btn-success btn-sm">
                        <i class="bi bi-check-circle"></i> تم الانتهاء
                    </button>
                    <button onclick="updateAppointmentStatus('cancelled')" class="btn btn-danger btn-sm">
                        <i class="bi bi-x-circle"></i> إلغاء الموعد
                    </button>
                    <button onclick="updateAppointmentStatus('no_show')" class="btn btn-secondary btn-sm">
                        <i class="bi bi-person-x"></i> لم يحضر
                    </button>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Medical History Alert -->
        {% if appointment.patient.medical_history %}
        <div class="card shadow border-info mt-3">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-heart-pulse"></i> التاريخ المرضي
                </h5>
            </div>
            <div class="card-body">
                <p class="mb-0">{{ appointment.patient.medical_history|truncatechars:100 }}</p>
                <a href="{% url 'patient_detail' appointment.patient.pk %}" class="btn btn-sm btn-outline-info mt-2">
                    عرض التفاصيل الكاملة
                </a>
            </div>
        </div>
        {% endif %}

        <!-- Allergies Warning -->
        {% if appointment.patient.allergies %}
        <div class="card shadow border-danger mt-3">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-exclamation-triangle"></i> تحذير - حساسيات
                </h5>
            </div>
            <div class="card-body">
                <p class="text-danger mb-0">{{ appointment.patient.allergies }}</p>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updateAppointmentStatus(status) {
    if (confirm('هل أنت متأكد من تغيير حالة الموعد؟')) {
        // This would typically send an AJAX request to update the status
        // For now, we'll redirect to the edit page
        window.location.href = "{% url 'appointment_update' appointment.pk %}";
    }
}
</script>
{% endblock %}
