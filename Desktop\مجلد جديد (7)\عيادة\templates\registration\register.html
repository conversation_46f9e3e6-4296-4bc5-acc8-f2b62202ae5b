<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - نظام العيادة الطبية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .register-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card register-card">
                    <div class="card-body p-5">
                        <!-- Header -->
                        <div class="text-center mb-4">
                            <div class="bg-primary bg-opacity-10 rounded-circle p-3 d-inline-block mb-3">
                                <i class="bi bi-hospital fs-1 text-primary"></i>
                            </div>
                            <h2 class="fw-bold text-dark">إنشاء حساب جديد</h2>
                            <p class="text-muted">انضم إلى نظام العيادة الطبية</p>
                        </div>

                        <!-- Messages -->
                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}

                        <!-- Registration Form -->
                        <form method="post" novalidate>
                            {% csrf_token %}
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                        <i class="bi bi-person"></i> {{ form.first_name.label }}
                                    </label>
                                    {{ form.first_name }}
                                    {% if form.first_name.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.first_name.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                        <i class="bi bi-person"></i> {{ form.last_name.label }}
                                    </label>
                                    {{ form.last_name }}
                                    {% if form.last_name.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.last_name.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label">
                                    <i class="bi bi-at"></i> {{ form.username.label }}
                                </label>
                                {{ form.username }}
                                {% if form.username.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.username.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    <small>اسم المستخدم يجب أن يكون فريد ولا يحتوي على مسافات</small>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">
                                    <i class="bi bi-envelope"></i> {{ form.email.label }}
                                </label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.email.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.password1.id_for_label }}" class="form-label">
                                    <i class="bi bi-lock"></i> {{ form.password1.label }}
                                </label>
                                {{ form.password1 }}
                                {% if form.password1.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.password1.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    <small>كلمة المرور يجب أن تكون 8 أحرف على الأقل</small>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="{{ form.password2.id_for_label }}" class="form-label">
                                    <i class="bi bi-lock-fill"></i> {{ form.password2.label }}
                                </label>
                                {{ form.password2 }}
                                {% if form.password2.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.password2.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-person-plus"></i> إنشاء الحساب
                                </button>
                            </div>
                        </form>

                        <!-- Login Link -->
                        <div class="text-center">
                            <p class="text-muted mb-0">
                                لديك حساب بالفعل؟ 
                                <a href="{% url 'login' %}" class="text-decoration-none fw-bold">
                                    تسجيل الدخول
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Password strength indicator
        document.addEventListener('DOMContentLoaded', function() {
            const password1 = document.getElementById('{{ form.password1.id_for_label }}');
            const password2 = document.getElementById('{{ form.password2.id_for_label }}');
            
            if (password1) {
                password1.addEventListener('input', function() {
                    const value = this.value;
                    let strength = 0;
                    
                    if (value.length >= 8) strength++;
                    if (/[a-z]/.test(value)) strength++;
                    if (/[A-Z]/.test(value)) strength++;
                    if (/[0-9]/.test(value)) strength++;
                    if (/[^A-Za-z0-9]/.test(value)) strength++;
                    
                    // Remove existing strength indicator
                    const existing = this.parentNode.querySelector('.password-strength');
                    if (existing) existing.remove();
                    
                    if (value.length > 0) {
                        const indicator = document.createElement('div');
                        indicator.className = 'password-strength mt-1';
                        
                        let color = 'danger';
                        let text = 'ضعيفة';
                        
                        if (strength >= 3) {
                            color = 'success';
                            text = 'قوية';
                        } else if (strength >= 2) {
                            color = 'warning';
                            text = 'متوسطة';
                        }
                        
                        indicator.innerHTML = `<small class="text-${color}">قوة كلمة المرور: ${text}</small>`;
                        this.parentNode.appendChild(indicator);
                    }
                });
            }
            
            // Password confirmation check
            if (password2) {
                password2.addEventListener('input', function() {
                    const existing = this.parentNode.querySelector('.password-match');
                    if (existing) existing.remove();
                    
                    if (this.value.length > 0) {
                        const indicator = document.createElement('div');
                        indicator.className = 'password-match mt-1';
                        
                        if (this.value === password1.value) {
                            indicator.innerHTML = '<small class="text-success"><i class="bi bi-check-circle"></i> كلمتا المرور متطابقتان</small>';
                        } else {
                            indicator.innerHTML = '<small class="text-danger"><i class="bi bi-x-circle"></i> كلمتا المرور غير متطابقتان</small>';
                        }
                        
                        this.parentNode.appendChild(indicator);
                    }
                });
            }
        });
    </script>
</body>
</html>
