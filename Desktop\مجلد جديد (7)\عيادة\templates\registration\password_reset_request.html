<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نسيت كلمة المرور - نظام العيادة الطبية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .reset-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card reset-card">
                    <div class="card-body p-5">
                        <!-- Header -->
                        <div class="text-center mb-4">
                            <div class="bg-warning bg-opacity-10 rounded-circle p-3 d-inline-block mb-3">
                                <i class="bi bi-key fs-1 text-warning"></i>
                            </div>
                            <h2 class="fw-bold text-dark">نسيت كلمة المرور؟</h2>
                            <p class="text-muted">لا تقلق، سنساعدك في استعادة حسابك</p>
                        </div>

                        <!-- Messages -->
                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                    <i class="bi bi-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}x-circle{% else %}info-circle{% endif %}"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}

                        <!-- Reset Form -->
                        <form method="post" novalidate>
                            {% csrf_token %}
                            
                            <div class="mb-4">
                                <label for="{{ form.email.id_for_label }}" class="form-label">
                                    <i class="bi bi-envelope"></i> {{ form.email.label }}
                                </label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.email.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    <small>أدخل البريد الإلكتروني المرتبط بحسابك</small>
                                </div>
                            </div>

                            <div class="d-grid mb-4">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-send"></i> إرسال رابط الاستعادة
                                </button>
                            </div>
                        </form>

                        <!-- Instructions -->
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle"></i> كيف يعمل هذا؟</h6>
                            <ol class="mb-0 small">
                                <li>أدخل بريدك الإلكتروني أعلاه</li>
                                <li>سنرسل لك رابط إعادة تعيين كلمة المرور</li>
                                <li>اضغط على الرابط في البريد الإلكتروني</li>
                                <li>أدخل كلمة مرور جديدة</li>
                            </ol>
                        </div>

                        <!-- Back to Login -->
                        <div class="text-center">
                            <p class="text-muted mb-0">
                                تذكرت كلمة المرور؟ 
                                <a href="{% url 'login' %}" class="text-decoration-none fw-bold">
                                    <i class="bi bi-arrow-right"></i> العودة لتسجيل الدخول
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Email validation
        document.addEventListener('DOMContentLoaded', function() {
            const emailInput = document.getElementById('{{ form.email.id_for_label }}');
            
            if (emailInput) {
                emailInput.addEventListener('blur', function() {
                    const email = this.value;
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    
                    // Remove existing validation message
                    const existing = this.parentNode.querySelector('.email-validation');
                    if (existing) existing.remove();
                    
                    if (email && !emailRegex.test(email)) {
                        const validation = document.createElement('div');
                        validation.className = 'email-validation text-danger small mt-1';
                        validation.innerHTML = '<i class="bi bi-x-circle"></i> يرجى إدخال بريد إلكتروني صحيح';
                        this.parentNode.appendChild(validation);
                    }
                });
                
                // Clear validation on input
                emailInput.addEventListener('input', function() {
                    const existing = this.parentNode.querySelector('.email-validation');
                    if (existing) existing.remove();
                });
            }
        });
    </script>
</body>
</html>
