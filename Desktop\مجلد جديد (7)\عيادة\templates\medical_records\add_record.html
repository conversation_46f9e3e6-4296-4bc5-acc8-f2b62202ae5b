{% extends 'control_panels/base_dashboard.html' %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="add-record-card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-file-medical-alt me-2"></i>
                        إنشاء سجل طبي جديد
                    </h4>
                </div>
                <div class="card-body">
                    <form method="post" id="medicalRecordForm">
                        {% csrf_token %}

                        <!-- Patient Selection -->
                        <div class="section-header">
                            <h5><i class="fas fa-user me-2"></i>اختيار المريض</h5>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-8">
                                <label for="patient_search" class="form-label">البحث عن المريض *</label>
                                <div class="input-group position-relative">
                                    <input type="text" class="form-control" id="patient_search"
                                           placeholder="ابحث بالاسم، رقم المريض، أو رقم الهوية..."
                                           autocomplete="off">
                                    <button class="btn btn-outline-secondary" type="button" id="searchBtn" onclick="testSearch()">
                                        <i class="fas fa-search"></i>
                                    </button>
                                    <button class="btn btn-outline-info" type="button" onclick="createTestPatients()">
                                        <i class="fas fa-plus"></i> إنشاء بيانات تجريبية
                                    </button>
                                </div>
                                <input type="hidden" id="selected_patient_id" name="patient_id" required>
                                <div id="patient_results" class="search-results"></div>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">المريض المحدد</label>
                                <div id="selected_patient_info" class="selected-patient">
                                    <p class="text-muted">لم يتم اختيار مريض</p>
                                </div>
                            </div>
                        </div>

                        <!-- Record Details -->
                        <div class="section-header">
                            <h5><i class="fas fa-clipboard-list me-2"></i>تفاصيل السجل</h5>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="record_type" class="form-label">نوع السجل *</label>
                                <select class="form-control" id="record_type" name="record_type" required>
                                    <option value="">اختر نوع السجل</option>
                                    <option value="CONSULTATION">استشارة</option>
                                    <option value="DIAGNOSIS">تشخيص</option>
                                    <option value="TREATMENT">علاج</option>
                                    <option value="SURGERY">جراحة</option>
                                    <option value="EMERGENCY">طوارئ</option>
                                    <option value="FOLLOW_UP">متابعة</option>
                                    <option value="DISCHARGE">خروج</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="priority" class="form-label">الأولوية *</label>
                                <select class="form-control" id="priority" name="priority" required>
                                    <option value="NORMAL">عادي</option>
                                    <option value="LOW">منخفض</option>
                                    <option value="HIGH">عالي</option>
                                    <option value="URGENT">عاجل</option>
                                    <option value="CRITICAL">حرج</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="department" class="form-label">القسم *</label>
                                <select class="form-control" id="department" name="department_id" required>
                                    <option value="">اختر القسم</option>
                                    {% for dept in departments %}
                                        <option value="{{ dept.id }}">{{ dept.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <!-- Medical Content -->
                        <div class="section-header">
                            <h5><i class="fas fa-stethoscope me-2"></i>المحتوى الطبي</h5>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="chief_complaint" class="form-label">الشكوى الرئيسية *</label>
                                <textarea class="form-control" id="chief_complaint" name="chief_complaint"
                                          rows="3" required placeholder="اذكر الشكوى الرئيسية للمريض"></textarea>
                            </div>
                            <div class="col-md-6">
                                <label for="history_present_illness" class="form-label">تاريخ المرض الحالي *</label>
                                <textarea class="form-control" id="history_present_illness" name="history_of_present_illness"
                                          rows="3" required placeholder="تفاصيل تاريخ المرض الحالي"></textarea>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="past_medical_history" class="form-label">التاريخ الطبي السابق</label>
                                <textarea class="form-control" id="past_medical_history" name="past_medical_history"
                                          rows="3" placeholder="التاريخ الطبي السابق للمريض"></textarea>
                            </div>
                            <div class="col-md-6">
                                <label for="family_history" class="form-label">التاريخ العائلي</label>
                                <textarea class="form-control" id="family_history" name="family_history"
                                          rows="3" placeholder="التاريخ المرضي للعائلة"></textarea>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="social_history" class="form-label">التاريخ الاجتماعي</label>
                            <textarea class="form-control" id="social_history" name="social_history"
                                      rows="2" placeholder="التدخين، الكحول، المهنة، إلخ"></textarea>
                        </div>

                        <!-- Clinical Examination -->
                        <div class="section-header">
                            <h5><i class="fas fa-search me-2"></i>الفحص السريري</h5>
                        </div>

                        <div class="mb-3">
                            <label for="physical_examination" class="form-label">الفحص السريري *</label>
                            <textarea class="form-control" id="physical_examination" name="physical_examination"
                                      rows="4" required placeholder="نتائج الفحص السريري"></textarea>
                        </div>

                        <!-- Assessment and Plan -->
                        <div class="section-header">
                            <h5><i class="fas fa-clipboard-check me-2"></i>التقييم والخطة</h5>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="assessment" class="form-label">التقييم *</label>
                                <textarea class="form-control" id="assessment" name="assessment"
                                          rows="4" required placeholder="التقييم الطبي"></textarea>
                            </div>
                            <div class="col-md-6">
                                <label for="plan" class="form-label">الخطة العلاجية *</label>
                                <textarea class="form-control" id="plan" name="plan"
                                          rows="4" required placeholder="الخطة العلاجية المقترحة"></textarea>
                            </div>
                        </div>

                        <!-- Additional Options -->
                        <div class="section-header">
                            <h5><i class="fas fa-cog me-2"></i>خيارات إضافية</h5>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_confidential" name="is_confidential">
                                    <label class="form-check-label" for="is_confidential">
                                        <i class="fas fa-lock me-2"></i>
                                        سجل سري (محدود الوصول)
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_completed" name="is_completed">
                                    <label class="form-check-label" for="is_completed">
                                        <i class="fas fa-check-circle me-2"></i>
                                        السجل مكتمل (لا يمكن تعديله)
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="form-actions">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>
                                حفظ السجل الطبي
                            </button>
                            <button type="button" class="btn btn-outline-primary" onclick="saveDraft()">
                                <i class="fas fa-file-alt me-2"></i>
                                حفظ كمسودة
                            </button>
                            <a href="{% url 'medical_records:record_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.add-record-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.add-record-card .card-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 1.5rem;
}

.add-record-card .card-body {
    padding: 2rem;
}

.section-header {
    margin: 2rem 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.section-header:first-child {
    margin-top: 0;
}

.section-header h5 {
    color: #495057;
    margin: 0;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ced4da;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1050;
    display: none;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.search-result-item {
    padding: 0.75rem;
    border-bottom: 1px solid #f8f9fa;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.search-result-item:hover {
    background-color: #f8f9fa;
}

.search-result-item:last-child {
    border-bottom: none;
}

.selected-patient {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    min-height: 60px;
    display: flex;
    align-items: center;
}

.selected-patient.has-patient {
    background: #d4edda;
    border-color: #c3e6cb;
}

.patient-info {
    display: flex;
    flex-direction: column;
}

.patient-name {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
}

.patient-details {
    font-size: 0.875rem;
    color: #6c757d;
}

.form-check {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

.form-actions {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
    text-align: center;
}

.form-actions .btn {
    margin: 0 0.5rem;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
    transform: translateY(-1px);
}

@media (max-width: 768px) {
    .add-record-card .card-body {
        padding: 1rem;
    }

    .form-actions .btn {
        display: block;
        width: 100%;
        margin: 0.5rem 0;
    }

    .search-results {
        position: relative;
        border: 1px solid #ced4da;
        border-radius: 8px;
        margin-top: 0.5rem;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let searchTimeout;

document.getElementById('patient_search').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    const query = this.value.trim();

    console.log('تم إدخال:', query); // للتشخيص

    if (query.length < 2) {
        hideSearchResults();
        return;
    }

    // إظهار مؤشر التحميل
    const resultsDiv = document.getElementById('patient_results');
    resultsDiv.innerHTML = '<div class="search-result-item text-muted"><i class="fas fa-spinner fa-spin"></i> جاري البحث...</div>';
    resultsDiv.style.display = 'block';

    searchTimeout = setTimeout(() => {
        searchPatients(query);
    }, 300);
});

function searchPatients(query) {
    console.log('البحث عن:', query); // للتشخيص

    fetch(`/patients/search-ajax/?q=${encodeURIComponent(query)}`)
        .then(response => {
            console.log('استجابة الخادم:', response); // للتشخيص
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('البيانات المستلمة:', data); // للتشخيص
            displaySearchResults(data.patients);
        })
        .catch(error => {
            console.error('خطأ في البحث:', error);
            document.getElementById('patient_results').innerHTML = '<div class="search-result-item text-danger">حدث خطأ في البحث: ' + error.message + '</div>';
            document.getElementById('patient_results').style.display = 'block';
        });
}

function displaySearchResults(patients) {
    const resultsDiv = document.getElementById('patient_results');

    console.log('عرض النتائج:', patients); // للتشخيص

    if (!patients || patients.length === 0) {
        resultsDiv.innerHTML = '<div class="search-result-item text-muted">لا توجد نتائج</div>';
    } else {
        resultsDiv.innerHTML = patients.map(patient => `
            <div class="search-result-item" onclick="selectPatient(${patient.id}, '${patient.full_name}', '${patient.patient_id}', '${patient.age}', '${patient.gender}')">
                <div class="patient-info">
                    <div class="patient-name">${patient.full_name}</div>
                    <div class="patient-details">رقم المريض: ${patient.patient_id} | العمر: ${patient.age} | الجنس: ${patient.gender}</div>
                </div>
            </div>
        `).join('');
    }

    resultsDiv.style.display = 'block';
}

function selectPatient(id, name, patientId, age, gender) {
    document.getElementById('selected_patient_id').value = id;
    document.getElementById('patient_search').value = name;

    const selectedDiv = document.getElementById('selected_patient_info');
    selectedDiv.innerHTML = `
        <div class="d-flex justify-content-between align-items-center">
            <div class="patient-info">
                <div class="patient-name">${name}</div>
                <div class="patient-details">رقم المريض: ${patientId} | العمر: ${age} | الجنس: ${gender}</div>
            </div>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearPatientSelection()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    selectedDiv.classList.add('has-patient');

    hideSearchResults();
}

function hideSearchResults() {
    document.getElementById('patient_results').style.display = 'none';
}

function clearPatientSelection() {
    document.getElementById('selected_patient_id').value = '';
    document.getElementById('patient_search').value = '';
    document.getElementById('selected_patient_info').classList.remove('has-patient');
    document.getElementById('selected_patient_info').innerHTML = '<p class="text-muted">لم يتم اختيار مريض</p>';
    document.getElementById('patient_search').focus();
}

// إخفاء النتائج عند النقر خارجها
document.addEventListener('click', function(event) {
    if (!event.target.closest('#patient_search') && !event.target.closest('#patient_results')) {
        hideSearchResults();
    }
});

function saveDraft() {
    const form = document.getElementById('medicalRecordForm');
    const formData = new FormData(form);
    formData.append('is_draft', 'true');

    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم حفظ المسودة بنجاح');
        } else {
            alert('حدث خطأ في حفظ المسودة');
        }
    });
}

// التحقق من صحة النموذج
document.getElementById('medicalRecordForm').addEventListener('submit', function(event) {
    const patientId = document.getElementById('selected_patient_id').value;

    if (!patientId) {
        event.preventDefault();
        alert('يرجى اختيار مريض');
        document.getElementById('patient_search').focus();
        return false;
    }
});

// دالة اختبار البحث
function testSearch() {
    const query = document.getElementById('patient_search').value.trim();
    if (query.length < 2) {
        alert('يرجى إدخال نص للبحث (حرفين على الأقل)');
        return;
    }

    console.log('اختبار البحث عن:', query);
    searchPatients(query);
}

// دالة إنشاء بيانات تجريبية
function createTestPatients() {
    if (confirm('هل تريد إنشاء بيانات مرضى تجريبية؟\nسيتم إنشاء 20 مريض تجريبي.')) {
        fetch('/patients/create-test-data/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`تم إنشاء ${data.count} مريض تجريبي بنجاح!`);
            } else {
                alert('حدث خطأ في إنشاء البيانات التجريبية');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            alert('حدث خطأ في إنشاء البيانات التجريبية');
        });
    }
}
</script>
{% endblock %}
