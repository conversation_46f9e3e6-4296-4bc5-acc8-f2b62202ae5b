{% extends 'base.html' %}

{% block title %}قائمة المرضى - نظام العيادة الطبية{% endblock %}

{% block extra_css %}
<style>
.stat-card {
    border: none;
    border-radius: 15px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.stat-card.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.bg-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.stat-card.bg-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card.bg-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card.bg-secondary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.patient-card {
    border: none;
    border-radius: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
}

.patient-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.search-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
}

.filter-btn {
    border-radius: 20px;
    padding: 0.5rem 1rem;
    margin: 0.2rem;
    transition: all 0.3s ease;
}

.filter-btn.active {
    background: #667eea;
    color: white;
    transform: scale(1.05);
}

.phone-link {
    color: #28a745;
    text-decoration: none;
}

.phone-link:hover {
    color: #20c997;
    text-decoration: underline;
}

.age-badge {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    color: #333;
    font-weight: 500;
}

.status-badge {
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.patient-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.2rem;
}
</style>
{% endblock %}
{% block content %}
<!-- Page Header -->
<div class="bg-gradient-success text-white rounded-3 p-4 mb-4 shadow" style="margin-top: 50px;">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    <div class="bg-white bg-opacity-25 rounded-circle p-3 me-3">
                        <i class="bi bi-people fs-2 text-white"></i>
                    </div>
                    <div>
                        <h1 class="h2 mb-1 text-white">إدارة المرضى</h1>
                        <p class="mb-0 text-white-50">عرض وإدارة جميع المرضى المسجلين في النظام</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{% url 'patient_create' %}" class="btn btn-light btn-lg">
                    <i class="bi bi-person-plus"></i>
                    <span class="d-none d-sm-inline">إضافة مريض جديد</span>
                    <span class="d-inline d-sm-none">إضافة</span>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="card-title text-white-50 mb-1">إجمالي المرضى</h6>
                        <h2 class="mb-0">{{ total_patients }}</h2>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-people fs-1 text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card bg-success text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="card-title text-white-50 mb-1">المرضى النشطون</h6>
                        <h2 class="mb-0">{{ active_patients }}</h2>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-person-check fs-1 text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card bg-info text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="card-title text-white-50 mb-1">الذكور</h6>
                        <h2 class="mb-0">{{ male_patients }}</h2>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-gender-male fs-1 text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="card-title text-white-50 mb-1">الإناث</h6>
                        <h2 class="mb-0">{{ female_patients }}</h2>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-gender-female fs-1 text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Age Statistics -->
<div class="row mb-4">
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="card stat-card bg-secondary text-white">
            <div class="card-body ">
                <div class="d-flex align-items-center">
                <div class="flex-grow-1">

                <h5 class="text-white-50 mb-1">الأطفال (أقل من 18)</h5>
                <h3 class="mb-0">{{ children }}</h3>
            </div>
            <div class="ms-3">
                        <i class="bi bi-emoji-smile fs-2 text-white-50 mb-2"></i>
            </div>
            </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-3">
        <div class="card stat-card bg-primary text-white">
            <div class="card-body ">
                <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                {% comment %} <i class="bi bi-person fs-2 text-white-50 mb-2"></i> {% endcomment %}
                <h5 class="text-white-50 mb-1">البالغون (18-60)</h5>
                <h3 class="mb-0">{{ adults }}</h3>
            </div>
            <div class="ms-3">
                        <i class="bi bi-person fs-2 text-white-50 mb-2"></i>
            </div>
            </div>
            </div>

        </div>
    </div>

    <div class="col-lg-4 col-md-4 mb-3">
        <div class="card stat-card bg-info text-white">
            <div class="card-body text-center">
                <i class="bi bi-person-walking fs-2 text-white-50 mb-2"></i>
                <h5 class="text-white-50 mb-1">كبار السن (+60)</h5>
                <h3 class="mb-0">{{ elderly }}</h3>
            </div>
        </div>
    </div>
</div>

<!-- Advanced Search Form -->
<div class="card search-card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0 text-white">
            <i class="bi bi-search"></i> البحث والتصفية المتقدمة
        </h5>
    </div>
    <div class="card-body">
        <form method="get" id="searchForm">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-3">
                    <label class="form-label text-white-50">البحث العام</label>
                    <input type="text" name="search" class="form-control"
                           placeholder="الاسم، الهاتف، البريد الإلكتروني..."
                           value="{{ search_query }}">
                </div>

                <div class="col-lg-2 col-md-6 mb-3">
                    <label class="form-label text-white-50">الجنس</label>
                    <select name="gender" class="form-control">
                        <option value="">الكل</option>
                        <option value="M" {% if gender_filter == 'M' %}selected{% endif %}>ذكر</option>
                        <option value="F" {% if gender_filter == 'F' %}selected{% endif %}>أنثى</option>
                    </select>
                </div>

                <div class="col-lg-2 col-md-6 mb-3">
                    <label class="form-label text-white-50">الحالة</label>
                    <select name="status" class="form-control">
                        <option value="">الكل</option>
                        <option value="active" {% if status_filter == 'active' %}selected{% endif %}>نشط</option>
                        <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>غير نشط</option>
                    </select>
                </div>

                <div class="col-lg-2 col-md-6 mb-3">
                    <label class="form-label text-white-50">الفئة العمرية</label>
                    <select name="age_range" class="form-control">
                        <option value="">الكل</option>
                        <option value="children" {% if age_range == 'children' %}selected{% endif %}>أطفال (أقل من 18)</option>
                        <option value="adults" {% if age_range == 'adults' %}selected{% endif %}>بالغون (18-60)</option>
                        <option value="elderly" {% if age_range == 'elderly' %}selected{% endif %}>كبار السن (+60)</option>
                    </select>
                </div>

                <div class="col-lg-2 col-md-6 mb-3">
                    <label class="form-label text-white-50">ترتيب حسب</label>
                    <select name="sort" class="form-control">
                        <option value="-created_at" {% if sort_by == '-created_at' %}selected{% endif %}>الأحدث</option>
                        <option value="created_at" {% if sort_by == 'created_at' %}selected{% endif %}>الأقدم</option>
                        <option value="first_name" {% if sort_by == 'first_name' %}selected{% endif %}>الاسم (أ-ي)</option>
                        <option value="-first_name" {% if sort_by == '-first_name' %}selected{% endif %}>الاسم (ي-أ)</option>
                        <option value="patient_id" {% if sort_by == 'patient_id' %}selected{% endif %}>رقم المريض (تصاعدي)</option>
                        <option value="-patient_id" {% if sort_by == '-patient_id' %}selected{% endif %}>رقم المريض (تنازلي)</option>
                    </select>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <button type="submit" class="btn btn-light me-2">
                        <i class="bi bi-search"></i> بحث
                    </button>
                    <a href="{% url 'patient_list' %}" class="btn btn-outline-success btn-sm">
                        <i class="bi bi-arrow-clockwise"></i> إعادة تعيين
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Quick Filter Buttons -->
<div class="mb-3">
    <div class="d-flex flex-wrap">
        <a href="?status=active" class="btn filter-btn btn-outline-success {% if status_filter == 'active' %}active{% endif %}">
            <i class="bi bi-person-check"></i> المرضى النشطون
        </a>
        <a href="?gender=M" class="btn filter-btn btn-outline-info {% if gender_filter == 'M' %}active{% endif %}">
            <i class="bi bi-gender-male"></i> الذكور
        </a>
        <a href="?gender=F" class="btn filter-btn btn-outline-warning {% if gender_filter == 'F' %}active{% endif %}">
            <i class="bi bi-gender-female"></i> الإناث
        </a>
        <a href="?age_range=children" class="btn filter-btn btn-outline-primary {% if age_range == 'children' %}active{% endif %}">
            <i class="bi bi-emoji-smile"></i> الأطفال
        </a>
        <a href="?age_range=elderly" class="btn filter-btn btn-outline-secondary {% if age_range == 'elderly' %}active{% endif %}">
            <i class="bi bi-person-walking"></i> كبار السن
        </a>
    </div>
</div>

<!-- Results Summary -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <div>
        <h5 class="mb-0">نتائج البحث</h5>
        <small class="text-muted">
            عرض {{ page_obj.start_index }}-{{ page_obj.end_index }} من أصل {{ page_obj.paginator.count }} مريض
        </small>
    </div>
    <div class="btn-group" role="group">
        <input type="radio" class="btn-check" name="viewMode" id="cardView" autocomplete="off" checked>
        <label class="btn btn-outline-primary" for="cardView">
            <i class="bi bi-grid-3x3-gap"></i> كروت
        </label>

        <input type="radio" class="btn-check" name="viewMode" id="tableView" autocomplete="off">
        <label class="btn btn-outline-primary" for="tableView">
            <i class="bi bi-table"></i> جدول
        </label>
    </div>
</div>

<!-- Patients Display -->
{% if page_obj %}
    <!-- Card View -->
    <div id="cardViewContainer" class="row">
        {% for patient in page_obj %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card patient-card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-start mb-3">
                            <div class="patient-avatar me-3">
                                {{ patient.first_name|first }}{{ patient.last_name|first }}
                            </div>
                            <div class="flex-grow-1">
                                <h5 class="card-title mb-1">
                                    <a href="{% url 'patient_detail' patient.pk %}" class="text-decoration-none">
                                        {{ patient.full_name }}
                                    </a>
                                </h5>
                                <p class="text-muted mb-0">رقم المريض: {{ patient.patient_id }}</p>
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="bi bi-three-dots-vertical"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'patient_detail' patient.pk %}">
                                        <i class="bi bi-eye"></i> عرض التفاصيل
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'patient_update' patient.pk %}">
                                        <i class="bi bi-pencil"></i> تعديل
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'appointment_create' %}?patient_id={{ patient.pk }}">
                                        <i class="bi bi-calendar-plus"></i> حجز موعد
                                    </a></li>
                                </ul>
                            </div>
                        </div>

                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <div class="border-end">
                                    <div class="h6 mb-0">{{ patient.age }}</div>
                                    <small class="text-muted">سنة</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border-end">
                                    <div class="h6 mb-0">
                                        {% if patient.gender == 'M' %}
                                            <i class="bi bi-gender-male text-primary"></i>
                                        {% else %}
                                            <i class="bi bi-gender-female text-warning"></i>
                                        {% endif %}
                                    </div>
                                    <small class="text-muted">
                                        {% if patient.gender == 'M' %}ذكر{% else %}أنثى{% endif %}
                                    </small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="h6 mb-0">
                                    {% if patient.is_active %}
                                        <i class="bi bi-check-circle text-success"></i>
                                    {% else %}
                                        <i class="bi bi-x-circle text-danger"></i>
                                    {% endif %}
                                </div>
                                <small class="text-muted">
                                    {% if patient.is_active %}نشط{% else %}غير نشط{% endif %}
                                </small>
                            </div>
                        </div>

                        <div class="mb-2">
                            <i class="bi bi-telephone text-success me-2"></i>
                            <a href="tel:{{ patient.phone }}" class="phone-link">{{ patient.phone }}</a>
                        </div>

                        {% if patient.email %}
                        <div class="mb-2">
                            <i class="bi bi-envelope text-info me-2"></i>
                            <a href="mailto:{{ patient.email }}" class="text-decoration-none">{{ patient.email|truncatechars:25 }}</a>
                        </div>
                        {% endif %}

                        <div class="mb-3">
                            <i class="bi bi-calendar text-muted me-2"></i>
                            <small class="text-muted">مسجل في {{ patient.created_at|date:"d/m/Y" }}</small>
                        </div>

                        <div class="d-flex gap-2">
                            <a href="{% url 'patient_detail' patient.pk %}" class="btn btn-primary btn-sm flex-fill">
                                <i class="bi bi-eye"></i> عرض
                            </a>
                            <a href="{% url 'appointment_create' %}?patient_id={{ patient.pk }}" class="btn btn-success btn-sm flex-fill">
                                <i class="bi bi-calendar-plus"></i> موعد
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>

    <!-- Table View (Hidden by default) -->
    <div id="tableViewContainer" class="d-none">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>رقم المريض</th>
                        <th>الاسم الكامل</th>
                        <th>العمر</th>
                        <th>الجنس</th>
                        <th>رقم الهاتف</th>
                        <th>تاريخ التسجيل</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for patient in page_obj %}
                        <tr>
                            <td>{{ patient.patient_id }}</td>
                            <td>
                                <a href="{% url 'patient_detail' patient.pk %}" class="text-decoration-none">
                                    {{ patient.full_name }}
                                </a>
                            </td>
                            <td><span class="age-badge">{{ patient.age }} سنة</span></td>
                            <td>
                                {% if patient.gender == 'M' %}
                                    <span class="badge bg-primary">ذكر</span>
                                {% else %}
                                    <span class="badge bg-warning">أنثى</span>
                                {% endif %}
                            </td>
                            <td><a href="tel:{{ patient.phone }}" class="phone-link">{{ patient.phone }}</a></td>
                            <td>{{ patient.created_at|date:"d/m/Y" }}</td>
                            <td>
                                {% if patient.is_active %}
                                    <span class="status-badge bg-success text-white">نشط</span>
                                {% else %}
                                    <span class="status-badge bg-danger text-white">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'patient_detail' patient.pk %}"
                                       class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{% url 'patient_update' patient.pk %}"
                                       class="btn btn-sm btn-outline-warning" title="تعديل">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <a href="{% url 'appointment_create' %}?patient_id={{ patient.pk }}"
                                       class="btn btn-sm btn-outline-success" title="حجز موعد">
                                        <i class="bi bi-calendar-plus"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
{% else %}
    <div class="text-center py-5">
        <i class="bi bi-people fs-1 text-muted mb-3"></i>
        <h4 class="text-muted">لا توجد نتائج</h4>
        <p class="text-muted">لم يتم العثور على مرضى يطابقون معايير البحث</p>
        <a href="{% url 'patient_list' %}" class="btn btn-primary">
            <i class="bi bi-arrow-clockwise"></i> عرض جميع المرضى
        </a>
    </div>
{% endif %}

<!-- Pagination -->
{% if page_obj.has_other_pages %}
    <nav aria-label="Patient pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                        <i class="bi bi-chevron-double-left"></i> الأولى
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                        <i class="bi bi-chevron-left"></i> السابقة
                    </a>
                </li>
            {% endif %}

            {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                            {{ num }}
                        </a>
                    </li>
                {% endif %}
            {% endfor %}

            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                        التالية <i class="bi bi-chevron-right"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                        الأخيرة <i class="bi bi-chevron-double-right"></i>
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Toggle between card and table view
    $('input[name="viewMode"]').change(function() {
        if ($(this).attr('id') === 'cardView') {
            $('#cardViewContainer').removeClass('d-none');
            $('#tableViewContainer').addClass('d-none');
            localStorage.setItem('patientViewMode', 'card');
        } else {
            $('#cardViewContainer').addClass('d-none');
            $('#tableViewContainer').removeClass('d-none');
            localStorage.setItem('patientViewMode', 'table');
        }
    });

    // Restore view mode from localStorage
    const savedViewMode = localStorage.getItem('patientViewMode');
    if (savedViewMode === 'table') {
        $('#tableView').prop('checked', true);
        $('#cardViewContainer').addClass('d-none');
        $('#tableViewContainer').removeClass('d-none');
    }

    // Auto-submit search form on filter change
    $('#searchForm select').change(function() {
        $('#searchForm').submit();
    });

    // Enhanced search with debounce
    let searchTimeout;
    $('input[name="search"]').on('input', function() {
        clearTimeout(searchTimeout);
        const searchValue = $(this).val();

        searchTimeout = setTimeout(function() {
            if (searchValue.length >= 3 || searchValue.length === 0) {
                $('#searchForm').submit();
            }
        }, 500);
    });

    // Animate statistics cards on page load
    $('.stat-card').each(function(index) {
        $(this).css('opacity', '0').css('transform', 'translateY(20px)');
        $(this).delay(index * 100).animate({
            opacity: 1
        }, 500).css('transform', 'translateY(0)');
    });

    // Animate patient cards on page load
    $('.patient-card').each(function(index) {
        $(this).css('opacity', '0').css('transform', 'translateY(20px)');
        $(this).delay(index * 50).animate({
            opacity: 1
        }, 300).css('transform', 'translateY(0)');
    });

    // Add loading state to buttons
    $('.btn').click(function() {
        const $btn = $(this);
        if (!$btn.hasClass('dropdown-toggle')) {
            const originalText = $btn.html();
            $btn.html('<i class="bi bi-hourglass-split"></i> جاري التحميل...');
            $btn.prop('disabled', true);

            setTimeout(function() {
                $btn.html(originalText);
                $btn.prop('disabled', false);
            }, 2000);
        }
    });

    // Smooth scroll to top
    $(window).scroll(function() {
        if ($(this).scrollTop() > 100) {
            if ($('#scrollToTop').length === 0) {
                $('body').append('<button id="scrollToTop" class="btn btn-primary position-fixed" style="bottom: 20px; right: 20px; z-index: 1000; border-radius: 50%; width: 50px; height: 50px;"><i class="bi bi-arrow-up"></i></button>');
            }
        } else {
            $('#scrollToTop').remove();
        }
    });

    $(document).on('click', '#scrollToTop', function() {
        $('html, body').animate({scrollTop: 0}, 500);
    });

    // Phone number formatting
    $('a[href^="tel:"]').each(function() {
        const phone = $(this).text();
        if (phone.length === 11 && phone.startsWith('01')) {
            const formatted = phone.replace(/(\d{4})(\d{3})(\d{4})/, '$1 $2 $3');
            $(this).text(formatted);
        }
    });

    // Add tooltips to action buttons
    $('[title]').tooltip();

    // Real-time search suggestions (if needed)
    $('input[name="search"]').on('focus', function() {
        // Can add search suggestions here
    });
});
</script>
{% endblock %}
