{% extends 'control_panels/base_dashboard.html' %}
{% load static %}

{% block title %}إضافة فحص مختبري{% endblock %}

{% block extra_css %}
<style>
    .add-lab-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .lab-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-bottom: 30px;
        overflow: hidden;
        max-width: 1000px;
        margin: 0 auto;
    }
    
    .lab-header {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        color: white;
        padding: 30px;
        text-align: center;
    }
    
    .lab-title {
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 10px;
    }
    
    .form-section {
        background: white;
        border-radius: 10px;
        padding: 30px;
        margin: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    
    .section-header {
        margin-bottom: 25px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e9ecef;
    }
    
    .section-title {
        color: #495057;
        font-size: 1.3rem;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }
    
    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 12px 15px;
        transition: all 0.3s ease;
        font-size: 1rem;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #17a2b8;
        box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
    }
    
    .required {
        color: #dc3545;
    }
    
    .patient-search-container {
        position: relative;
    }
    
    .search-results {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ced4da;
        border-top: none;
        border-radius: 0 0 8px 8px;
        max-height: 300px;
        overflow-y: auto;
        z-index: 1050;
        display: none;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    }
    
    .search-result-item {
        padding: 12px 15px;
        border-bottom: 1px solid #f8f9fa;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }
    
    .search-result-item:hover {
        background-color: #f8f9fa;
    }
    
    .search-result-item:last-child {
        border-bottom: none;
    }
    
    .patient-name {
        font-weight: 600;
        color: #495057;
        margin-bottom: 3px;
    }
    
    .patient-details {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .selected-patient {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        min-height: 70px;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
    }
    
    .selected-patient.has-patient {
        background: #d4edda;
        border-color: #c3e6cb;
    }
    
    .test-type-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
        margin-top: 15px;
    }
    
    .test-type-card {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
    }
    
    .test-type-card:hover {
        border-color: #17a2b8;
        background: #f8f9fa;
    }
    
    .test-type-card.selected {
        border-color: #17a2b8;
        background: #e7f3ff;
    }
    
    .test-type-icon {
        font-size: 2rem;
        color: #17a2b8;
        margin-bottom: 10px;
    }
    
    .test-type-name {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }
    
    .test-type-desc {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .form-actions {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e9ecef;
        text-align: center;
    }
    
    .btn-primary-action {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 25px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-size: 1.1rem;
    }
    
    .btn-primary-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(23, 162, 184, 0.3);
        color: white;
    }
    
    .btn-secondary {
        background: #6c757d;
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 25px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-size: 1.1rem;
        margin-left: 15px;
    }
    
    .btn-secondary:hover {
        background: #5a6268;
        color: white;
        transform: translateY(-2px);
    }
    
    @media (max-width: 768px) {
        .test-type-grid {
            grid-template-columns: 1fr;
        }
        
        .form-actions .btn-primary-action,
        .form-actions .btn-secondary {
            display: block;
            width: 100%;
            margin: 10px 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="add-lab-container">
    <div class="container-fluid">
        <!-- رأس الصفحة -->
        <div class="lab-card">
            <div class="lab-header">
                <h1 class="lab-title">
                    <i class="fas fa-flask me-3"></i>
                    إضافة فحص مختبري جديد
                </h1>
                <p>طلب فحص مختبري للمريض</p>
            </div>
        </div>
        
        <!-- نموذج إضافة الفحص -->
        <div class="lab-card">
            <div class="form-section">
                <form method="post" id="labTestForm">
                    {% csrf_token %}
                    
                    <!-- اختيار المريض -->
                    <div class="section-header">
                        <h3 class="section-title">
                            <i class="fas fa-user"></i>
                            اختيار المريض
                        </h3>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <label for="patient_search" class="form-label">البحث عن المريض <span class="required">*</span></label>
                            <div class="patient-search-container">
                                <input type="text" class="form-control" id="patient_search"
                                       placeholder="ابحث بالاسم، رقم المريض، أو رقم الهوية..."
                                       autocomplete="off">
                                <input type="hidden" id="selected_patient_id" name="patient_id" required>
                                <div id="patient_results" class="search-results"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">المريض المحدد</label>
                            <div id="selected_patient_info" class="selected-patient">
                                <p class="text-muted mb-0">لم يتم اختيار مريض</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- نوع الفحص -->
                    <div class="section-header">
                        <h3 class="section-title">
                            <i class="fas fa-vial"></i>
                            نوع الفحص المختبري
                        </h3>
                    </div>
                    
                    <div class="test-type-grid">
                        <div class="test-type-card" onclick="selectTestType('BLOOD', this)">
                            <div class="test-type-icon">
                                <i class="fas fa-tint"></i>
                            </div>
                            <div class="test-type-name">فحص الدم</div>
                            <div class="test-type-desc">تحليل مكونات الدم</div>
                        </div>
                        
                        <div class="test-type-card" onclick="selectTestType('URINE', this)">
                            <div class="test-type-icon">
                                <i class="fas fa-flask"></i>
                            </div>
                            <div class="test-type-name">فحص البول</div>
                            <div class="test-type-desc">تحليل عينة البول</div>
                        </div>
                        
                        <div class="test-type-card" onclick="selectTestType('STOOL', this)">
                            <div class="test-type-icon">
                                <i class="fas fa-microscope"></i>
                            </div>
                            <div class="test-type-name">فحص البراز</div>
                            <div class="test-type-desc">تحليل عينة البراز</div>
                        </div>
                        
                        <div class="test-type-card" onclick="selectTestType('CULTURE', this)">
                            <div class="test-type-icon">
                                <i class="fas fa-bacteria"></i>
                            </div>
                            <div class="test-type-name">مزرعة</div>
                            <div class="test-type-desc">زراعة البكتيريا</div>
                        </div>
                        
                        <div class="test-type-card" onclick="selectTestType('BIOCHEMISTRY', this)">
                            <div class="test-type-icon">
                                <i class="fas fa-atom"></i>
                            </div>
                            <div class="test-type-name">كيمياء حيوية</div>
                            <div class="test-type-desc">فحوصات كيميائية</div>
                        </div>
                        
                        <div class="test-type-card" onclick="selectTestType('HORMONE', this)">
                            <div class="test-type-icon">
                                <i class="fas fa-dna"></i>
                            </div>
                            <div class="test-type-name">هرمونات</div>
                            <div class="test-type-desc">فحص الهرمونات</div>
                        </div>
                    </div>
                    
                    <input type="hidden" id="selected_test_type" name="test_type" required>
                    
                    <!-- تفاصيل الفحص -->
                    <div class="section-header mt-4">
                        <h3 class="section-title">
                            <i class="fas fa-clipboard-list"></i>
                            تفاصيل الفحص
                        </h3>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="test_name" class="form-label">اسم الفحص <span class="required">*</span></label>
                            <input type="text" class="form-control" id="test_name" name="test_name" required
                                   placeholder="مثال: تحليل صورة دم كاملة">
                        </div>
                        <div class="col-md-6">
                            <label for="priority" class="form-label">الأولوية <span class="required">*</span></label>
                            <select class="form-select" id="priority" name="priority" required>
                                <option value="NORMAL">عادي</option>
                                <option value="HIGH">عالي</option>
                                <option value="URGENT">عاجل</option>
                                <option value="STAT">فوري</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="requested_by" class="form-label">طلب بواسطة</label>
                            <input type="text" class="form-control" id="requested_by" name="requested_by"
                                   value="{{ request.user.get_full_name }}" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="department" class="form-label">القسم الطالب</label>
                            <select class="form-select" id="department" name="department_id">
                                <option value="">اختر القسم</option>
                                {% for dept in departments %}
                                    <option value="{{ dept.id }}">{{ dept.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="clinical_notes" class="form-label">الملاحظات السريرية</label>
                        <textarea class="form-control" id="clinical_notes" name="clinical_notes"
                                  rows="3" placeholder="أي ملاحظات سريرية مهمة للفحص"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="special_instructions" class="form-label">تعليمات خاصة</label>
                        <textarea class="form-control" id="special_instructions" name="special_instructions"
                                  rows="2" placeholder="تعليمات خاصة للمختبر"></textarea>
                    </div>
                    
                    <!-- إجراءات النموذج -->
                    <div class="form-actions">
                        <button type="submit" class="btn-primary-action">
                            <i class="fas fa-paper-plane"></i>
                            إرسال طلب الفحص
                        </button>
                        <a href="{% url 'medical_records:record_list' %}" class="btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let searchTimeout;

// البحث عن المرضى
document.getElementById('patient_search').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    const query = this.value.trim();

    if (query.length < 2) {
        hideSearchResults();
        return;
    }

    // إظهار مؤشر التحميل
    const resultsDiv = document.getElementById('patient_results');
    resultsDiv.innerHTML = '<div class="search-result-item text-muted"><i class="fas fa-spinner fa-spin"></i> جاري البحث...</div>';
    resultsDiv.style.display = 'block';

    searchTimeout = setTimeout(() => {
        searchPatients(query);
    }, 300);
});

function searchPatients(query) {
    fetch(`/patients/search-ajax/?q=${encodeURIComponent(query)}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            displaySearchResults(data.patients);
        })
        .catch(error => {
            console.error('خطأ في البحث:', error);
            document.getElementById('patient_results').innerHTML = '<div class="search-result-item text-danger">حدث خطأ في البحث</div>';
            document.getElementById('patient_results').style.display = 'block';
        });
}

function displaySearchResults(patients) {
    const resultsDiv = document.getElementById('patient_results');

    if (!patients || patients.length === 0) {
        resultsDiv.innerHTML = '<div class="search-result-item text-muted">لا توجد نتائج</div>';
    } else {
        resultsDiv.innerHTML = patients.map(patient => `
            <div class="search-result-item" onclick="selectPatient(${patient.id}, '${patient.full_name}', '${patient.patient_id}', '${patient.age}', '${patient.gender}')">
                <div class="patient-name">${patient.full_name}</div>
                <div class="patient-details">رقم المريض: ${patient.patient_id} | العمر: ${patient.age} | الجنس: ${patient.gender}</div>
            </div>
        `).join('');
    }

    resultsDiv.style.display = 'block';
}

function selectPatient(id, name, patientId, age, gender) {
    document.getElementById('selected_patient_id').value = id;
    document.getElementById('patient_search').value = name;

    const selectedDiv = document.getElementById('selected_patient_info');
    selectedDiv.innerHTML = `
        <div class="d-flex justify-content-between align-items-center w-100">
            <div>
                <div class="patient-name">${name}</div>
                <div class="patient-details">رقم المريض: ${patientId} | العمر: ${age} | الجنس: ${gender}</div>
            </div>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearPatientSelection()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    selectedDiv.classList.add('has-patient');

    hideSearchResults();
}

function hideSearchResults() {
    document.getElementById('patient_results').style.display = 'none';
}

function clearPatientSelection() {
    document.getElementById('selected_patient_id').value = '';
    document.getElementById('patient_search').value = '';
    document.getElementById('selected_patient_info').classList.remove('has-patient');
    document.getElementById('selected_patient_info').innerHTML = '<p class="text-muted mb-0">لم يتم اختيار مريض</p>';
    document.getElementById('patient_search').focus();
}

// اختيار نوع الفحص
function selectTestType(type, element) {
    // إزالة التحديد من جميع البطاقات
    document.querySelectorAll('.test-type-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // تحديد البطاقة المختارة
    element.classList.add('selected');
    document.getElementById('selected_test_type').value = type;
}

// إخفاء النتائج عند النقر خارجها
document.addEventListener('click', function(event) {
    if (!event.target.closest('.patient-search-container')) {
        hideSearchResults();
    }
});

// التحقق من صحة النموذج
document.getElementById('labTestForm').addEventListener('submit', function(event) {
    const patientId = document.getElementById('selected_patient_id').value;
    const testType = document.getElementById('selected_test_type').value;

    if (!patientId) {
        event.preventDefault();
        alert('يرجى اختيار مريض');
        document.getElementById('patient_search').focus();
        return false;
    }
    
    if (!testType) {
        event.preventDefault();
        alert('يرجى اختيار نوع الفحص');
        return false;
    }
});
</script>
{% endblock %}
