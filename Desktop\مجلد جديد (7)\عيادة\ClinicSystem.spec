# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['run_clinic.py'],
    pathex=[],
    binaries=[],
    datas=[('templates', 'templates'), ('db.sqlite3', '.')],
    hiddenimports=['django.core.management.commands.runserver', 'clinic.models', 'clinic.views', 'clinic.forms', 'clinic.urls', 'clinic_system.settings'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='ClinicSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
