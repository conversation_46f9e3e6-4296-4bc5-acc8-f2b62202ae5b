{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }} - نظام العيادة الطبية{% endblock %}

{% block extra_css %}
<style>
.patient-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    transition: all 0.3s ease;
}

.patient-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.form-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 1.5rem;
}

.info-card {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    border: none;
    color: white;
}

.medical-info-card {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border: none;
    color: #333;
}

.tips-card {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border: none;
    color: #333;
}

.age-calculator {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.character-counter {
    font-size: 0.8rem;
    color: #6c757d;
}
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom" style="margin-top: 50px;">
    <h1 class="h2">
        <i class="bi bi-person-plus"></i>
        {{ title }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'patient_list' %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right"></i> العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card patient-card">
            <div class="card-header form-section">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person-vcard"></i> تسجيل مريض جديد
                </h5>
            </div>
            <div class="card-body">
                <form method="post" id="patientForm">
                    {% csrf_token %}
                    {% crispy form %}
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- حاسبة العمر -->
        <div class="card patient-card info-card mb-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-calculator"></i> حاسبة العمر
                </h5>
            </div>
            <div class="card-body">
                <div class="age-calculator">
                    <div class="text-center">
                        <div class="h4 mb-1" id="calculatedAge">-- سنة</div>
                        <small class="text-muted">العمر الحالي</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- نصائح وإرشادات -->
        <div class="card patient-card tips-card mb-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i> إرشادات التسجيل
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-light">
                    <h6><i class="bi bi-lightbulb text-warning"></i> نصائح مهمة:</h6>
                    <ul class="mb-0 small">
                        <li>تأكد من صحة رقم الهاتف للتواصل</li>
                        <li>أدخل التاريخ المرضي بدقة</li>
                        <li>اذكر جميع الحساسيات المعروفة</li>
                        <li>تأكد من بيانات جهة الاتصال في الطوارئ</li>
                        <li>أدخل فصيلة الدم إذا كانت معروفة</li>
                        <li>اذكر الأمراض المزمنة والأدوية الحالية</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- معلومات طبية سريعة -->
        <div class="card patient-card medical-info-card mb-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-heart-pulse"></i> معلومات طبية مهمة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border rounded p-2">
                            <i class="bi bi-droplet text-danger"></i>
                            <div class="small">فصيلة الدم</div>
                            <div class="fw-bold" id="bloodTypeDisplay">--</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-2">
                            <i class="bi bi-shield-check text-success"></i>
                            <div class="small">التأمين</div>
                            <div class="fw-bold" id="insuranceDisplay">--</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {% if patient %}
            <div class="card patient-card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history"></i> معلومات التسجيل
                    </h5>
                </div>
                <div class="card-body">
                    <p><strong>تاريخ التسجيل:</strong><br>
                       {{ patient.created_at|date:"d/m/Y H:i" }}</p>
                    <p><strong>آخر تحديث:</strong><br>
                       {{ patient.updated_at|date:"d/m/Y H:i" }}</p>
                    <p><strong>العمر:</strong><br>
                       {{ patient.age }} سنة</p>
                </div>
            </div>
        {% else %}
            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle"></i> ملاحظة:</h6>
                <p class="mb-0 small">
                    سيتم إنشاء رقم مريض تلقائياً عند الحفظ.
                    يمكنك تعديل الرقم قبل الحفظ إذا لزم الأمر.
                </p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // حاسبة العمر
    function calculateAge(birthDate) {
        const today = new Date();
        const birth = new Date(birthDate);
        let age = today.getFullYear() - birth.getFullYear();
        const monthDiff = today.getMonth() - birth.getMonth();

        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
            age--;
        }

        return age;
    }

    // تحديث حاسبة العمر
    const dobInput = document.querySelector('input[name="date_of_birth"]');
    const ageDisplay = document.getElementById('calculatedAge');

    if (dobInput && ageDisplay) {
        dobInput.addEventListener('change', function(e) {
            if (e.target.value) {
                const age = calculateAge(e.target.value);
                if (age >= 0 && age <= 150) {
                    ageDisplay.textContent = age + ' سنة';
                    ageDisplay.className = 'h4 mb-1 text-success';
                } else {
                    ageDisplay.textContent = 'عمر غير صحيح';
                    ageDisplay.className = 'h4 mb-1 text-danger';
                }
            } else {
                ageDisplay.textContent = '-- سنة';
                ageDisplay.className = 'h4 mb-1';
            }
        });

        // حساب العمر عند تحميل الصفحة إذا كان التاريخ موجود
        if (dobInput.value) {
            dobInput.dispatchEvent(new Event('change'));
        }
    }

    // تنسيق أرقام الهاتف
    function formatPhone(input) {
        if (input) {
            input.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                if (value.length > 10) {
                    value = value.slice(0, 10);
                }

                if (value.length > 0) {
                    if (value.length <= 2) {
                        value = value;
                    } else if (value.length <= 5) {
                        value = value.slice(0, 2) + '-' + value.slice(2);
                    } else if (value.length <= 8) {
                        value = value.slice(0, 2) + '-' + value.slice(2, 5) + '-' + value.slice(5);
                    } else {
                        value = value.slice(0, 2) + '-' + value.slice(2, 5) + '-' + value.slice(5, 8) + '-' + value.slice(8);
                    }
                }
                e.target.value = value;
            });
        }
    }

    const phoneInput = document.querySelector('input[name="phone"]');
    const emergencyPhoneInput = document.querySelector('input[name="emergency_phone"]');
    formatPhone(phoneInput);
    formatPhone(emergencyPhoneInput);

    // تحديث عرض فصيلة الدم
    const bloodTypeSelect = document.querySelector('select[name="blood_type"]');
    const bloodTypeDisplay = document.getElementById('bloodTypeDisplay');

    if (bloodTypeSelect && bloodTypeDisplay) {
        bloodTypeSelect.addEventListener('change', function(e) {
            bloodTypeDisplay.textContent = e.target.value || '--';
        });

        // تحديث العرض عند تحميل الصفحة
        if (bloodTypeSelect.value) {
            bloodTypeDisplay.textContent = bloodTypeSelect.value;
        }
    }

    // تحديث عرض التأمين
    const insuranceInput = document.querySelector('input[name="insurance_company"]');
    const insuranceDisplay = document.getElementById('insuranceDisplay');

    if (insuranceInput && insuranceDisplay) {
        insuranceInput.addEventListener('input', function(e) {
            insuranceDisplay.textContent = e.target.value || '--';
        });

        // تحديث العرض عند تحميل الصفحة
        if (insuranceInput.value) {
            insuranceDisplay.textContent = insuranceInput.value;
        }
    }

    // عداد الأحرف للحقول النصية
    function addCharacterCounter(textarea, maxLength) {
        if (textarea) {
            const counter = document.createElement('div');
            counter.className = 'character-counter text-end mt-1';
            textarea.parentNode.appendChild(counter);

            function updateCounter() {
                const remaining = maxLength - textarea.value.length;
                counter.textContent = `${textarea.value.length}/${maxLength} حرف`;
                counter.className = remaining < 50 ? 'character-counter text-end mt-1 text-warning' : 'character-counter text-end mt-1 text-muted';
            }

            textarea.addEventListener('input', updateCounter);
            updateCounter();
        }
    }

    // إضافة عدادات الأحرف
    addCharacterCounter(document.querySelector('textarea[name="medical_history"]'), 1000);
    addCharacterCounter(document.querySelector('textarea[name="allergies"]'), 500);
    addCharacterCounter(document.querySelector('textarea[name="chronic_diseases"]'), 500);
    addCharacterCounter(document.querySelector('textarea[name="current_medications"]'), 500);

    // التحقق من صحة النموذج قبل الإرسال
    const form = document.getElementById('patientForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            // التحقق من صحة رقم الهاتف
            const phonePattern = /^[0-9]{2}-[0-9]{3}-[0-9]{3}-[0-9]{2}$/;
            if (phoneInput && phoneInput.value && !phonePattern.test(phoneInput.value)) {
                phoneInput.classList.add('is-invalid');
                isValid = false;
            }

            if (!isValid) {
                e.preventDefault();
                alert('يرجى التأكد من ملء جميع الحقول المطلوبة بشكل صحيح');
            }
        });
    }

    // تحسين تجربة المستخدم
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentNode.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            this.parentNode.classList.remove('focused');
        });
    });
});
</script>
{% endblock %}
