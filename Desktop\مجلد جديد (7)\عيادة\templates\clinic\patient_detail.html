{% extends 'base.html' %}

{% block title %}{{ patient.full_name }} - نظام العيادة الطبية{% endblock %}

{% block extra_css %}
<style>
    .patient-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .stats-card {
        transition: transform 0.2s ease-in-out;
    }

    .stats-card:hover {
        transform: translateY(-2px);
    }

    .action-btn {
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .timeline-item {
        border-left: 3px solid #e9ecef;
        padding-left: 1rem;
        margin-bottom: 1rem;
    }

    .timeline-item.primary {
        border-left-color: #0d6efd;
    }

    .timeline-item.success {
        border-left-color: #198754;
    }

    .timeline-item.warning {
        border-left-color: #ffc107;
    }

    .card-hover {
        transition: box-shadow 0.3s ease;
    }

    .card-hover:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }

    .badge-pulse {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class=" bg-gradient-primary text-white rounded-3 p-4 mb-4 shadow" style="margin-top: 50px;">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    <div class="bg-white bg-opacity-25 rounded-circle p-3 me-3">
                        <i class="bi bi-person-vcard fs-2 text-white"></i>
                    </div>
                    <div>
                        <h1 class="h2 mb-1 text-white">{{ patient.full_name }}</h1>
                        <p class="mb-0 text-white-50">
                            <i class="bi bi-person"></i> {{ patient.age }} سنة •
                            <i class="bi bi-telephone"></i> {{ patient.phone }} •
                            {% if patient.gender == 'M' %}
                                <i class="bi bi-gender-male"></i> ذكر
                            {% else %}
                                <i class="bi bi-gender-female"></i> أنثى
                            {% endif %}
                        </p>
                        <div class="mt-2">
                            <span class="badge bg-light text-dark me-2">
                                <i class="bi bi-hash"></i> {{ patient.patient_id }}
                            </span>
                            {% if last_appointment %}
                                <span class="badge bg-success">
                                    <i class="bi bi-calendar-check"></i> آخر زيارة: {{ last_appointment.appointment_date|date:"d/m/Y" }}
                                </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="btn-group mb-2">
                    <a href="{% url 'patient_update' patient.pk %}" class="btn btn-warning">
                        <i class="bi bi-pencil"></i>
                        <span class="d-none d-lg-inline">تعديل</span>
                    </a>
                    <a href="{% url 'patient_delete' patient.pk %}" class="btn btn-danger">
                        <i class="bi bi-trash"></i>
                        <span class="d-none d-lg-inline">حذف</span>
                    </a>

                    <a href="{% url 'patient_list' %}" class="btn btn-light">
                    <i class="bi bi-arrow-right"></i>
                    <span class="d-none d-sm-inline">العودة للقائمة</span>
                    <span class="d-inline d-sm-none">عودة</span>
                </a>
                </div>

            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white shadow stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">إجمالي المواعيد</div>
                        <div class="h4 mb-0">{{ total_appointments }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-check fs-2"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white shadow stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">معدل الحضور</div>
                        <div class="h4 mb-0">{{ completion_rate }}%</div>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-graph-up fs-2"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white shadow stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">إجمالي المدفوعات</div>
                        <div class="h4 mb-0">{{ total_payments }} ريال</div>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-currency-dollar fs-2"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white shadow stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">فحوصات معلقة</div>
                        <div class="h4 mb-0">
                            {{ pending_lab_tests }}
                            {% if pending_lab_tests > 0 %}
                                <span class="badge-pulse">!</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-hourglass-split fs-2"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Patient Information -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person-badge"></i> المعلومات الشخصية
                </h5>
            </div>
            <div class="card-body ">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>الاسم الكامل:</strong></td>
                        <td>{{ patient.full_name }}</td>
                    </tr>
                    <tr>
                        <td><strong>العمر:</strong></td>
                        <td>{{ patient.age }} سنة</td>
                    </tr>
                    <tr>
                        <td><strong>تاريخ الميلاد:</strong></td>
                        <td>{{ patient.date_of_birth|date:"d/m/Y" }}</td>
                    </tr>
                    <tr>
                        <td><strong>الجنس:</strong></td>
                        <td>
                            {% if patient.gender == 'M' %}
                                <span class="badge bg-primary">ذكر</span>
                            {% else %}
                                <span class="badge bg-info">أنثى</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>رقم الهاتف:</strong></td>
                        <td>{{ patient.phone }}</td>
                    </tr>
                    {% if patient.email %}
                    <tr>
                        <td><strong>البريد الإلكتروني:</strong></td>
                        <td>{{ patient.email }}</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td><strong>العنوان:</strong></td>
                        <td>{{ patient.address }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Emergency Contact -->
        <div class="card shadow mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-telephone-plus"></i> جهة الاتصال في الطوارئ
                </h5>
            </div>
            <div class="card-body">
                <p><strong>الاسم:</strong> {{ patient.emergency_contact }}</p>
                <p><strong>الهاتف:</strong> {{ patient.emergency_phone }}</p>
            </div>
        </div>

        <!-- Medical Information -->
        <div class="card shadow mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-heart-pulse"></i> المعلومات الطبية
                </h5>
            </div>
            <div class="card-body">
                {% if patient.medical_history %}
                    <h6>التاريخ المرضي:</h6>
                    <p class="text-muted">{{ patient.medical_history }}</p>
                {% endif %}

                {% if patient.allergies %}
                    <h6>الحساسيات:</h6>
                    <p class="text-danger">{{ patient.allergies }}</p>
                {% endif %}

                {% if not patient.medical_history and not patient.allergies %}
                    <p class="text-muted">لا توجد معلومات طبية مسجلة</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Patient Records -->
    <div class="col-lg-8">
        <!-- Quick Actions -->
        <div class="card shadow mb-4 card-hover">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning"></i> إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-lg-2 col-md-4 col-6">
                        <a href="{% url 'appointment_create' %}?patient_id={{ patient.id }}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3 action-btn">
                            <i class="bi bi-calendar-plus fs-3 mb-2"></i>
                            <small class="text-center fw-bold">حجز موعد</small>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <a href="{% url 'diagnosis_create' %}?patient_id={{ patient.id }}" class="btn btn-outline-success w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3 action-btn">
                            <i class="bi bi-clipboard-plus fs-3 mb-2"></i>
                            <small class="text-center fw-bold">إضافة تشخيص</small>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <a href="{% url 'prescription_create' %}?patient_id={{ patient.id }}" class="btn btn-outline-info w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3 action-btn">
                            <i class="bi bi-prescription2 fs-3 mb-2"></i>
                            <small class="text-center fw-bold">كتابة وصفة</small>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <a href="{% url 'payment_create' %}?patient_id={{ patient.id }}" class="btn btn-outline-warning w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3 action-btn">
                            <i class="bi bi-credit-card fs-3 mb-2"></i>
                            <small class="text-center fw-bold">إضافة دفعة</small>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <a href="{% url 'lab' %}?patient_id={{ patient.id }}" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3 action-btn">
                            <i class="bi bi-clipboard-data fs-3 mb-2"></i>
                            <small class="text-center fw-bold">طلب فحص</small>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <a href="{% url 'rays' %}?patient_id={{ patient.id }}" class="btn btn-outline-dark w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3 action-btn">
                            <i class="fs-3 mb-2 bi bi-camera fs-1 mb-2"></i>
                            <small class="text-center fw-bold">طلب أشعة</small>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Health Summary -->
        {% if recent_diagnoses %}
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-heart-pulse"></i> ملخص الحالة الصحية الحديثة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for diagnosis in recent_diagnoses %}
                    <div class="col-md-4 mb-3">
                        <div class="border rounded p-3 h-100">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="text-primary mb-0">{{ diagnosis.diagnosis|truncatechars:40 }}</h6>
                                <small class="text-muted">{{ diagnosis.diagnosis_date|date:"d/m" }}</small>
                            </div>
                            <p class="text-muted small mb-2">{{ diagnosis.symptoms|truncatechars:60 }}</p>
                            <small class="text-info">د.  {{ diagnosis.doctor.last_name }} {{ diagnosis.doctor.first_name }}</small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Appointments -->
        <div class="card shadow mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-calendar-check"></i> المواعيد الأخيرة
                    <span class="badge bg-primary ms-2">{{ total_appointments }}</span>
                </h5>
                <a href="{% url 'appointment_list' %}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                {% if appointments %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th><i class="bi bi-calendar3"></i> التاريخ</th>
                                    <th><i class="bi bi-person-badge"></i> الطبيب</th>
                                    <th><i class="bi bi-chat-text"></i> السبب</th>
                                    <th><i class="bi bi-check-circle"></i> الحالة</th>
                                    <th><i class="bi bi-gear"></i> إجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for appointment in appointments %}
                                    <tr>
                                        <td>
                                            <strong>{{ appointment.appointment_date|date:"d/m/Y" }}</strong><br>
                                            <small class="text-muted">{{ appointment.appointment_date|date:"H:i" }}</small>
                                        </td>
                                        <td>{{ appointment.doctor.first_name }} {{ appointment.doctor.last_name }}</td>
                                        <td>{{ appointment.reason|truncatechars:40 }}</td>
                                        <td>
                                            <span class="badge bg-{% if appointment.status == 'scheduled' %}primary{% elif appointment.status == 'completed' %}success{% elif appointment.status == 'cancelled' %}danger{% else %}secondary{% endif %}">
                                                {{ appointment.get_status_display }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{% url 'appointment_detail' appointment.pk %}" class="btn btn-sm btn-outline-info">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-calendar-x fs-1 text-muted"></i>
                        <p class="text-muted mt-2">لا توجد مواعيد مسجلة</p>
                        <a href="{% url 'appointment_create' %}?patient_id={{ patient.id }}" class="btn btn-primary">
                            <i class="bi bi-calendar-plus"></i> حجز موعد جديد
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- التشخيصات -->
        <div class="card shadow mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clipboard-pulse"></i> التشخيصات الطبية
                </h5>
                <a href="{% url 'diagnosis_list' %}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                {% if diagnoses %}
                    {% for diagnosis in diagnoses %}
                        <div class="card mb-3 border-start border-primary border-4">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="text-primary mb-0">{{ diagnosis.diagnosis|truncatechars:60 }}</h6>
                                    <span class="badge bg-light text-dark">{{ diagnosis.diagnosis_date|date:"d/m/Y" }}</span>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted d-block"><strong>الأعراض:</strong></small>
                                        <p class="small mb-2">{{ diagnosis.symptoms|truncatechars:80 }}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted d-block"><strong>خطة العلاج:</strong></small>
                                        <p class="small mb-2">{{ diagnosis.treatment_plan|truncatechars:80 }}</p>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-info">
                                        <i class="bi bi-person-badge"></i> د. {{ diagnosis.doctor.first_name }} {{ diagnosis.doctor.last_name }}
                                    </small>
                                    <a href="{% url 'diagnosis_detail' diagnosis.pk %}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i> التفاصيل
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-clipboard-pulse fs-1 text-muted"></i>
                        <p class="text-muted mt-2">لا توجد تشخيصات مسجلة</p>
                        <a href="{% url 'diagnosis_create' %}?patient_id={{ patient.id }}" class="btn btn-primary">
                            <i class="bi bi-clipboard-plus"></i> إضافة تشخيص جديد
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- الوصفات الطبية -->
        <div class="card shadow mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-prescription2"></i> الوصفات الطبية
                </h5>
                <a href="{% url 'prescription_list' %}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                {% if prescriptions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th><i class="bi bi-calendar3"></i> التاريخ</th>
                                    <th><i class="bi bi-person-badge"></i> الطبيب</th>
                                    <th><i class="bi bi-capsule"></i> الأدوية</th>
                                    <th><i class="bi bi-clock"></i> المدة</th>
                                    <th><i class="bi bi-gear"></i> إجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for prescription in prescriptions %}
                                    <tr>
                                        <td>
                                            <strong>{{ prescription.prescription_date|date:"d/m/Y" }}</strong><br>
                                            <small class="text-muted">{{ prescription.prescription_date|date:"H:i" }}</small>
                                        </td>
                                        <td>{{ prescription.doctor.first_name }} {{ prescription.doctor.last_name }}</td>
                                        <td>{{ prescription.medications|truncatechars:40 }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ prescription.duration }}</span>
                                        </td>
                                        <td>
                                            <a href="{% url 'prescription_detail' prescription.pk %}" class="btn btn-sm btn-outline-info">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-prescription2 fs-1 text-muted"></i>
                        <p class="text-muted mt-2">لا توجد وصفات طبية مسجلة</p>
                        <a href="{% url 'prescription_create' %}?patient_id={{ patient.id }}" class="btn btn-primary">
                            <i class="bi bi-prescription2"></i> كتابة وصفة جديدة
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>


        <!-- الفحوصات المخبرية -->
        <div class="card shadow mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-vial"></i> الفحوصات المخبرية
                    {% if pending_lab_tests > 0 %}
                        <span class="badge bg-warning ms-2">{{ pending_lab_tests }} معلق</span>
                    {% endif %}
                </h5>
                <a href="{% url 'record_list' %}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                {% if labtest %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th><i class="bi bi-calendar3"></i> التاريخ</th>
                                    <th><i class="bi bi-person-badge"></i> الطبيب</th>
                                    <th><i class="bi bi-clipboard-data"></i> اسم الفحص</th>
                                    <th><i class="bi bi-tags"></i> الفئة</th>
                                    <th><i class="bi bi-check-circle"></i> الحالة</th>
                                    <th><i class="bi bi-gear"></i> إجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for test in labtest %}
                                    <tr>
                                        <td>
                                            <strong>{{ test.ordered_date|date:"d/m/Y" }}</strong><br>
                                            <small class="text-muted">{{ test.ordered_date|date:"H:i" }}</small>
                                        </td>
                                        <td>{{ test.doctor.first_name }} {{ test.doctor.last_name }}</td>
                                        <td>{{ test.test_name|truncatechars:30 }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ test.test_category }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-{% if test.status == 'ordered' %}warning{% elif test.status == 'completed' %}success{% elif test.status == 'in_progress' %}primary{% else %}secondary{% endif %}">
                                                {{ test.get_status_display }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{% url 'lab_detail' test.pk %}" class="btn btn-sm btn-outline-info">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-vial fs-1 text-muted"></i>
                        <p class="text-muted mt-2">لا توجد فحوصات مخبرية مسجلة</p>
                        <a href="{% url 'lab' %}?patient_id={{ patient.id }}" class="btn btn-primary">
                            <i class="bi bi-vial"></i> طلب فحص جديد
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- الأشعة -->
        <div class="card shadow mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-x-ray"></i> الأشعة والتصوير الطبي
                    {% if pending_rays > 0 %}
                        <span class="badge bg-warning ms-2">{{ pending_rays }} معلق</span>
                    {% endif %}
                </h5>
                <a href="{% url 'rays_list' %}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                {% if rays %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th><i class="bi bi-calendar3"></i> التاريخ</th>
                                    <th><i class="bi bi-person-badge"></i> الطبيب</th>
                                    <th><i class="bi bi-camera"></i> نوع الأشعة</th>
                                    <th><i class="bi bi-body-text"></i> الجزء</th>
                                    <th><i class="bi bi-gear"></i> إجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for ray in rays %}
                                    <tr>
                                        <td>
                                            <strong>{{ ray.ordered_date|date:"d/m/Y" }}</strong><br>
                                            <small class="text-muted">{{ ray.ordered_date|date:"H:i" }}</small>
                                        </td>
                                        <td>{{ ray.doctor.first_name }} {{ ray.doctor.last_name }}</td>
                                        <td>
                                            <span class="badge bg-dark">{{ ray.get_rays_name_display }}</span>
                                        </td>
                                        <td>{{ ray.part|truncatechars:20 }}</td>
                                        <td>
                                            <a href="{% url 'rays_detail' ray.pk %}" class="btn btn-sm btn-outline-info">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-x-ray fs-1 text-muted"></i>
                        <p class="text-muted mt-2">لا توجد أشعة مسجلة</p>
                        <a href="{% url 'rays' %}?patient_id={{ patient.id }}" class="btn btn-primary">
                            <i class="bi bi-x-ray"></i> طلب أشعة جديدة
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>


        <!-- المدفوعات -->
        <div class="card shadow mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-credit-card"></i> المدفوعات والفواتير
                    {% if pending_payments > 0 %}
                        <span class="badge bg-warning ms-2">{{ pending_payments }} معلق</span>
                    {% endif %}
                </h5>
                <a href="{% url 'payment_list' %}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                {% if payments %}
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="bg-success bg-opacity-10 border border-success rounded p-3 text-center">
                                <h4 class="text-success mb-0">{{ total_payments }} ريال</h4>
                                <small class="text-muted">إجمالي المدفوعات</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="bg-warning bg-opacity-10 border border-warning rounded p-3 text-center">
                                <h4 class="text-warning mb-0">{{ pending_payments }}</h4>
                                <small class="text-muted">دفعات معلقة</small>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th><i class="bi bi-calendar3"></i> التاريخ</th>
                                    <th><i class="bi bi-currency-dollar"></i> المبلغ</th>
                                    <th><i class="bi bi-credit-card"></i> الطريقة</th>
                                    <th><i class="bi bi-check-circle"></i> الحالة</th>
                                    <th><i class="bi bi-gear"></i> إجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in payments %}
                                    <tr>
                                        <td>
                                            <strong>{{ payment.payment_date|date:"d/m/Y" }}</strong><br>
                                            <small class="text-muted">{{ payment.payment_date|date:"H:i" }}</small>
                                        </td>
                                        <td>
                                            <strong class="text-success">{{ payment.amount }} ريال</strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ payment.get_payment_method_display }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-{% if payment.status == 'paid' %}success{% elif payment.status == 'pending' %}warning{% elif payment.status == 'cancelled' %}danger{% else %}secondary{% endif %}">
                                                {{ payment.get_status_display }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{% url 'payment_detail' payment.pk %}" class="btn btn-sm btn-outline-info">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-credit-card fs-1 text-muted"></i>
                        <p class="text-muted mt-2">لا توجد مدفوعات مسجلة</p>
                        <a href="{% url 'payment_create' %}?patient_id={{ patient.id }}" class="btn btn-primary">
                            <i class="bi bi-credit-card"></i> إضافة دفعة جديدة
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Registration Info -->
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i> معلومات التسجيل والنشاط
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong><i class="bi bi-calendar-plus"></i> تاريخ التسجيل:</strong><br>
                        <span class="text-muted">{{ patient.created_at|date:"d/m/Y H:i" }}</span></p>

                        <p><strong><i class="bi bi-pencil-square"></i> آخر تحديث:</strong><br>
                        <span class="text-muted">{{ patient.updated_at|date:"d/m/Y H:i" }}</span></p>
                    </div>
                    <div class="col-md-6">
                        {% if last_appointment %}
                            <p><strong><i class="bi bi-calendar-check"></i> آخر زيارة:</strong><br>
                            <span class="text-muted">{{ last_appointment.appointment_date|date:"d/m/Y" }}</span></p>
                        {% endif %}

                        <p><strong><i class="bi bi-activity"></i> حالة المريض:</strong><br>
                        {% if patient.is_active %}
                            <span class="badge bg-success">نشط</span>
                        {% else %}
                            <span class="badge bg-secondary">غير نشط</span>
                        {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
