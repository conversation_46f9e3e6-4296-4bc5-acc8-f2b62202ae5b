# Generated by Django 4.2.7 on 2025-06-13 00:03

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ("staff", "0003_salarystructure_payroll"),
        ("patients", "0003_alter_patient_national_id"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("medical_records", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="MedicalRecordCategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="اسم الفئة")),
                ("description", models.TextField(blank=True, verbose_name="الوصف")),
                (
                    "color",
                    models.Char<PERSON>ield(
                        default="#007bff", max_length=7, verbose_name="اللون"
                    ),
                ),
                (
                    "icon",
                    models.Char<PERSON>ield(
                        default="fas fa-notes-medical",
                        max_length=50,
                        verbose_name="الأيقونة",
                    ),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="نشط")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name": "فئة السجل الطبي",
                "verbose_name_plural": "فئات السجلات الطبية",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Prescription",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "prescription_id",
                    models.CharField(
                        max_length=20, unique=True, verbose_name="رقم الوصفة"
                    ),
                ),
                ("diagnosis", models.TextField(verbose_name="التشخيص")),
                (
                    "instructions",
                    models.TextField(blank=True, verbose_name="تعليمات عامة"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "نشطة"),
                            ("completed", "مكتملة"),
                            ("cancelled", "ملغية"),
                            ("expired", "منتهية الصلاحية"),
                        ],
                        default="active",
                        max_length=10,
                        verbose_name="الحالة",
                    ),
                ),
                (
                    "prescribed_date",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="تاريخ الوصفة"
                    ),
                ),
                (
                    "expiry_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ انتهاء الصلاحية"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="أنشأ بواسطة",
                    ),
                ),
                (
                    "doctor",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="medical_prescriptions",
                        to="staff.staff",
                        verbose_name="الطبيب",
                    ),
                ),
                (
                    "medical_record",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="medical_records.medicalrecord",
                        verbose_name="السجل الطبي",
                    ),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="medical_prescriptions",
                        to="patients.patient",
                        verbose_name="المريض",
                    ),
                ),
            ],
            options={
                "verbose_name": "الوصفة الطبية",
                "verbose_name_plural": "الوصفات الطبية",
                "ordering": ["-prescribed_date"],
            },
        ),
        migrations.CreateModel(
            name="VitalSigns",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "temperature",
                    models.DecimalField(
                        blank=True,
                        decimal_places=1,
                        max_digits=4,
                        null=True,
                        verbose_name="درجة الحرارة (°C)",
                    ),
                ),
                (
                    "blood_pressure_systolic",
                    models.IntegerField(
                        blank=True, null=True, verbose_name="ضغط الدم الانقباضي"
                    ),
                ),
                (
                    "blood_pressure_diastolic",
                    models.IntegerField(
                        blank=True, null=True, verbose_name="ضغط الدم الانبساطي"
                    ),
                ),
                (
                    "heart_rate",
                    models.IntegerField(
                        blank=True, null=True, verbose_name="النبض (نبضة/دقيقة)"
                    ),
                ),
                (
                    "respiratory_rate",
                    models.IntegerField(
                        blank=True, null=True, verbose_name="التنفس (نفس/دقيقة)"
                    ),
                ),
                (
                    "oxygen_saturation",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=5,
                        null=True,
                        verbose_name="تشبع الأكسجين (%)",
                    ),
                ),
                (
                    "weight",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=5,
                        null=True,
                        verbose_name="الوزن (كجم)",
                    ),
                ),
                (
                    "height",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=5,
                        null=True,
                        verbose_name="الطول (سم)",
                    ),
                ),
                (
                    "bmi",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=4,
                        null=True,
                        verbose_name="مؤشر كتلة الجسم",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="ملاحظات")),
                (
                    "recorded_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="تاريخ التسجيل"
                    ),
                ),
                (
                    "medical_record",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="medical_records.medicalrecord",
                        verbose_name="السجل الطبي",
                    ),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="patients.patient",
                        verbose_name="المريض",
                    ),
                ),
                (
                    "recorded_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="سجل بواسطة",
                    ),
                ),
            ],
            options={
                "verbose_name": "العلامات الحيوية",
                "verbose_name_plural": "العلامات الحيوية",
                "ordering": ["-recorded_at"],
            },
        ),
        migrations.CreateModel(
            name="PrescriptionItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "medication_name",
                    models.CharField(max_length=200, verbose_name="اسم الدواء"),
                ),
                ("dosage", models.CharField(max_length=100, verbose_name="الجرعة")),
                (
                    "frequency",
                    models.CharField(max_length=100, verbose_name="عدد المرات"),
                ),
                (
                    "duration",
                    models.CharField(max_length=100, verbose_name="مدة العلاج"),
                ),
                (
                    "instructions",
                    models.TextField(blank=True, verbose_name="تعليمات خاصة"),
                ),
                ("quantity", models.IntegerField(verbose_name="الكمية")),
                (
                    "refills",
                    models.IntegerField(default=0, verbose_name="عدد التجديدات"),
                ),
                (
                    "prescription",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="items",
                        to="medical_records.prescription",
                        verbose_name="الوصفة",
                    ),
                ),
            ],
            options={
                "verbose_name": "عنصر الوصفة",
                "verbose_name_plural": "عناصر الوصفة",
            },
        ),
        migrations.CreateModel(
            name="MedicalRecordAttachment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200, verbose_name="العنوان")),
                (
                    "file",
                    models.FileField(
                        upload_to="medical_records/attachments/", verbose_name="الملف"
                    ),
                ),
                (
                    "attachment_type",
                    models.CharField(
                        choices=[
                            ("image", "صورة"),
                            ("document", "مستند"),
                            ("lab_result", "نتيجة مختبر"),
                            ("xray", "أشعة سينية"),
                            ("ct_scan", "أشعة مقطعية"),
                            ("mri", "رنين مغناطيسي"),
                            ("ultrasound", "موجات فوق صوتية"),
                            ("ecg", "تخطيط قلب"),
                            ("other", "أخرى"),
                        ],
                        max_length=20,
                        verbose_name="نوع المرفق",
                    ),
                ),
                ("description", models.TextField(blank=True, verbose_name="الوصف")),
                ("uploaded_at", models.DateTimeField(auto_now_add=True)),
                (
                    "medical_record",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="attachments",
                        to="medical_records.medicalrecord",
                        verbose_name="السجل الطبي",
                    ),
                ),
                (
                    "uploaded_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="رفع بواسطة",
                    ),
                ),
            ],
            options={
                "verbose_name": "مرفق السجل الطبي",
                "verbose_name_plural": "مرفقات السجلات الطبية",
                "ordering": ["-uploaded_at"],
            },
        ),
        migrations.CreateModel(
            name="LabTest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "test_id",
                    models.CharField(
                        max_length=20, unique=True, verbose_name="رقم الفحص"
                    ),
                ),
                (
                    "test_name",
                    models.CharField(max_length=200, verbose_name="اسم الفحص"),
                ),
                (
                    "test_category",
                    models.CharField(max_length=100, verbose_name="فئة الفحص"),
                ),
                (
                    "instructions",
                    models.TextField(blank=True, verbose_name="تعليمات الفحص"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("ordered", "مطلوب"),
                            ("in_progress", "قيد التنفيذ"),
                            ("completed", "مكتمل"),
                            ("cancelled", "ملغي"),
                        ],
                        default="ordered",
                        max_length=15,
                        verbose_name="الحالة",
                    ),
                ),
                (
                    "ordered_date",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="تاريخ الطلب"
                    ),
                ),
                (
                    "sample_collected_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ أخذ العينة"
                    ),
                ),
                (
                    "result_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ النتيجة"
                    ),
                ),
                ("results", models.TextField(blank=True, verbose_name="النتائج")),
                (
                    "normal_range",
                    models.CharField(
                        blank=True, max_length=200, verbose_name="المعدل الطبيعي"
                    ),
                ),
                (
                    "is_abnormal",
                    models.BooleanField(default=False, verbose_name="غير طبيعي"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="أنشأ بواسطة",
                    ),
                ),
                (
                    "doctor",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="staff.staff",
                        verbose_name="الطبيب الطالب",
                    ),
                ),
                (
                    "medical_record",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="medical_records.medicalrecord",
                        verbose_name="السجل الطبي",
                    ),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="patients.patient",
                        verbose_name="المريض",
                    ),
                ),
                (
                    "technician",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="lab_tests_performed",
                        to="staff.staff",
                        verbose_name="فني المختبر",
                    ),
                ),
            ],
            options={
                "verbose_name": "الفحص المخبري",
                "verbose_name_plural": "الفحوصات المخبرية",
                "ordering": ["-ordered_date"],
            },
        ),
    ]
