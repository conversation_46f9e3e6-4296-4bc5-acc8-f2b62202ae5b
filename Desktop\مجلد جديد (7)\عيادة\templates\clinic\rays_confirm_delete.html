{% extends 'base.html' %}

{% block title %}حذف الموعد - نظام العيادة الطبية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-calendar-x text-danger"></i> 
        حذف الموعد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'rays_detail' prescription.pk %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right"></i> العودة
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-exclamation-triangle"></i> تأكيد حذف الموعد
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <h6><i class="bi bi-exclamation-triangle"></i> تحذير مهم:</h6>
                    <p class="mb-0">
                        هذا الإجراء لا يمكن التراجع عنه. سيتم حذف الموعد نهائياً من النظام.
                    </p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6>تفاصيل الموعد المراد حذفه:</h6>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>المريض:</strong></td>
                                <td>{{ appointment.patient.full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>التاريخ والوقت:</strong></td>
                                <td>{{ appointment.appointment_date|date:"d/m/Y H:i" }}</td>
                            </tr>
                            <tr>
                                <td><strong>الطبيب:</strong></td>
                                <td>{{ appointment.doctor.first_name }} {{ appointment.doctor.last_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>المدة:</strong></td>
                                <td>{{ appointment.duration }} دقيقة</td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    <span class="badge bg-{% if appointment.status == 'scheduled' %}primary{% elif appointment.status == 'completed' %}success{% else %}secondary{% endif %}">
                                        {{ appointment.get_status_display }}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="col-md-6">
                        <h6>سبب الزيارة:</h6>
                        <div class="bg-light p-3 rounded mb-3">
                            {{ appointment.reason }}
                        </div>
                        
                        {% if appointment.notes %}
                        <h6>ملاحظات:</h6>
                        <div class="bg-light p-3 rounded">
                            {{ appointment.notes }}
                        </div>
                        {% endif %}
                    </div>
                </div>

                <hr>

                <div class="text-center">
                    <p class="text-muted mb-4">
                        هل أنت متأكد من رغبتك في حذف هذا الموعد؟
                    </p>
                    
                    <form method="post" class="d-inline">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-danger me-3">
                            <i class="bi bi-trash"></i> نعم، احذف الموعد
                        </button>
                    </form>
                    
                    <a href="{% url 'rays_detail' prescription.pk %}" class="btn btn-secondary">
                        <i class="bi bi-x-circle"></i> إلغاء
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add confirmation dialog
    const deleteForm = document.querySelector('form');
    if (deleteForm) {
        deleteForm.addEventListener('submit', function(e) {
            if (!confirm('هل أنت متأكد من رغبتك في حذف هذا الموعد؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                e.preventDefault();
            }
        });
    }
});
</script>
{% endblock %}
