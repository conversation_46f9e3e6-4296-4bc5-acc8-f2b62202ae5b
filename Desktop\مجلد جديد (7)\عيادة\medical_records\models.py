from django.db import models
from django.contrib.auth.models import User
from clinic.models import Patient

# from staff.models import Staff
# from departments.models import Department
from django.utils import timezone
from decimal import Decimal


class MedicalRecord(models.Model):
    """السجل الطبي الرئيسي"""
    RECORD_TYPES = [
        ('CONSULTATION', 'استشارة'),
        ('DIAGNOSIS', 'تشخيص'),
        ('TREATMENT', 'علاج'),
        ('SURGERY', 'جراحة'),
        ('EMERGENCY', 'طوارئ'),
        ('FOLLOW_UP', 'متابعة'),
        ('DISCHARGE', 'خروج'),
    ]

    PRIORITY_LEVELS = [
        ('LOW', 'منخفض'),
        ('NORMAL', 'عادي'),
        ('HIGH', 'عالي'),
        ('URGENT', 'عاجل'),
        ('CRITICAL', 'حرج'),
    ]

    record_id = models.CharField(max_length=20, unique=True)
    patient = models.ForeignKey(Patient, on_delete=models.CASCADE)
    doctor = models.ForeignKey(User, on_delete=models.CASCADE)
    # department = models.ForeignKey(Department, on_delete=models.CASCADE)

    record_type = models.CharField(max_length=20, choices=RECORD_TYPES)
    priority = models.CharField(max_length=20, choices=PRIORITY_LEVELS,
                               default='NORMAL')

    chief_complaint = models.TextField()
    history_of_present_illness = models.TextField()
    past_medical_history = models.TextField(blank=True)
    family_history = models.TextField(blank=True)
    social_history = models.TextField(blank=True)

    physical_examination = models.TextField()
    assessment = models.TextField()
    plan = models.TextField()

    is_confidential = models.BooleanField(default=False)
    is_completed = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = "سجل طبي"
        verbose_name_plural = "السجلات الطبية"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.record_id} - {self.patient.full_name}"

    def save(self, *args, **kwargs):
        if not self.record_id:
            # توليد رقم السجل تلقائياً
            last_record = MedicalRecord.objects.order_by('-id').first()
            if last_record:
                last_number = int(last_record.record_id.split('-')[-1])
                self.record_id = f"MR-{last_number + 1:06d}"
            else:
                self.record_id = "MR-000001"
        super().save(*args, **kwargs)


class MedicalRecordCategory(models.Model):
    """فئات السجلات الطبية"""
    name = models.CharField(max_length=100, verbose_name="اسم الفئة")
    description = models.TextField(blank=True, verbose_name="الوصف")
    color = models.CharField(max_length=7, default="#007bff", verbose_name="اللون")
    icon = models.CharField(max_length=50, default="fas fa-notes-medical", verbose_name="الأيقونة")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "فئة السجل الطبي"
        verbose_name_plural = "فئات السجلات الطبية"
        ordering = ['name']

    def __str__(self):
        return self.name


class MedicalRecordAttachment(models.Model):
    """مرفقات السجلات الطبية"""
    ATTACHMENT_TYPES = [
        ('image', 'صورة'),
        ('document', 'مستند'),
        ('lab_result', 'نتيجة مختبر'),
        ('xray', 'أشعة سينية'),
        ('ct_scan', 'أشعة مقطعية'),
        ('mri', 'رنين مغناطيسي'),
        ('ultrasound', 'موجات فوق صوتية'),
        ('ecg', 'تخطيط قلب'),
        ('other', 'أخرى'),
    ]

    medical_record = models.ForeignKey(MedicalRecord, on_delete=models.CASCADE,
                                     related_name='attachments', verbose_name="السجل الطبي")
    title = models.CharField(max_length=200, verbose_name="العنوان")
    file = models.FileField(upload_to='medical_records/attachments/', verbose_name="الملف")
    attachment_type = models.CharField(max_length=20, choices=ATTACHMENT_TYPES, verbose_name="نوع المرفق")
    description = models.TextField(blank=True, verbose_name="الوصف")
    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="رفع بواسطة")
    uploaded_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "مرفق السجل الطبي"
        verbose_name_plural = "مرفقات السجلات الطبية"
        ordering = ['-uploaded_at']

    def __str__(self):
        return f"{self.title} - {self.medical_record.record_id}"


class VitalSigns(models.Model):
    """العلامات الحيوية"""
    patient = models.ForeignKey(Patient, on_delete=models.CASCADE, verbose_name="المريض")
    medical_record = models.ForeignKey(MedicalRecord, on_delete=models.CASCADE,
                                     null=True, blank=True, verbose_name="السجل الطبي")
    temperature = models.DecimalField(max_digits=4, decimal_places=1, null=True, blank=True,
                                    verbose_name="درجة الحرارة (°C)")
    blood_pressure_systolic = models.IntegerField(null=True, blank=True,
                                                 verbose_name="ضغط الدم الانقباضي")
    blood_pressure_diastolic = models.IntegerField(null=True, blank=True,
                                                  verbose_name="ضغط الدم الانبساطي")
    heart_rate = models.IntegerField(null=True, blank=True, verbose_name="النبض (نبضة/دقيقة)")
    respiratory_rate = models.IntegerField(null=True, blank=True, verbose_name="التنفس (نفس/دقيقة)")
    oxygen_saturation = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True,
                                          verbose_name="تشبع الأكسجين (%)")
    weight = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True,
                               verbose_name="الوزن (كجم)")
    height = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True,
                               verbose_name="الطول (سم)")
    bmi = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True,
                            verbose_name="مؤشر كتلة الجسم")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    recorded_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="سجل بواسطة")
    recorded_at = models.DateTimeField(default=timezone.now, verbose_name="تاريخ التسجيل")

    class Meta:
        verbose_name = "العلامات الحيوية"
        verbose_name_plural = "العلامات الحيوية"
        ordering = ['-recorded_at']

    def __str__(self):
        return f"علامات حيوية - {self.patient.full_name} - {self.recorded_at.strftime('%Y-%m-%d %H:%M')}"

    @property
    def blood_pressure(self):
        if self.blood_pressure_systolic and self.blood_pressure_diastolic:
            return f"{self.blood_pressure_systolic}/{self.blood_pressure_diastolic}"
        return None

    def save(self, *args, **kwargs):
        # حساب مؤشر كتلة الجسم تلقائياً
        if self.weight and self.height:
            height_m = float(self.height) / 100  # تحويل من سم إلى متر
            self.bmi = Decimal(str(float(self.weight) / (height_m ** 2))).quantize(Decimal('0.01'))
        super().save(*args, **kwargs)


class Prescription(models.Model):
    """الوصفات الطبية"""
    STATUS_CHOICES = [
        ('active', 'نشطة'),
        ('completed', 'مكتملة'),
        ('cancelled', 'ملغية'),
        ('expired', 'منتهية الصلاحية'),
    ]

    prescription_id = models.CharField(max_length=20, unique=True, verbose_name="رقم الوصفة")
    patient = models.ForeignKey(Patient, on_delete=models.CASCADE,
                               related_name='medical_prescriptions', verbose_name="المريض")
    doctor = models.ForeignKey(User, on_delete=models.CASCADE,
                              related_name='medical_prescriptions', verbose_name="الطبيب")
    medical_record = models.ForeignKey(MedicalRecord, on_delete=models.CASCADE,
                                     null=True, blank=True, verbose_name="السجل الطبي")
    diagnosis = models.TextField(verbose_name="التشخيص")
    instructions = models.TextField(blank=True, verbose_name="تعليمات عامة")
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='active', verbose_name="الحالة")
    prescribed_date = models.DateTimeField(default=timezone.now, verbose_name="تاريخ الوصفة")
    expiry_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ انتهاء الصلاحية")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشأ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "الوصفة الطبية"
        verbose_name_plural = "الوصفات الطبية"
        ordering = ['-prescribed_date']

    def __str__(self):
        return f"{self.prescription_id} - {self.patient.full_name}"

    def save(self, *args, **kwargs):
        if not self.prescription_id:
            # إنشاء رقم وصفة تلقائي
            last_prescription = Prescription.objects.filter(
                prescription_id__startswith=f"RX{timezone.now().year}"
            ).order_by('-prescription_id').first()

            if last_prescription:
                last_number = int(last_prescription.prescription_id[-4:])
                new_number = last_number + 1
            else:
                new_number = 1

            self.prescription_id = f"RX{timezone.now().year}{new_number:04d}"

        super().save(*args, **kwargs)


class PrescriptionItem(models.Model):
    """عناصر الوصفة الطبية"""
    prescription = models.ForeignKey(Prescription, on_delete=models.CASCADE,
                                   related_name='items', verbose_name="الوصفة")
    medication_name = models.CharField(max_length=200, verbose_name="اسم الدواء")
    dosage = models.CharField(max_length=100, verbose_name="الجرعة")
    frequency = models.CharField(max_length=100, verbose_name="عدد المرات")
    duration = models.CharField(max_length=100, verbose_name="مدة العلاج")
    instructions = models.TextField(blank=True, verbose_name="تعليمات خاصة")
    quantity = models.IntegerField(verbose_name="الكمية")
    refills = models.IntegerField(default=0, verbose_name="عدد التجديدات")

    class Meta:
        verbose_name = "عنصر الوصفة"
        verbose_name_plural = "عناصر الوصفة"

    def __str__(self):
        return f"{self.medication_name} - {self.dosage}"


class LabTest(models.Model):
    """الفحوصات المخبرية"""
    STATUS_CHOICES = [
        ('ordered', 'مطلوب'),
        ('in_progress', 'قيد التنفيذ'),
        ('completed', 'مكتمل'),
        ('cancelled', 'ملغي'),
    ]

    test_id = models.CharField(max_length=20, unique=True, verbose_name="رقم الفحص")
    patient = models.ForeignKey(Patient, on_delete=models.CASCADE, verbose_name="المريض")
    doctor = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="الطبيب الطالب")
    medical_record = models.ForeignKey(MedicalRecord, on_delete=models.CASCADE,
                                     null=True, blank=True, verbose_name="السجل الطبي")
    test_name = models.CharField(max_length=200, verbose_name="اسم الفحص")
    test_category = models.CharField(max_length=100, verbose_name="فئة الفحص")
    instructions = models.TextField(blank=True, verbose_name="تعليمات الفحص")
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='ordered', verbose_name="الحالة")
    ordered_date = models.DateTimeField(default=timezone.now, verbose_name="تاريخ الطلب")
    sample_collected_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ أخذ العينة")
    result_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ النتيجة")
    results = models.TextField(blank=True, verbose_name="النتائج")
    normal_range = models.CharField(max_length=200, blank=True, verbose_name="المعدل الطبيعي")
    is_abnormal = models.BooleanField(default=False, verbose_name="غير طبيعي")
    technician = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True,
                                 related_name='lab_tests_performed', verbose_name="فني المختبر")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشأ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "الفحص المخبري"
        verbose_name_plural = "الفحوصات المخبرية"
        ordering = ['-ordered_date']

    def __str__(self):
        return f"{self.test_id} - {self.test_name}"

    def save(self, *args, **kwargs):
        if not self.test_id:
            # إنشاء رقم فحص تلقائي
            last_test = LabTest.objects.filter(
                test_id__startswith=f"LAB{timezone.now().year}"
            ).order_by('-test_id').first()

            if last_test:
                last_number = int(last_test.test_id[-4:])
                new_number = last_number + 1
            else:
                new_number = 1

            self.test_id = f"LAB{timezone.now().year}{new_number:04d}"

        super().save(*args, **kwargs)
