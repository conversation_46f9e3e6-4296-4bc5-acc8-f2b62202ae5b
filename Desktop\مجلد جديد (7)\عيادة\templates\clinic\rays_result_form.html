{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }} - نظام العيادة الطبية{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="d-flex justify-content-between flex-wrap align-items-start pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2 mb-2 mb-md-0">
            <i class="bi bi-camera"></i>
            {{ title }}
        </h1>
        <div class="btn-toolbar">
            <a href="{% url 'rays_detail' rays.pk %}" class="btn btn-outline-secondary btn-sm">
                <i class="bi bi-arrow-right"></i>
                <span class="d-none d-sm-inline">العودة للأشعة</span>
                <span class="d-inline d-sm-none">عودة</span>
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Form Section -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-camera"></i>
                        نتائج الأشعة والتصوير الطبي
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <!-- Status and Radiologist -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.status.id_for_label }}" class="form-label">
                                    <i class="bi bi-flag"></i> حالة الفحص
                                </label>
                                {{ form.status }}
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.radiologist.id_for_label }}" class="form-label">
                                    <i class="bi bi-person-badge"></i> أخصائي الأشعة
                                </label>
                                {{ form.radiologist }}
                            </div>
                        </div>

                        <!-- Dates -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.scan_date.id_for_label }}" class="form-label">
                                    <i class="bi bi-calendar-check"></i> تاريخ التصوير
                                </label>
                                {{ form.scan_date }}
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.result_date.id_for_label }}" class="form-label">
                                    <i class="bi bi-calendar-event"></i> تاريخ النتيجة
                                </label>
                                {{ form.result_date }}
                            </div>
                        </div>

                        <!-- Results -->
                        <div class="mb-3">
                            <label for="{{ form.results.id_for_label }}" class="form-label">
                                <i class="bi bi-file-text"></i> نتائج الأشعة
                            </label>
                            {{ form.results }}
                            <div class="form-text">أدخل النتائج الأولية للأشعة</div>
                        </div>

                        <!-- Findings -->
                        <div class="mb-3">
                            <label for="{{ form.findings.id_for_label }}" class="form-label">
                                <i class="bi bi-search"></i> الموجودات
                            </label>
                            {{ form.findings }}
                            <div class="form-text">وصف تفصيلي للموجودات في الصور</div>
                        </div>

                        <!-- Impression -->
                        <div class="mb-3">
                            <label for="{{ form.impression.id_for_label }}" class="form-label">
                                <i class="bi bi-lightbulb"></i> الانطباع التشخيصي
                            </label>
                            {{ form.impression }}
                            <div class="form-text">الانطباع التشخيصي النهائي</div>
                        </div>

                        <!-- Recommendations -->
                        <div class="mb-3">
                            <label for="{{ form.recommendations.id_for_label }}" class="form-label">
                                <i class="bi bi-clipboard-check"></i> التوصيات
                            </label>
                            {{ form.recommendations }}
                            <div class="form-text">التوصيات والخطوات التالية</div>
                        </div>

                        <!-- Radiologist Report -->
                        <div class="mb-3">
                            <label for="{{ form.radiologist_report.id_for_label }}" class="form-label">
                                <i class="bi bi-file-earmark-text"></i> تقرير أخصائي الأشعة
                            </label>
                            {{ form.radiologist_report }}
                            <div class="form-text">التقرير الطبي المفصل</div>
                        </div>

                        <!-- Abnormal Flag -->
                        <div class="mb-3">
                            <div class="form-check">
                                {{ form.is_abnormal }}
                                <label class="form-check-label text-danger" for="{{ form.is_abnormal.id_for_label }}">
                                    <i class="bi bi-exclamation-triangle"></i> نتيجة غير طبيعية
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="bi bi-check-circle"></i>
                                حفظ نتائج الأشعة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Rays Info Sidebar -->
        <div class="col-lg-4">
            <!-- Rays Information -->
            <div class="card shadow mb-3">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle"></i> معلومات الأشعة
                    </h5>
                </div>
                <div class="card-body">
                    <p><strong>رقم الأشعة:</strong><br>
                       <span class="badge bg-primary">{{ rays.rays_id }}</span></p>
                    
                    <p><strong>نوع الأشعة:</strong><br>
                       {{ rays.get_rays_name_display }}</p>
                    
                    <p><strong>الجزء المراد تصويره:</strong><br>
                       <span class="badge bg-secondary">{{ rays.part }}</span></p>
                    
                    <p><strong>استخدام الصبغة:</strong><br>
                       <span class="badge bg-{% if rays.UseOfSeed == 'yes' %}warning{% else %}success{% endif %}">
                           {{ rays.get_UseOfSeed_display }}
                       </span></p>
                    
                    <p><strong>الحالة الحالية:</strong><br>
                       <span class="badge bg-{{ rays.get_status_color }}">
                           {{ rays.get_status_display }}
                       </span></p>
                    
                    {% if rays.is_urgent %}
                    <p><strong>عاجل:</strong><br>
                       <span class="badge bg-danger">فحص عاجل</span></p>
                    {% endif %}
                    
                    <p><strong>تاريخ الطلب:</strong><br>
                       {{ rays.ordered_date|date:"d/m/Y H:i" }}</p>
                </div>
            </div>

            <!-- Patient Information -->
            <div class="card shadow mb-3">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-person"></i> معلومات المريض
                    </h5>
                </div>
                <div class="card-body">
                    <p><strong>الاسم:</strong><br>
                       <a href="{% url 'patient_detail' rays.patient.pk %}" class="text-decoration-none">
                           {{ rays.patient.full_name }}
                       </a></p>
                    
                    <p><strong>العمر:</strong><br>
                       {{ rays.patient.age }} سنة</p>
                    
                    <p><strong>الجنس:</strong><br>
                       {{ rays.patient.get_gender_display }}</p>
                    
                    <p><strong>الهاتف:</strong><br>
                       {{ rays.patient.phone }}</p>
                </div>
            </div>

            <!-- Doctor Information -->
            <div class="card shadow mb-3">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-person-badge"></i> الطبيب الطالب
                    </h5>
                </div>
                <div class="card-body">
                    <p><strong>الطبيب:</strong><br>
                       {{ rays.doctor.first_name }} {{ rays.doctor.last_name }}</p>
                    
                    {% if rays.linical %}
                    <p><strong>المؤشر السريري:</strong><br>
                       <small class="text-muted">{{ rays.linical|truncatechars:100 }}</small></p>
                    {% endif %}
                    
                    {% if rays.instructions %}
                    <p><strong>تعليمات خاصة:</strong><br>
                       <small class="text-muted">{{ rays.instructions|truncatechars:100 }}</small></p>
                    {% endif %}
                </div>
            </div>

            <!-- Patient Allergies Warning -->
            {% if rays.patient.allergies %}
            <div class="card shadow border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-exclamation-triangle"></i> تحذير - حساسيات المريض
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-danger mb-0">{{ rays.patient.allergies }}</p>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-fill result date when status changes to completed
document.getElementById('{{ form.status.id_for_label }}').addEventListener('change', function() {
    if (this.value === 'completed') {
        const now = new Date();
        const dateString = now.toISOString().slice(0, 16);
        document.getElementById('{{ form.result_date.id_for_label }}').value = dateString;
    }
});

// Highlight abnormal results
document.getElementById('{{ form.is_abnormal.id_for_label }}').addEventListener('change', function() {
    const resultsField = document.getElementById('{{ form.results.id_for_label }}');
    const findingsField = document.getElementById('{{ form.findings.id_for_label }}');
    
    if (this.checked) {
        resultsField.classList.add('border-danger');
        findingsField.classList.add('border-danger');
        resultsField.classList.remove('border-success');
        findingsField.classList.remove('border-success');
    } else {
        resultsField.classList.add('border-success');
        findingsField.classList.add('border-success');
        resultsField.classList.remove('border-danger');
        findingsField.classList.remove('border-danger');
    }
});
</script>
{% endblock %}
