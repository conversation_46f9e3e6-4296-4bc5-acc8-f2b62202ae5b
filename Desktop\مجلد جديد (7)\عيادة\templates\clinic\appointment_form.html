{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }} - نظام العيادة الطبية{% endblock %}

{% block extra_css %}
<style>
    .appointment-form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .form-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    }

    .info-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    }

    .form-section-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 20px;
    }

    .time-picker-container {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border-radius: 10px;
        padding: 15px;
        color: white;
    }

    .duration-selector {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .duration-btn {
        border: 2px solid #667eea;
        background: transparent;
        color: #667eea;
        border-radius: 25px;
        padding: 8px 16px;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .duration-btn:hover,
    .duration-btn.active {
        background: #667eea;
        color: white;
        transform: translateY(-2px);
    }

    .patient-info-card {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        border-radius: 10px;
        padding: 15px;
        color: white;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="appointment-form-header  bg-gradient-primary text-white rounded-3 p-4 mb-4 shadow" style="margin-top: 50px;">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    <div class="bg-white bg-opacity-25 rounded-circle p-3 me-3">
                        <i class="bi bi-calendar{% if appointment %}-gear{% else %}-plus{% endif %} fs-2 text-white"></i>
                    </div>
                    <div>
                        <h1 class="h2 mb-1 text-white">{{ title }}</h1>
                        <p class="mb-0 text-white-50">
                            {% if appointment %}
                                تعديل بيانات الموعد وإعادة جدولته
                            {% else %}
                                حجز موعد جديد للمريض
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{% url 'appointment_list' %}" class="btn btn-light btn-lg">
                    <i class="bi bi-arrow-right"></i> العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Patient Info Card (if editing) -->
{% if appointment %}
    <div class="patient-info-card mb-4">
        <div class="d-flex align-items-center">
            <div class="bg-white bg-opacity-25 rounded-circle p-2 me-3">
                <i class="bi bi-person fs-4"></i>
            </div>
            <div>
                <h5 class="mb-1">{{ appointment.patient.full_name }}</h5>
                <p class="mb-0 opacity-75">
                    <i class="bi bi-telephone"></i> {{ appointment.patient.phone }} |
                    <i class="bi bi-calendar"></i> {{ appointment.patient.age }} سنة
                </p>
            </div>
        </div>
    </div>
{% endif %}

<div class="row">
    <div class="col-lg-8">
        <div class="card form-card">
            <div class="card-body p-4">
                <form method="post" id="appointmentForm">
                    {% csrf_token %}
                    {% crispy form %}
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Quick Duration Selector -->
        <div class="card info-card mb-4">
            <div class="card-header bg-transparent border-0">
                <h6 class="card-title mb-0">
                    <i class="bi bi-clock"></i> اختيار سريع للمدة
                </h6>
            </div>
            <div class="card-body">
                <div class="duration-selector">
                    <button type="button" class="duration-btn btn filter-btn btn-outline-success" data-duration="15">15 دقيقة</button>
                    <button type="button" class="duration-btn btn filter-btn btn-outline-info" data-duration="30">30 دقيقة</button>
                    <button type="button" class="duration-btn btn filter-btn btn-outline-warning" data-duration="45">45 دقيقة</button>
                    <button type="button" class="duration-btn btn filter-btn btn-outline-primary" data-duration="60">ساعة</button>
                    <button type="button" class="duration-btn btn filter-btn btn-outline-secondary " data-duration="90">ساعة ونصف</button>
                    <button type="button" class="duration-btn btn btn-outline-primary" data-duration="120">ساعتان</button>
                </div>
            </div>
        </div>

        <!-- Tips Card -->
        <div class="card info-card mb-4">
            <div class="card-header bg-transparent border-0">
                <h6 class="card-title mb-0">
                    <i class="bi bi-lightbulb"></i> نصائح مهمة
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info border-0">
                    <ul class="mb-0 list-unstyled">
                        <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>تأكد من اختيار التاريخ والوقت المناسب</li>
                        <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>حدد المريض بدقة</li>
                        <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>اكتب سبب الزيارة بوضوح</li>
                        <li class="mb-0"><i class="bi bi-check-circle text-success me-2"></i>حدد المدة المتوقعة للموعد</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Working Hours Info -->
        <div class="card info-card mb-4">
            <div class="card-header bg-transparent border-0">
                <h6 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i> ساعات العمل
                </h6>
            </div>
            <div class="card-body">
                <div class="time-picker-container">
                    <div class="row text-center">
                        <div class="col-6">
                            <h6 class="mb-1">من</h6>
                            <p class="mb-0 fs-5">8:00 ص</p>
                        </div>
                        <div class="col-6">
                            <h6 class="mb-1">إلى</h6>
                            <p class="mb-0 fs-5">6:00 م</p>
                        </div>
                    </div>
                    <hr class="my-3 border-white">
                    <p class="mb-0 text-center">
                        <small>السبت - الخميس</small>
                    </p>
                </div>
            </div>
        </div>

        {% if appointment %}
            <div class="card info-card">
                <div class="card-header bg-transparent border-0">
                    <h6 class="card-title mb-0 text-warning">
                        <i class="bi bi-exclamation-triangle"></i> تحذير
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning border-0">
                        <p class="mb-0">
                            تعديل بيانات الموعد قد يؤثر على جدولة المواعيد الأخرى.
                            تأكد من صحة البيانات قبل الحفظ.
                        </p>
                    </div>
                </div>
            </div>
        {% endif %}

        {% if appointment %}
            <div class="card shadow mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history"></i> معلومات الموعد
                    </h5>
                </div>
                <div class="card-body">
                    <p><strong>تاريخ الإنشاء:</strong><br>
                       {{ appointment.created_at|date:"d/m/Y H:i" }}</p>
                    <p><strong>آخر تحديث:</strong><br>
                       {{ appointment.updated_at|date:"d/m/Y H:i" }}</p>
                    <p><strong>الحالة الحالية:</strong><br>
                       <span class="badge bg-{% if appointment.status == 'scheduled' %}primary{% elif appointment.status == 'completed' %}success{% else %}secondary{% endif %}">
                           {{ appointment.get_status_display }}
                       </span>
                    </p>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Duration quick selector
    $('.duration-btn').click(function() {
        const duration = $(this).data('duration');
        $('#id_duration').val(duration);

        // Update active state
        $('.duration-btn').removeClass('active');
        $(this).addClass('active');

        // Visual feedback
        $(this).effect('bounce', {times: 2}, 300);
    });

    // Set active duration button based on current value
    const currentDuration = $('#id_duration').val();
    if (currentDuration) {
        $(`.duration-btn[data-duration="${currentDuration}"]`).addClass('active');
    }

    // Form validation
    $('#appointmentForm').submit(function(e) {
        let isValid = true;
        const requiredFields = ['patient', 'appointment_date', 'reason'];

        requiredFields.forEach(function(field) {
            const fieldElement = $(`#id_${field}`);
            if (!fieldElement.val()) {
                isValid = false;
                fieldElement.addClass('is-invalid');

                // Add error message if not exists
                if (!fieldElement.next('.invalid-feedback').length) {
                    fieldElement.after('<div class="invalid-feedback">هذا الحقل مطلوب</div>');
                }
            } else {
                fieldElement.removeClass('is-invalid');
                fieldElement.next('.invalid-feedback').remove();
            }
        });

        if (!isValid) {
            e.preventDefault();

            // Show error alert
            if (!$('.alert-danger').length) {
                $('.form-card .card-body').prepend(`
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-triangle"></i>
                        يرجى ملء جميع الحقول المطلوبة
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `);
            }

            // Scroll to first error
            $('html, body').animate({
                scrollTop: $('.is-invalid').first().offset().top - 100
            }, 500);
        }
    });

    // Real-time validation
    $('input, select, textarea').on('blur', function() {
        const field = $(this);
        if (field.val()) {
            field.removeClass('is-invalid').addClass('is-valid');
            field.next('.invalid-feedback').remove();
        }
    });

    // Date and time validation
    $('#id_appointment_date').change(function() {
        const selectedDate = new Date($(this).val());
        const now = new Date();

        if (selectedDate < now) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">لا يمكن حجز موعد في الماضي</div>');
            }
        } else {
            $(this).removeClass('is-invalid').addClass('is-valid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    // Patient selection enhancement
    $('#id_patient').change(function() {
        const patientId = $(this).val();
        if (patientId) {
            // You can add AJAX call here to get patient details
            $(this).removeClass('is-invalid').addClass('is-valid');
        }
    });

    // Recurring appointment logic
    $('#id_is_recurring').change(function() {
        const recurringFields = $('#id_recurring_frequency, #id_recurring_end_date').closest('.form-group');
        if ($(this).is(':checked')) {
            recurringFields.show().find('input, select').prop('disabled', false);
        } else {
            recurringFields.hide().find('input, select').prop('disabled', true);
        }
    });

    // Initialize recurring fields visibility
    $('#id_is_recurring').trigger('change');

    // Animate form sections on load
    $('.form-card, .info-card').each(function(index) {
        $(this).css('opacity', '0').css('transform', 'translateY(20px)');
        $(this).delay(index * 100).animate({
            opacity: 1
        }, 500).css('transform', 'translateY(0)');
    });

    // Add loading state to submit button
    $('#appointmentForm').submit(function() {
        const submitBtn = $(this).find('button[type="submit"]');
        submitBtn.html('<i class="bi bi-hourglass-split"></i> جاري الحفظ...');
        submitBtn.prop('disabled', true);
    });

    // Auto-save draft (optional)
    let autoSaveTimeout;
    $('input, select, textarea').on('input change', function() {
        clearTimeout(autoSaveTimeout);
        autoSaveTimeout = setTimeout(function() {
            // Save form data to localStorage
            const formData = {};
            $('#appointmentForm').find('input, select, textarea').each(function() {
                formData[$(this).attr('name')] = $(this).val();
            });
            localStorage.setItem('appointmentFormDraft', JSON.stringify(formData));

            // Show auto-save indicator
            if (!$('.auto-save-indicator').length) {
                $('.form-card .card-body').append('<small class="auto-save-indicator text-muted"><i class="bi bi-check-circle text-success"></i> تم الحفظ التلقائي</small>');
                setTimeout(function() {
                    $('.auto-save-indicator').fadeOut();
                }, 2000);
            }
        }, 2000);
    });

    // Restore draft on page load
    const savedDraft = localStorage.getItem('appointmentFormDraft');
    if (savedDraft && !$('#appointmentForm input[name="patient"]').val()) {
        const formData = JSON.parse(savedDraft);
        Object.keys(formData).forEach(function(key) {
            $(`#appointmentForm [name="${key}"]`).val(formData[key]);
        });

        // Show restore notification
        $('.form-card .card-body').prepend(`
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="bi bi-info-circle"></i>
                تم استعادة البيانات المحفوظة تلقائياً
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);
    }

    // Clear draft on successful submit
    $('#appointmentForm').on('submit', function() {
        localStorage.removeItem('appointmentFormDraft');
    });

    // Set minimum date to today
    const dateInput = document.querySelector('input[name="appointment_date"]');
    if (dateInput) {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');

        const minDateTime = `${year}-${month}-${day}T${hours}:${minutes}`;
        dateInput.min = minDateTime;

        // Validate appointment time
        dateInput.addEventListener('change', function(e) {
            const selectedDate = new Date(e.target.value);
            const currentDate = new Date();

            if (selectedDate < currentDate) {
                alert('لا يمكن حجز موعد في الماضي');
                e.target.focus();
            }

            // Check if it's during working hours (8 AM to 6 PM)
            const hours = selectedDate.getHours();
            if (hours < 8 || hours >= 18) {
                if (confirm('الموعد خارج ساعات العمل (8 صباحاً - 6 مساءً). هل تريد المتابعة؟')) {
                    // User confirmed, allow the time
                } else {
                    e.target.focus();
                }
            }
        });
    }

    // Auto-calculate end time based on duration
    const durationInput = document.querySelector('input[name="duration"]');
    if (durationInput && dateInput) {
        function updateEndTime() {
            const startTime = new Date(dateInput.value);
            const duration = parseInt(durationInput.value) || 30;

            if (!isNaN(startTime.getTime())) {
                const endTime = new Date(startTime.getTime() + duration * 60000);
                const endTimeStr = endTime.toLocaleTimeString('ar-SA', {
                    hour: '2-digit',
                    minute: '2-digit'
                });

                // Show end time info
                let infoDiv = document.getElementById('end-time-info');
                if (!infoDiv) {
                    infoDiv = document.createElement('div');
                    infoDiv.id = 'end-time-info';
                    infoDiv.className = 'alert alert-info mt-2';
                    durationInput.parentNode.appendChild(infoDiv);
                }
                infoDiv.innerHTML = `<small><i class="bi bi-clock"></i> وقت انتهاء الموعد المتوقع: ${endTimeStr}</small>`;
            }
        }

        dateInput.addEventListener('change', updateEndTime);
        durationInput.addEventListener('input', updateEndTime);
    }
});
</script>
{% endblock %}
