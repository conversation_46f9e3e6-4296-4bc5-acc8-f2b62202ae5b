from django.contrib import admin
from .models import MedicalRecord


@admin.register(MedicalRecord)
class MedicalRecordAdmin(admin.ModelAdmin):
    list_display = ['record_id', 'patient', 'doctor', 'department', 'record_type', 'priority', 'is_completed', 'created_at']
    list_filter = ['record_type', 'priority', 'is_completed', 'is_confidential', 'department', 'created_at']
    search_fields = ['record_id', 'patient__first_name', 'patient__last_name', 'doctor__first_name', 'doctor__last_name']
    readonly_fields = ['record_id', 'created_at', 'updated_at']
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('record_id', 'patient', 'doctor', 'department', 'record_type', 'priority')
        }),
        ('التاريخ الطبي', {
            'fields': ('chief_complaint', 'history_of_present_illness', 'past_medical_history', 'family_history', 'social_history')
        }),
        ('الفحص والتقييم', {
            'fields': ('physical_examination', 'assessment', 'plan')
        }),
        ('إعدادات', {
            'fields': ('is_confidential', 'is_completed', 'completed_at')
        }),
        ('التواريخ', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
