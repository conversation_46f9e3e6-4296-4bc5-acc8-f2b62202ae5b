{% extends 'base.html' %}

{% block title %}لوحة التحكم - نظام العيادة الطبية{% endblock %}

{% block extra_css %}
{% load static %}
<link rel="stylesheet" href="{% static 'css/dashboard-enhanced.css' %}">
<style>
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }
    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }
    .border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }
    .border-left-warning {
        border-left: 0.25rem solid #f6c23e !important;
    }
    .text-gray-300 {
        color: #dddfeb !important;
    }
    .text-gray-800 {
        color: #5a5c69 !important;
    }
    .progress-sm {
        height: 0.5rem;
    }
    .stat-card {
        transition: all 0.3s ease;
        border-radius: 15px;
    }
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }
    .quick-stats {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        color: white;
    }
    .activity-card {
        border-radius: 15px;
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }
    .activity-item {
        padding: 1rem;
        border-bottom: 1px solid #e3e6f0;
        transition: background-color 0.3s;
    }
    .activity-item:hover {
        background-color: #f8f9fc;
    }
    .activity-item:last-child {
        border-bottom: none;
    }
    .progress-circle {
        width: 60px;
        height: 60px;
    }
</style>
{% endblock %}

{% block content %}
<!-- مؤشر التحميل -->
<div class="loading-indicator position-fixed top-0 start-0 w-100 h-100 d-none"
     style="background: rgba(0,0,0,0.5); z-index: 9999;">
    <div class="d-flex justify-content-center align-items-center h-100">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
    </div>
</div>

<!-- Page Header -->
<div class="bg-gradient-primary text-white rounded-3 p-4 mb-4 shadow" style="margin-top: 50px;">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    <div class="bg-white bg-opacity-25 rounded-circle p-3 me-3">
                        <i class="bi bi-speedometer2 fs-2 text-white"></i>
                    </div>
                    <div>
                        <h1 class="h2 mb-1 text-white">لوحة التحكم</h1>
                        <p class="mb-0 text-white-50">مرحباً بك في نظام إدارة العيادة الطبية</p>
                        <small class="text-white-50">
                            <i class="bi bi-person"></i> {{ user.get_full_name|default:user.username }}
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="text-white mb-3">
                    <div class="mb-2">
                        <i class="bi bi-calendar3"></i> {{ "now"|date:"l, d F Y" }}
                    </div>
                    <div class="current-time">
                        <i class="bi bi-clock"></i> {{ "now"|date:"H:i" }}
                    </div>
                </div>
                <!-- بحث سريع -->
                <div class="input-group input-group-sm">
                    <input type="text" class="form-control bg-white bg-opacity-25 border-0 text-white"
                           placeholder="بحث سريع..." id="quickSearch"
                           style="backdrop-filter: blur(10px);">
                    <button class="btn btn-outline-light border-0" type="button">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <!-- المرضى -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2 stat-card dashboard-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            إجمالي المرضى
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" data-counter="{{ total_patients }}">{{ total_patients }}</div>
                        <div class="text-xs text-success mt-1">
                            <i class="bi bi-arrow-up"></i> {{ new_patients_month }} هذا الشهر
                        </div>
                        <div class="text-xs text-muted">
                            <i class="bi bi-calendar-week"></i> {{ new_patients_week }} هذا الأسبوع
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-people fs-2 text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- المواعيد -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2 stat-card dashboard-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            مواعيد اليوم
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" data-countern="{{ today_appointments_count }}">{{ today_appointments_count }}</div>
                        <div class="text-xs text-info mt-1">
                            <i class="bi bi-calendar-check"></i> {{ pending_appointments }} معلقة
                        </div>
                        <div class="text-xs text-muted">
                            <i class="bi bi-check-circle"></i> {{ completed_appointments }} مكتملة
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-calendar-event fs-2 text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الفحوصات المخبرية -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2 stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            الفحوصات المخبرية
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_lab_tests }}</div>
                        <div class="row no-gutters align-items-center mt-1">
                            <div class="col-auto">
                                <div class="h6 mb-0 mr-3 font-weight-bold text-gray-800">{{ lab_completion_rate }}%</div>
                            </div>
                            <div class="col">
                                <div class="progress progress-sm mr-2">
                                    <div class="progress-bar bg-info" role="progressbar"
                                         style="width: {{ lab_completion_rate }}%" aria-valuenow="{{ lab_completion_rate }}"
                                         aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-clipboard-data fs-2 text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الأشعة -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2 stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            الأشعة والتصوير
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_rays }}</div>
                        <div class="row no-gutters align-items-center mt-1">
                            <div class="col-auto">
                                <div class="h6 mb-0 mr-3 font-weight-bold text-gray-800">{{ rays_completion_rate }}%</div>
                            </div>
                            <div class="col">
                                <div class="progress progress-sm mr-2">
                                    <div class="progress-bar bg-warning" role="progressbar"
                                         style="width: {{ rays_completion_rate }}%" aria-valuenow="{{ rays_completion_rate }}"
                                         aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-camera fs-2 text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card activity-card quick-stats">
            <div class="card-header text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning-charge"></i> إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{% url 'patient_create' %}" class="btn btn-light btn-block h-100 d-flex flex-column justify-content-center align-items-center text-decoration-none">
                            <i class="bi bi-person-plus fs-1 mb-2"></i>
                            <span>إضافة مريض</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{% url 'appointment_create' %}" class="btn btn-light btn-block h-100 d-flex flex-column justify-content-center align-items-center text-decoration-none">
                            <i class="bi bi-calendar-plus fs-1 mb-2"></i>
                            <span>حجز موعد</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{% url 'lab' %}" class="btn btn-light btn-block h-100 d-flex flex-column justify-content-center align-items-center text-decoration-none">
                            <i class="bi bi-clipboard-data fs-1 mb-2"></i>
                            <span>طلب فحص</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{% url 'rays' %}" class="btn btn-light btn-block h-100 d-flex flex-column justify-content-center align-items-center text-decoration-none">
                            <i class="bi bi-camera fs-1 mb-2"></i>
                            <span>طلب أشعة</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{% url 'prescription_create' %}" class="btn btn-light btn-block h-100 d-flex flex-column justify-content-center align-items-center text-decoration-none">
                            <i class="bi bi-prescription2 fs-1 mb-2"></i>
                            <span>كتابة روشتة</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{% url 'payment_create' %}" class="btn btn-light btn-block h-100 d-flex flex-column justify-content-center align-items-center text-decoration-none">
                            <i class="bi bi-credit-card fs-1 mb-2"></i>
                            <span>تسجيل دفعة</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Performance Overview -->
<div class="row mb-4">
    <div class="col-lg-8 mb-4">
        <div class="card activity-card">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-bar-chart"></i> نظرة عامة على الأداء
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center mb-3">
                        <div class="progress-circle mx-auto mb-2 d-flex align-items-center justify-content-center bg-primary text-white rounded-circle">
                            <span class="h5 mb-0">{{ completion_rate }}%</span>
                        </div>
                        <h6 class="text-primary">معدل إكمال المواعيد</h6>
                        <small class="text-muted">{{ completed_appointments }} من {{ total_appointments }}</small>
                    </div>
                    <div class="col-md-4 text-center mb-3">
                        <div class="progress-circle mx-auto mb-2 d-flex align-items-center justify-content-center bg-info text-white rounded-circle">
                            <span class="h5 mb-0">{{ lab_completion_rate }}%</span>
                        </div>
                        <h6 class="text-info">معدل إكمال الفحوصات</h6>
                        <small class="text-muted">{{ completed_lab_tests }} من {{ total_lab_tests }}</small>
                    </div>
                    <div class="col-md-4 text-center mb-3">
                        <div class="progress-circle mx-auto mb-2 d-flex align-items-center justify-content-center bg-warning text-white rounded-circle">
                            <span class="h5 mb-0">{{ rays_completion_rate }}%</span>
                        </div>
                        <h6 class="text-warning">معدل إكمال الأشعة</h6>
                        <small class="text-muted">{{ completed_rays }} من {{ total_rays }}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-4">
        <div class="card activity-card">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-success">
                    <i class="bi bi-cash-stack"></i> الإيرادات
                </h6>
            </div>
            <div class="card-body text-center">
                <div class="h2 mb-0 font-weight-bold text-success">
                    {{ total_revenue|floatformat:0 }} ج.م
                </div>
                <div class="text-muted">إجمالي الإيرادات</div>
                <hr>
                <div class="text-sm">
                    <i class="bi bi-receipt"></i> {{ total_payments }} عملية دفع
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Recent Activity -->
<div class="row">
    <!-- Recent Patients -->
    <div class="col-lg-6 mb-4">
        <div class="card activity-card">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-people"></i> المرضى الجدد
                </h6>
                <a href="{% url 'patient_list' %}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body p-0">
                {% if recent_patients %}
                    {% for patient in recent_patients %}
                        <div class="activity-item d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="bg-primary rounded-circle p-2 me-3">
                                    <i class="bi bi-person text-white"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">{{ patient.full_name }}</h6>
                                    <small class="text-muted">{{ patient.phone }}</small>
                                </div>
                            </div>
                            <div class="text-end">
                                <small class="text-muted">{{ patient.created_at|date:"d/m/Y" }}</small>
                                <br>
                                <a href="{% url 'patient_detail' patient.id %}" class="btn btn-sm btn-outline-primary">
                                    عرض
                                </a>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-people fs-1 text-muted"></i>
                        <p class="text-muted mt-2">لا توجد مرضى جدد</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Today's Appointments -->
    <div class="col-lg-6 mb-4">
        <div class="card activity-card">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-success">
                    <i class="bi bi-calendar-check"></i> مواعيد اليوم
                </h6>
                <a href="{% url 'appointment_list' %}" class="btn btn-sm btn-outline-success">
                    عرض الكل
                </a>
            </div>
            <div class="card-body p-0">
                {% if today_appointments %}
                    {% for appointment in today_appointments %}
                        <div class="activity-item d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="bg-success rounded-circle p-2 me-3">
                                    <i class="bi bi-calendar-event text-white"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">{{ appointment.patient.full_name }}</h6>
                                    <small class="text-muted">{{ appointment.reason|truncatechars:30 }}</small>
                                </div>
                            </div>
                            <div class="text-end">
                                <small class="text-muted">{{ appointment.appointment_date|date:"H:i" }}</small>
                                <br>
                                <span class="badge bg-{% if appointment.status == 'completed' %}success{% elif appointment.status == 'scheduled' %}warning{% else %}secondary{% endif %}">
                                    {% if appointment.status == 'completed' %}مكتمل
                                    {% elif appointment.status == 'scheduled' %}مجدول
                                    {% else %}{{ appointment.status }}{% endif %}
                                </span>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-calendar-x fs-1 text-muted"></i>
                        <p class="text-muted mt-2">لا توجد مواعيد اليوم</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recent Lab Tests and Rays -->
<div class="row">
    <!-- Recent Lab Tests -->
    <div class="col-lg-6 mb-4">
        <div class="card activity-card">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="bi bi-clipboard-data"></i> الفحوصات الحديثة
                </h6>
                <a href="{% url 'record_list' %}" class="btn btn-sm btn-outline-info">
                    عرض الكل
                </a>
            </div>
            <div class="card-body p-0">
                {% if recent_lab_tests %}
                    {% for lab_test in recent_lab_tests %}
                        <div class="activity-item d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="bg-info rounded-circle p-2 me-3">
                                    <i class="bi bi-clipboard-data text-white"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">{{ lab_test.patient.full_name }}</h6>
                                    <small class="text-muted">{{ lab_test.test_type }}</small>
                                </div>
                            </div>
                            <div class="text-end">
                                <small class="text-muted">{{ lab_test.ordered_date|date:"d/m" }}</small>
                                <br>
                                {% if lab_test.results %}
                                    <span class="badge bg-success">مكتمل</span>
                                {% else %}
                                    <span class="badge bg-warning">معلق</span>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-clipboard-data fs-1 text-muted"></i>
                        <p class="text-muted mt-2">لا توجد فحوصات حديثة</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recent Rays -->
    <div class="col-lg-6 mb-4">
        <div class="card activity-card">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-warning">
                    <i class="bi bi-camera"></i> الأشعة الحديثة
                </h6>
                <a href="{% url 'rays_list' %}" class="btn btn-sm btn-outline-warning">
                    عرض الكل
                </a>
            </div>
            <div class="card-body p-0">
                {% if recent_rays %}
                    {% for ray in recent_rays %}
                        <div class="activity-item d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="bg-warning rounded-circle p-2 me-3">
                                    <i class="bi bi-camera text-white"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">{{ ray.patient.full_name }}</h6>
                                    <small class="text-muted">{{ ray.ray_type }}</small>
                                </div>
                            </div>
                            <div class="text-end">
                                <small class="text-muted">{{ ray.ordered_date|date:"d/m" }}</small>
                                <br>
                                {% if ray.results %}
                                    <span class="badge bg-success">مكتمل</span>
                                {% else %}
                                    <span class="badge bg-warning">معلق</span>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-camera fs-1 text-muted"></i>
                        <p class="text-muted mt-2">لا توجد أشعة حديثة</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="{% static 'js/dashboard-enhanced.js' %}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // إضافة data-counter للأرقام المتحركة
    document.querySelectorAll('.h5').forEach(element => {
        const value = element.textContent.trim();
        if (!isNaN(value) && value !== '') {
            element.setAttribute('data-counter', value);
            element.textContent = '{{total_patients}}';
        }
    });
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.h5').forEach(element => {
        const value = element.textContent.trim();
        if (!isNaN(value) && value !== '') {
            element.setAttribute('data-countern', value);
            element.textContent = ' {{today_appointments_count}}';
        }
    });

    // تحديث الوقت كل دقيقة
    setInterval(function() {
        const now = new Date();
        const timeElement = document.querySelector('.current-time');
        if (timeElement) {
            timeElement.textContent = now.toLocaleTimeString('ar-EG', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }
    }, 60000);

    // تأثيرات التحويم على البطاقات
    document.querySelectorAll('.stat-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // إضافة تأثيرات للأزرار السريعة
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            // تأثير الموجة
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255,255,255,0.5);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            `;

            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // إضافة CSS للتأثير
    const style = document.createElement('style');
    style.textContent = `
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);

    // تحديث البيانات كل 5 دقائق (اختياري)
    // setInterval(function() {
    //     location.reload();
    // }, 300000);
});
</script>
{% endblock %}
