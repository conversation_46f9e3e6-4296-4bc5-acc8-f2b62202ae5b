{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }} - نظام العيادة الطبية{% endblock %}

{% block extra_css %}
<style>
.payment-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    transition: all 0.3s ease;
}

.payment-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.form-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 1.5rem;
}

.info-card {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    border: none;
    color: white;
}

.calculator-card {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border: none;
    color: #333;
}

.payment-methods-card {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border: none;
    color: #333;
}

.total-amount {
    font-size: 1.5rem;
    font-weight: bold;
    color: #28a745;
}

.amount-breakdown {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
}
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap align-items-start pt-3 pb-2 mb-3 border-bottom" style="margin-top: 50px;">
    <h1 class="h2 mb-2 mb-md-0">
        <i class="bi bi-credit-card"></i>
        {{ title }}
    </h1>
    <div class="btn-toolbar">
        <a href="{% url 'payment_list' %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right"></i> العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card payment-card">
            <div class="card-header form-section">
                <h5 class="card-title mb-0">
                    <i class="bi bi-credit-card"></i> تسجيل دفعة جديدة
                </h5>
            </div>
            <div class="card-body">
                <form method="post" id="paymentForm">
                    {% csrf_token %}
                    {% crispy form %}
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card payment-card info-card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i> إرشادات الدفع
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-light">
                    <h6><i class="bi bi-lightbulb text-warning"></i> نصائح مهمة:</h6>
                    <ul class="mb-0 small">
                        <li>تأكد من صحة المبلغ المدفوع</li>
                        <li>اختر طريقة الدفع الصحيحة</li>
                        <li>اكتب وصف واضح للخدمة</li>
                        <li>احفظ رقم مرجع الدفع الإلكتروني</li>
                        <li>سيتم إنشاء رقم إيصال تلقائياً</li>
                    </ul>
                </div>
            </div>
        </div>

                {% if payment %}
                    <div class="alert alert-warning">
                        <h6><i class="bi bi-exclamation-triangle"></i> تحذير:</h6>
                        <p class="mb-0">
                            تعديل بيانات الدفعة قد يؤثر على التقارير المالية.
                            تأكد من صحة البيانات قبل الحفظ.
                        </p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- حاسبة المبالغ -->
        <div class="card payment-card calculator-card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-calculator"></i> حاسبة المبالغ
                </h5>
            </div>
            <div class="card-body">
                <div class="amount-breakdown">
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">المبلغ الأساسي:</small>
                            <div id="baseAmount">0.00 ريال</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">الخصم:</small>
                            <div id="discountDisplay">0.00 ريال</div>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-6">
                            <small class="text-muted">الضريبة:</small>
                            <div id="taxDisplay">0.00 ريال</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">المبلغ الإجمالي:</small>
                            <div class="total-amount" id="totalDisplay">0.00 ريال</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- طرق الدفع المتاحة -->
        <div class="card payment-card payment-methods-card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-credit-card-2-front"></i> طرق الدفع المتاحة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 mb-2">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-cash-coin text-success me-2"></i>
                            <span class="small">نقدي</span>
                        </div>
                    </div>
                    <div class="col-6 mb-2">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-credit-card text-primary me-2"></i>
                            <span class="small">بطاقة ائتمان</span>
                        </div>
                    </div>
                    <div class="col-6 mb-2">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-bank text-info me-2"></i>
                            <span class="small">تحويل بنكي</span>
                        </div>
                    </div>
                    <div class="col-6 mb-2">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-shield-check text-warning me-2"></i>
                            <span class="small">تأمين</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {% if payment %}
            <div class="card shadow mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history"></i> معلومات الدفعة
                    </h5>
                </div>
                <div class="card-body">
                    <p><strong>رقم الإيصال:</strong><br>
                       {{ payment.receipt_number }}</p>
                    <p><strong>تاريخ الدفع:</strong><br>
                       {{ payment.payment_date|date:"d/m/Y H:i" }}</p>
                    <p><strong>الحالة الحالية:</strong><br>
                       {% if payment.status == 'paid' %}
                           <span class="badge bg-success">{{ payment.get_status_display }}</span>
                       {% elif payment.status == 'pending' %}
                           <span class="badge bg-warning">{{ payment.get_status_display }}</span>
                       {% elif payment.status == 'cancelled' %}
                           <span class="badge bg-danger">{{ payment.get_status_display }}</span>
                       {% else %}
                           <span class="badge bg-secondary">{{ payment.get_status_display }}</span>
                       {% endif %}
                    </p>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced amount calculation system
    const amountInput = document.querySelector('input[name="amount"]');
    const discountInput = document.querySelector('input[name="discount_amount"]');
    const taxInput = document.querySelector('input[name="tax_amount"]');

    function updateAmountDisplay() {
        const baseAmount = parseFloat(amountInput?.value || 0);
        const discount = parseFloat(discountInput?.value || 0);
        const tax = parseFloat(taxInput?.value || 0);

        const total = baseAmount - discount + tax;

        // Update display elements
        document.getElementById('baseAmount').textContent = baseAmount.toFixed(2) + ' ريال';
        document.getElementById('discountDisplay').textContent = discount.toFixed(2) + ' ريال';
        document.getElementById('taxDisplay').textContent = tax.toFixed(2) + ' ريال';
        document.getElementById('totalDisplay').textContent = total.toFixed(2) + ' ريال';

        // Update the main total amount display in form
        const totalAmountElement = document.getElementById('totalAmount');
        if (totalAmountElement) {
            totalAmountElement.textContent = total.toFixed(2);
        }
    }

    // Auto-format numeric inputs
    [amountInput, discountInput, taxInput].forEach(input => {
        if (input) {
            input.addEventListener('input', function(e) {
                let value = e.target.value.replace(/[^\d.]/g, '');

                // Ensure only one decimal point
                const parts = value.split('.');
                if (parts.length > 2) {
                    value = parts[0] + '.' + parts.slice(1).join('');
                }

                // Limit decimal places to 2
                if (parts[1] && parts[1].length > 2) {
                    value = parts[0] + '.' + parts[1].substring(0, 2);
                }

                e.target.value = value;
                updateAmountDisplay();
            });

            // Validate on blur
            input.addEventListener('blur', function(e) {
                const value = parseFloat(e.target.value);
                if (isNaN(value) || value < 0) {
                    e.target.value = '0.00';
                    updateAmountDisplay();
                }
            });
        }
    });

    // Initial calculation
    updateAmountDisplay();

    // Payment type change handler
    const paymentTypeSelect = document.querySelector('select[name="payment_type"]');
    if (paymentTypeSelect) {
        paymentTypeSelect.addEventListener('change', function(e) {
            const selectedType = e.target.value;
            const descriptionField = document.querySelector('textarea[name="description"]');

            if (descriptionField && !descriptionField.value.trim()) {
                let defaultDescription = '';
                switch(selectedType) {
                    case 'consultation':
                        defaultDescription = 'رسوم كشف طبي';
                        break;
                    case 'procedure':
                        defaultDescription = 'رسوم إجراء طبي';
                        break;
                    case 'medication':
                        defaultDescription = 'رسوم أدوية';
                        break;
                    case 'lab_test':
                        defaultDescription = 'رسوم فحص مخبري';
                        break;
                    case 'radiology':
                        defaultDescription = 'رسوم أشعة وتصوير طبي';
                        break;
                    case 'other':
                        defaultDescription = 'رسوم خدمات أخرى';
                        break;
                }
                descriptionField.value = defaultDescription;
            }
        });
    }

    // Payment method change handler
    const paymentMethodSelect = document.querySelector('select[name="payment_method"]');
    const paymentReferenceField = document.querySelector('input[name="payment_reference"]');

    if (paymentMethodSelect) {
        paymentMethodSelect.addEventListener('change', function(e) {
            const selectedMethod = e.target.value;

            // Show/hide payment reference field based on method
            if (paymentReferenceField) {
                const referenceGroup = paymentReferenceField.closest('.form-group');
                if (selectedMethod === 'card' || selectedMethod === 'bank_transfer') {
                    referenceGroup.style.display = 'block';
                    paymentReferenceField.required = true;
                } else {
                    referenceGroup.style.display = 'none';
                    paymentReferenceField.required = false;
                    paymentReferenceField.value = '';
                }
            }
        });

        // Trigger initial change
        paymentMethodSelect.dispatchEvent(new Event('change'));
    }

    // Form validation
    const form = document.querySelector('#paymentForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            const amount = parseFloat(amountInput?.value || 0);
            const patient = document.querySelector('select[name="patient"]').value;

            if (!patient) {
                e.preventDefault();
                alert('يرجى اختيار المريض');
                return false;
            }

            if (amount <= 0) {
                e.preventDefault();
                alert('يرجى إدخال مبلغ صحيح أكبر من صفر');
                amountInput?.focus();
                return false;
            }

            // Show loading state
            const submitBtn = form.querySelector('input[type="submit"]');
            submitBtn.value = 'جاري الحفظ...';
            submitBtn.disabled = true;
        });
    }

    // Auto-generate invoice number
    const invoiceField = document.querySelector('input[name="invoice_number"]');
    if (invoiceField && !invoiceField.value) {
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        const time = String(today.getHours()).padStart(2, '0') + String(today.getMinutes()).padStart(2, '0');

        invoiceField.placeholder = `INV-${year}${month}${day}-${time}`;
    }

    // Add animation to cards
    const cards = document.querySelectorAll('.payment-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
