@echo off
chcp 65001 >nul
title نظام العيادة الطبية - Medical Clinic System

echo.
echo ========================================
echo    🏥 نظام العيادة الطبية
echo    Medical Clinic Management System
echo ========================================
echo.
echo 🚀 بدء تشغيل النظام...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت. يرجى تثبيت Python أولاً.
    echo 📥 تحميل من: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

REM تشغيل النظام
python run_clinic.py

REM في حالة الخطأ
if %errorlevel% neq 0 (
    echo.
    echo ❌ حدث خطأ في تشغيل النظام
    echo 🔧 تجربة إصلاح المشاكل...
    echo.
    
    echo 📦 تثبيت المكتبات المطلوبة...
    pip install -r requirements.txt
    
    echo 🗄️ إعداد قاعدة البيانات...
    python manage.py makemigrations
    python manage.py migrate
    
    echo 🔄 إعادة المحاولة...
    python run_clinic.py
)

pause
