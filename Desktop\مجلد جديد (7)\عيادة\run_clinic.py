#!/usr/bin/env python
"""
نظام العيادة الطبية - ملف التشغيل الرئيسي
Medical Clinic System - Main Runner
"""

import os
import sys
import subprocess
import threading
import time
import webbrowser
from pathlib import Path

# إضافة مسار المشروع إلى Python path
BASE_DIR = Path(__file__).resolve().parent
sys.path.insert(0, str(BASE_DIR))

# تعيين متغير البيئة لـ Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'clinic_system.settings')

try:
    import django
    from django.core.management import execute_from_command_line
    from django.core.management.commands.runserver import Command as RunServerCommand
    from django.conf import settings
except ImportError as exc:
    raise ImportError(
        "Couldn't import Django. Are you sure it's installed and "
        "available on your PYTHONPATH environment variable? Did you "
        "forget to activate a virtual environment?"
    ) from exc


class ClinicSystemRunner:
    """فئة تشغيل نظام العيادة الطبية"""
    
    def __init__(self):
        self.host = '127.0.0.1'
        self.port = '8000'
        self.server_process = None
        
    def setup_django(self):
        """إعداد Django"""
        try:
            django.setup()
            print("✓ تم إعداد Django بنجاح")
            return True
        except Exception as e:
            print(f"✗ خطأ في إعداد Django: {e}")
            return False
    
    def check_database(self):
        """فحص قاعدة البيانات وإنشاؤها إذا لزم الأمر"""
        try:
            from django.core.management import call_command
            from django.db import connection
            
            # فحص الاتصال بقاعدة البيانات
            connection.ensure_connection()
            
            # تشغيل migrations إذا لزم الأمر
            print("🔄 فحص قاعدة البيانات...")
            call_command('makemigrations', verbosity=0, interactive=False)
            call_command('migrate', verbosity=0, interactive=False)
            print("✓ قاعدة البيانات جاهزة")
            
            return True
        except Exception as e:
            print(f"✗ خطأ في قاعدة البيانات: {e}")
            return False
    
    def create_superuser_if_needed(self):
        """إنشاء مستخدم إداري إذا لم يكن موجوداً"""
        try:
            from django.contrib.auth.models import User
            
            if not User.objects.filter(is_superuser=True).exists():
                print("🔄 إنشاء حساب المدير...")
                User.objects.create_superuser(
                    username='admin',
                    email='<EMAIL>',
                    password='admin123',
                    first_name='مدير',
                    last_name='النظام'
                )
                print("✓ تم إنشاء حساب المدير:")
                print("   اسم المستخدم: admin")
                print("   كلمة المرور: admin123")
            
            return True
        except Exception as e:
            print(f"✗ خطأ في إنشاء المستخدم الإداري: {e}")
            return False
    
    def start_server(self):
        """تشغيل خادم Django"""
        try:
            print(f"🚀 بدء تشغيل الخادم على {self.host}:{self.port}")
            
            # تشغيل الخادم في thread منفصل
            def run_server():
                execute_from_command_line([
                    'manage.py', 'runserver', 
                    f'{self.host}:{self.port}',
                    '--noreload'
                ])
            
            server_thread = threading.Thread(target=run_server, daemon=True)
            server_thread.start()
            
            # انتظار حتى يصبح الخادم جاهزاً
            self.wait_for_server()
            
            return True
        except Exception as e:
            print(f"✗ خطأ في تشغيل الخادم: {e}")
            return False
    
    def wait_for_server(self):
        """انتظار حتى يصبح الخادم جاهزاً"""
        import socket
        import time
        
        max_attempts = 30
        for attempt in range(max_attempts):
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex((self.host, int(self.port)))
                sock.close()
                
                if result == 0:
                    print("✓ الخادم جاهز!")
                    return True
                    
            except Exception:
                pass
            
            print(f"⏳ انتظار الخادم... ({attempt + 1}/{max_attempts})")
            time.sleep(1)
        
        print("⚠️ انتهت مهلة انتظار الخادم")
        return False
    
    def open_browser(self):
        """فتح المتصفح"""
        try:
            url = f'http://{self.host}:{self.port}'
            print(f"🌐 فتح المتصفح: {url}")
            webbrowser.open(url)
            return True
        except Exception as e:
            print(f"✗ خطأ في فتح المتصفح: {e}")
            return False
    
    def run(self):
        """تشغيل النظام الكامل"""
        print("=" * 50)
        print("🏥 نظام العيادة الطبية")
        print("Medical Clinic Management System")
        print("=" * 50)
        
        # إعداد Django
        if not self.setup_django():
            input("اضغط Enter للخروج...")
            return False
        
        # فحص قاعدة البيانات
        if not self.check_database():
            input("اضغط Enter للخروج...")
            return False
        
        # إنشاء المستخدم الإداري
        if not self.create_superuser_if_needed():
            input("اضغط Enter للخروج...")
            return False
        
        # تشغيل الخادم
        if not self.start_server():
            input("اضغط Enter للخروج...")
            return False
        
        # فتح المتصفح
        self.open_browser()
        
        print("\n" + "=" * 50)
        print("✅ النظام يعمل بنجاح!")
        print(f"🌐 الرابط: http://{self.host}:{self.port}")
        print("📱 يمكنك الآن استخدام النظام من المتصفح")
        print("=" * 50)
        print("\n⚠️ لإيقاف النظام، اضغط Ctrl+C أو أغلق هذه النافذة")
        
        try:
            # إبقاء البرنامج يعمل
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 إيقاف النظام...")
            return True


def main():
    """الدالة الرئيسية"""
    try:
        runner = ClinicSystemRunner()
        runner.run()
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        input("اضغط Enter للخروج...")


if __name__ == '__main__':
    main()
