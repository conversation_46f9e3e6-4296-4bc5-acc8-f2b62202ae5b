# 🏥 نظام العيادة الطبية - دليل المستخدم

## 🚀 تشغيل النظام بسهولة

### الطريقة الأولى: استخدام الملف التنفيذي (الأسهل)
1. انقر مرتين على ملف `ClinicSystem.bat`
2. اختر "1" لتشغيل النظام
3. انتظر حتى يفتح المتصفح تلقائياً
4. استخدم بيانات الدخول:
   - **اسم المستخدم:** `admin`
   - **كلمة المرور:** `admin123`

### الطريقة الثانية: التشغيل اليدوي
1. افتح Command Prompt
2. انتقل إلى مجلد المشروع
3. شغل الأمر: `python run_clinic.py`

## 📋 المتطلبات

- **Windows 10 أو أحدث**
- **Python 3.8+** (تحميل من: https://www.python.org/downloads/)
- **اتصال بالإنترنت** (لتثبيت المكتبات في أول مرة)

## 🔧 الإعداد الأولي

### إذا كانت هذه أول مرة تستخدم النظام:

1. شغل `ClinicSystem.bat`
2. اختر "2" للإعداد الأولي
3. انتظر حتى يكتمل تثبيت المكتبات
4. أنشئ حساب المدير عند الطلب
5. ارجع للقائمة الرئيسية واختر "1" لتشغيل النظام

## 🌐 استخدام النظام

بعد تشغيل النظام:

1. **سيفتح المتصفح تلقائياً** على العنوان: http://127.0.0.1:8000
2. **سجل الدخول** باستخدام بيانات المدير
3. **استخدم القوائل الجانبية** للتنقل بين الأقسام:
   - 👥 **المرضى:** إضافة وإدارة بيانات المرضى
   - 📅 **المواعيد:** جدولة وإدارة المواعيد
   - 🩺 **التشخيصات:** تسجيل التشخيصات الطبية
   - 💊 **الوصفات الطبية:** كتابة وإدارة الوصفات
   - 💳 **المدفوعات:** تسجيل وتتبع المدفوعات

## 💾 النسخ الاحتياطي

### إنشاء نسخة احتياطية:
1. شغل `ClinicSystem.bat`
2. اختر "4" للنسخ الاحتياطي
3. سيتم حفظ النسخة في مجلد `backups`

### استعادة نسخة احتياطية:
1. شغل `ClinicSystem.bat`
2. اختر "5" لاستعادة النسخة
3. اختر الملف المطلوب من القائمة

## 🔄 تحديث النظام

1. شغل `ClinicSystem.bat`
2. اختر "3" لتحديث النظام
3. انتظر حتى يكتمل التحديث

## ❓ حل المشاكل الشائعة

### مشكلة: "Python is not recognized"
**الحل:**
1. تأكد من تثبيت Python من الموقع الرسمي
2. أثناء التثبيت، تأكد من تحديد "Add Python to PATH"
3. أعد تشغيل الكمبيوتر بعد التثبيت

### مشكلة: النظام لا يعمل
**الحل:**
1. شغل `ClinicSystem.bat`
2. اختر "2" للإعداد الأولي
3. إذا استمرت المشكلة، احذف ملف `db.sqlite3` وأعد الإعداد

### مشكلة: المتصفح لا يفتح تلقائياً
**الحل:**
1. افتح المتصفح يدوياً
2. اذهب إلى العنوان: http://127.0.0.1:8000

### مشكلة: نسيت كلمة مرور المدير
**الحل:**
1. أوقف النظام (Ctrl+C)
2. شغل الأمر: `python manage.py createsuperuser`
3. أنشئ حساب مدير جديد

## 📱 استخدام النظام على الشبكة المحلية

لاستخدام النظام من أجهزة أخرى على نفس الشبكة:

1. عدل ملف `run_clinic.py`
2. غير `self.host = '127.0.0.1'` إلى `self.host = '0.0.0.0'`
3. شغل النظام
4. من الأجهزة الأخرى، اذهب إلى: `http://[عنوان_IP_للجهاز]:8000`

## 🔒 الأمان

- **غير كلمة مرور المدير** بعد أول تشغيل
- **انسخ قاعدة البيانات** بانتظام
- **لا تشارك بيانات الدخول** مع غير المخولين
- **أغلق النظام** عند عدم الاستخدام

## 📞 الدعم الفني

في حالة وجود مشاكل:

1. **تحقق من ملف error.log** إن وجد
2. **شغل النظام من Command Prompt** لرؤية رسائل الخطأ
3. **تأكد من تحديث Windows** و Python
4. **أعد تثبيت المكتبات** باختيار "3" من القائمة

## 📊 معلومات النظام

لعرض معلومات مفصلة عن النظام:
1. شغل `ClinicSystem.bat`
2. اختر "6" لمعلومات النظام

---

**🏥 نظام العيادة الطبية - إصدار 1.0.0**  
**Medical Clinic Management System**

*شكراً لاستخدام نظامنا! 🙏*



حرارة مرتفعة, سعال جاف,صداع

التهاب رئوي

راحة متابعة الاكسجين