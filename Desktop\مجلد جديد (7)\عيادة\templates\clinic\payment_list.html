{% extends 'base.html' %}

{% block title %}قائمة المدفوعات - نظام العيادة الطبية{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="bg-gradient-success text-white rounded-3 p-4 mb-4 shadow" style="margin-top: 50px;">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    <div class="bg-white bg-opacity-25 rounded-circle p-3 me-3">
                        <i class="bi bi-credit-card fs-2 text-white"></i>
                    </div>
                    <div>
                        <h1 class="h2 mb-1 text-white">قائمة المدفوعات</h1>
                        <p class="mb-0 text-white-50">إدارة وتتبع المدفوعات والفواتير</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{% url 'payment_create' %}" class="btn btn-light btn-lg">
                    <i class="bi bi-plus-circle"></i>
                    <span class="d-none d-sm-inline">إضافة دفعة جديدة</span>
                    <span class="d-inline d-sm-none">إضافة دفعة</span>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter Form -->
<div class="row mb-3">
    <div class="col-12 col-md-10 col-lg-8">
        <form method="get" class="d-flex flex-wrap gap-2">
            <input type="text" name="search" class="form-control flex-grow-1"
                   placeholder="البحث بالمريض أو رقم الإيصال..."
                   value="{{ search_query }}">
            <select name="status" class="form-select" style="width: auto;">
                <option value="">جميع الحالات</option>
                {% for value, label in status_choices %}
                    <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                {% endfor %}
            </select>
            <button type="submit" class="btn btn-outline-secondary">
                <i class="bi bi-search"></i>
                <span class="d-none d-sm-inline">بحث</span>
            </button>
        </form>
    </div>
</div>

<!-- Payments Table -->
<div class="card shadow">
    <div class="card-body">
        {% if page_obj %}
            <!-- Desktop Table View -->
            <div class="table-responsive d-none d-lg-block">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>رقم الإيصال</th>
                            <th>المريض</th>
                            <th>المبلغ</th>
                            <th>طريقة الدفع</th>
                            <th>الحالة</th>
                            <th>التاريخ</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in page_obj %}
                            <tr>
                                <td>
                                    <a href="{% url 'payment_detail' payment.pk %}" class="text-decoration-none">
                                        {{ payment.receipt_number }}
                                    </a>
                                </td>
                                <td>
                                    <a href="{% url 'patient_detail' payment.patient.pk %}" class="text-decoration-none">
                                        {{ payment.patient.full_name }}
                                    </a>
                                </td>
                                <td><strong>{{ payment.amount }} ريال</strong></td>
                                <td>{{ payment.get_payment_method_display }}</td>
                                <td>
                                    {% if payment.status == 'paid' %}
                                        <span class="badge bg-success">{{ payment.get_status_display }}</span>
                                    {% elif payment.status == 'pending' %}
                                        <span class="badge bg-warning">{{ payment.get_status_display }}</span>
                                    {% elif payment.status == 'cancelled' %}
                                        <span class="badge bg-danger">{{ payment.get_status_display }}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ payment.get_status_display }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ payment.payment_date|date:"d/m/Y H:i" }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'payment_detail' payment.pk %}"
                                           class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="{% url 'payment_update' payment.pk %}"
                                           class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="{% url 'payment_delete' payment.pk %}"
                                           class="btn btn-sm btn-outline-danger" title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Mobile Card View -->
            <div class="d-lg-none">
                {% for payment in page_obj %}
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="card-title mb-1">
                                        <a href="{% url 'payment_detail' payment.pk %}" class="text-decoration-none">
                                            {{ payment.receipt_number }}
                                        </a>
                                    </h6>
                                    <p class="card-text mb-2">
                                        <a href="{% url 'patient_detail' payment.patient.pk %}" class="text-decoration-none">
                                            {{ payment.patient.full_name }}
                                        </a>
                                    </p>
                                    <p class="card-text mb-1">
                                        <strong class="text-success">{{ payment.amount }} ريال</strong>
                                    </p>
                                    <p class="card-text mb-1">
                                        <small class="text-muted">
                                            <i class="bi bi-credit-card"></i> {{ payment.get_payment_method_display }}
                                        </small>
                                    </p>
                                    <p class="card-text">
                                        <small class="text-muted">
                                            <i class="bi bi-calendar"></i> {{ payment.payment_date|date:"d/m/Y H:i" }}
                                        </small>
                                    </p>
                                </div>
                                <div class="text-end">
                                    {% if payment.status == 'paid' %}
                                        <span class="badge bg-success mb-2">{{ payment.get_status_display }}</span>
                                    {% elif payment.status == 'pending' %}
                                        <span class="badge bg-warning mb-2">{{ payment.get_status_display }}</span>
                                    {% elif payment.status == 'cancelled' %}
                                        <span class="badge bg-danger mb-2">{{ payment.get_status_display }}</span>
                                    {% else %}
                                        <span class="badge bg-secondary mb-2">{{ payment.get_status_display }}</span>
                                    {% endif %}
                                    <br>
                                    <div class="btn-group-vertical" role="group">
                                        <a href="{% url 'payment_detail' payment.pk %}"
                                           class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="{% url 'payment_update' payment.pk %}"
                                           class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="{% url 'payment_delete' payment.pk %}"
                                           class="btn btn-sm btn-outline-danger" title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
                <nav aria-label="Payment pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                                    الأولى
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                                    السابقة
                                </a>
                            </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                                        {{ num }}
                                    </a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                                    التالية
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                                    الأخيرة
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}

        {% else %}
            <div class="text-center py-5">
                <i class="bi bi-credit-card-2-front display-1 text-muted"></i>
                <h4 class="mt-3">لا توجد مدفوعات</h4>
                <p class="text-muted">لم يتم العثور على أي مدفوعات{% if search_query %} للبحث "{{ search_query }}"{% endif %}</p>
                <a href="{% url 'payment_create' %}" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> إضافة أول دفعة
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
