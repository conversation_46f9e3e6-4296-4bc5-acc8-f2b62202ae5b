{% extends 'control_panels/base_dashboard.html' %}
{% load static %}

{% block title %}تعديل السجل الطبي - {{ record.record_id }}{% endblock %}

{% block extra_css %}
<style>
    .edit-record-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .record-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-bottom: 30px;
        overflow: hidden;
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .record-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        text-align: center;
    }
    
    .record-title {
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 10px;
    }
    
    .form-section {
        background: white;
        border-radius: 10px;
        padding: 30px;
        margin: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    
    .section-header {
        margin-bottom: 25px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e9ecef;
    }
    
    .section-title {
        color: #495057;
        font-size: 1.3rem;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }
    
    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 12px 15px;
        transition: all 0.3s ease;
        font-size: 1rem;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .required {
        color: #dc3545;
    }
    
    .patient-info-card {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .patient-name {
        font-size: 1.2rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 10px;
    }
    
    .patient-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }
    
    .detail-item {
        display: flex;
        justify-content: space-between;
        padding: 5px 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .detail-label {
        font-weight: 600;
        color: #6c757d;
    }
    
    .detail-value {
        color: #495057;
    }
    
    .form-actions {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e9ecef;
        text-align: center;
    }
    
    .btn-primary-action {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 25px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-size: 1.1rem;
    }
    
    .btn-primary-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        color: white;
    }
    
    .btn-secondary {
        background: #6c757d;
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 25px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-size: 1.1rem;
        margin-left: 15px;
    }
    
    .btn-secondary:hover {
        background: #5a6268;
        color: white;
        transform: translateY(-2px);
    }
    
    @media (max-width: 768px) {
        .patient-details {
            grid-template-columns: 1fr;
        }
        
        .form-actions .btn-primary-action,
        .form-actions .btn-secondary {
            display: block;
            width: 100%;
            margin: 10px 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="edit-record-container">
    <div class="container-fluid">
        <!-- رأس الصفحة -->
        <div class="record-card">
            <div class="record-header">
                <h1 class="record-title">
                    <i class="fas fa-edit me-3"></i>
                    تعديل السجل الطبي
                </h1>
                <p>رقم السجل: {{ record.record_id }}</p>
            </div>
        </div>
        
        <!-- نموذج تعديل السجل -->
        <div class="record-card">
            <div class="form-section">
                <!-- معلومات المريض -->
                <div class="patient-info-card">
                    <div class="patient-name">{{ record.patient.full_name }}</div>
                    <div class="patient-details">
                        <div class="detail-item">
                            <span class="detail-label">رقم المريض:</span>
                            <span class="detail-value">{{ record.patient.patient_id }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">العمر:</span>
                            <span class="detail-value">{{ record.patient.get_age }} سنة</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">الجنس:</span>
                            <span class="detail-value">{{ record.patient.get_gender_display }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">فصيلة الدم:</span>
                            <span class="detail-value">{{ record.patient.blood_type|default:"غير محدد" }}</span>
                        </div>
                    </div>
                </div>
                
                <form method="post" id="editRecordForm">
                    {% csrf_token %}
                    
                    <!-- معلومات أساسية -->
                    <div class="section-header">
                        <h3 class="section-title">
                            <i class="fas fa-info-circle"></i>
                            المعلومات الأساسية
                        </h3>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="record_type" class="form-label">نوع السجل <span class="required">*</span></label>
                            <select class="form-select" id="record_type" name="record_type" required>
                                <option value="CONSULTATION" {% if record.record_type == 'CONSULTATION' %}selected{% endif %}>استشارة</option>
                                <option value="EMERGENCY" {% if record.record_type == 'EMERGENCY' %}selected{% endif %}>طوارئ</option>
                                <option value="ADMISSION" {% if record.record_type == 'ADMISSION' %}selected{% endif %}>دخول</option>
                                <option value="FOLLOW_UP" {% if record.record_type == 'FOLLOW_UP' %}selected{% endif %}>متابعة</option>
                                <option value="SURGERY" {% if record.record_type == 'SURGERY' %}selected{% endif %}>جراحة</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="priority" class="form-label">الأولوية <span class="required">*</span></label>
                            <select class="form-select" id="priority" name="priority" required>
                                <option value="NORMAL" {% if record.priority == 'NORMAL' %}selected{% endif %}>عادي</option>
                                <option value="HIGH" {% if record.priority == 'HIGH' %}selected{% endif %}>عالي</option>
                                <option value="URGENT" {% if record.priority == 'URGENT' %}selected{% endif %}>عاجل</option>
                                <option value="CRITICAL" {% if record.priority == 'CRITICAL' %}selected{% endif %}>حرج</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- الشكوى الرئيسية -->
                    <div class="section-header">
                        <h3 class="section-title">
                            <i class="fas fa-comment-medical"></i>
                            الشكوى الرئيسية
                        </h3>
                    </div>
                    
                    <div class="mb-3">
                        <label for="chief_complaint" class="form-label">الشكوى الرئيسية <span class="required">*</span></label>
                        <textarea class="form-control" id="chief_complaint" name="chief_complaint"
                                  rows="3" required>{{ record.chief_complaint }}</textarea>
                    </div>
                    
                    <!-- التاريخ المرضي -->
                    <div class="section-header">
                        <h3 class="section-title">
                            <i class="fas fa-history"></i>
                            التاريخ المرضي
                        </h3>
                    </div>
                    
                    <div class="mb-3">
                        <label for="history_of_present_illness" class="form-label">تاريخ المرض الحالي <span class="required">*</span></label>
                        <textarea class="form-control" id="history_of_present_illness" name="history_of_present_illness"
                                  rows="4" required>{{ record.history_of_present_illness }}</textarea>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="past_medical_history" class="form-label">التاريخ المرضي السابق</label>
                            <textarea class="form-control" id="past_medical_history" name="past_medical_history"
                                      rows="3">{{ record.past_medical_history }}</textarea>
                        </div>
                        <div class="col-md-6">
                            <label for="family_history" class="form-label">التاريخ العائلي</label>
                            <textarea class="form-control" id="family_history" name="family_history"
                                      rows="3">{{ record.family_history }}</textarea>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="social_history" class="form-label">التاريخ الاجتماعي</label>
                        <textarea class="form-control" id="social_history" name="social_history"
                                  rows="2">{{ record.social_history }}</textarea>
                    </div>
                    
                    <!-- الفحص السريري -->
                    <div class="section-header">
                        <h3 class="section-title">
                            <i class="fas fa-stethoscope"></i>
                            الفحص السريري
                        </h3>
                    </div>
                    
                    <div class="mb-3">
                        <label for="physical_examination" class="form-label">الفحص الجسدي <span class="required">*</span></label>
                        <textarea class="form-control" id="physical_examination" name="physical_examination"
                                  rows="4" required>{{ record.physical_examination }}</textarea>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label for="vital_signs_temperature" class="form-label">درجة الحرارة (°C)</label>
                            <input type="number" step="0.1" class="form-control" id="vital_signs_temperature" 
                                   name="vital_signs_temperature" value="{{ record.vital_signs_temperature }}">
                        </div>
                        <div class="col-md-3">
                            <label for="vital_signs_blood_pressure" class="form-label">ضغط الدم</label>
                            <input type="text" class="form-control" id="vital_signs_blood_pressure" 
                                   name="vital_signs_blood_pressure" value="{{ record.vital_signs_blood_pressure }}"
                                   placeholder="120/80">
                        </div>
                        <div class="col-md-3">
                            <label for="vital_signs_heart_rate" class="form-label">نبضات القلب</label>
                            <input type="number" class="form-control" id="vital_signs_heart_rate" 
                                   name="vital_signs_heart_rate" value="{{ record.vital_signs_heart_rate }}">
                        </div>
                        <div class="col-md-3">
                            <label for="vital_signs_respiratory_rate" class="form-label">معدل التنفس</label>
                            <input type="number" class="form-control" id="vital_signs_respiratory_rate" 
                                   name="vital_signs_respiratory_rate" value="{{ record.vital_signs_respiratory_rate }}">
                        </div>
                    </div>
                    
                    <!-- التقييم والخطة -->
                    <div class="section-header">
                        <h3 class="section-title">
                            <i class="fas fa-clipboard-check"></i>
                            التقييم والخطة
                        </h3>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="assessment" class="form-label">التقييم</label>
                            <textarea class="form-control" id="assessment" name="assessment"
                                      rows="3">{{ record.assessment }}</textarea>
                        </div>
                        <div class="col-md-6">
                            <label for="plan" class="form-label">الخطة العلاجية</label>
                            <textarea class="form-control" id="plan" name="plan"
                                      rows="3">{{ record.plan }}</textarea>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-control" id="notes" name="notes"
                                  rows="2">{{ record.notes }}</textarea>
                    </div>
                    
                    <!-- حالة السجل -->
                    <div class="section-header">
                        <h3 class="section-title">
                            <i class="fas fa-flag"></i>
                            حالة السجل
                        </h3>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_completed" name="is_completed"
                                       {% if record.is_completed %}checked{% endif %}>
                                <label class="form-check-label" for="is_completed">
                                    السجل مكتمل
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="follow_up_date" class="form-label">موعد المتابعة</label>
                            <input type="date" class="form-control" id="follow_up_date" name="follow_up_date"
                                   value="{{ record.follow_up_date|date:'Y-m-d' }}">
                        </div>
                    </div>
                    
                    <!-- إجراءات النموذج -->
                    <div class="form-actions">
                        <button type="submit" class="btn-primary-action">
                            <i class="fas fa-save"></i>
                            حفظ التغييرات
                        </button>
                        <a href="{% url 'medical_records:record_detail' record.id %}" class="btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// التحقق من صحة النموذج
document.getElementById('editRecordForm').addEventListener('submit', function(event) {
    const chiefComplaint = document.getElementById('chief_complaint').value.trim();
    const historyOfPresentIllness = document.getElementById('history_of_present_illness').value.trim();
    const physicalExamination = document.getElementById('physical_examination').value.trim();

    if (!chiefComplaint || !historyOfPresentIllness || !physicalExamination) {
        event.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
        return false;
    }
});

// تنسيق ضغط الدم
document.getElementById('vital_signs_blood_pressure').addEventListener('input', function() {
    let value = this.value.replace(/[^\d\/]/g, '');
    this.value = value;
});

// التحقق من القيم الرقمية
document.querySelectorAll('input[type="number"]').forEach(input => {
    input.addEventListener('input', function() {
        if (this.value < 0) {
            this.value = 0;
        }
    });
});
</script>
{% endblock %}
