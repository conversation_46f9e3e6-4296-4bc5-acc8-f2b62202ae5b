{% extends 'base.html' %}

{% block title %}تفاصيل الدفعة - نظام العيادة الطبية{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="bg-gradient-success text-white rounded-3 p-4 mb-4 shadow" style="margin-top: 50px;">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    <div class="bg-white bg-opacity-25 rounded-circle p-3 me-3">
                        <i class="bi bi-receipt fs-2 text-white"></i>
                    </div>
                    <div>
                        <h1 class="h2 mb-1 text-white">تفاصيل الدفعة</h1>
                        <p class="mb-0 text-white-50">
                            <i class="bi bi-hash"></i> {{ payment.receipt_number }} •
                            <i class="bi bi-calendar"></i> {{ payment.payment_date|date:"d/m/Y" }}
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="btn-group mb-2">
                    <a href="{% url 'payment_update' payment.pk %}" class="btn btn-warning">
                        <i class="bi bi-pencil"></i>
                        <span class="d-none d-lg-inline">تعديل</span>
                    </a>
                    <button onclick="printReceipt()" class="btn btn-light">
                        <i class="bi bi-printer"></i>
                        <span class="d-none d-lg-inline">طباعة</span>
                    </button>
                    <a href="{% url 'payment_delete' payment.pk %}" class="btn btn-danger">
                        <i class="bi bi-trash"></i>
                        <span class="d-none d-lg-inline">حذف</span>
                    </a>
                </div>
                <br>
                <a href="{% url 'payment_list' %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-right"></i>
                    <span class="d-none d-sm-inline">العودة للقائمة</span>
                    <span class="d-inline d-sm-none">عودة</span>
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Payment Receipt -->
    <div class="col-lg-8 mb-4">
        <!-- Receipt Header -->
        <div class="card shadow mb-4" id="receipt-content">
            <div class="card-header bg-success text-white">
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-hospital"></i> نظام العيادة الطبية
                        </h5>
                        <small>إيصال دفع</small>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <p class="mb-0"><strong>رقم الإيصال:</strong> {{ payment.receipt_number }}</p>
                        <p class="mb-0"><strong>التاريخ:</strong> {{ payment.payment_date|date:"d/m/Y H:i" }}</p>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Payment Information -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-success">معلومات المريض:</h6>
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td><strong>الاسم:</strong></td>
                                <td>{{ payment.patient.full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>العمر:</strong></td>
                                <td>{{ payment.patient.age }} سنة</td>
                            </tr>
                            <tr>
                                <td><strong>الجنس:</strong></td>
                                <td>{{ payment.patient.get_gender_display }}</td>
                            </tr>
                            <tr>
                                <td><strong>الهاتف:</strong></td>
                                <td>{{ payment.patient.phone }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-success">تفاصيل الدفع:</h6>
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td><strong>المبلغ:</strong></td>
                                <td class="text-success fw-bold fs-5">{{ payment.amount }} ريال</td>
                            </tr>
                            <tr>
                                <td><strong>طريقة الدفع:</strong></td>
                                <td>{{ payment.get_payment_method_display }}</td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    {% if payment.status == 'paid' %}
                                        <span class="badge bg-success fs-6">{{ payment.get_status_display }}</span>
                                    {% elif payment.status == 'pending' %}
                                        <span class="badge bg-warning fs-6">{{ payment.get_status_display }}</span>
                                    {% elif payment.status == 'cancelled' %}
                                        <span class="badge bg-danger fs-6">{{ payment.get_status_display }}</span>
                                    {% else %}
                                        <span class="badge bg-secondary fs-6">{{ payment.get_status_display }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الدفع:</strong></td>
                                <td>{{ payment.payment_date|date:"d/m/Y H:i" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Service Description -->
                <div class="mb-4">
                    <h6 class="text-success border-bottom pb-2">
                        <i class="bi bi-clipboard-text"></i> وصف الخدمة:
                    </h6>
                    <div class="bg-light p-3 rounded">
                        <p class="mb-0">{{ payment.description }}</p>
                    </div>
                </div>

                <!-- Notes -->
                {% if payment.notes %}
                <div class="mb-4">
                    <h6 class="text-success border-bottom pb-2">
                        <i class="bi bi-sticky"></i> ملاحظات:
                    </h6>
                    <div class="bg-light p-3 rounded">
                        <p class="mb-0">{{ payment.notes }}</p>
                    </div>
                </div>
                {% endif %}

                <!-- Payment Summary -->
                <div class="row">
                    <div class="col-md-6 offset-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>المبلغ الأساسي:</span>
                                    <span>{{ payment.amount }} ريال</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>الضريبة (0%):</span>
                                    <span>0.00 ريال</span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between fw-bold fs-5">
                                    <span>المجموع:</span>
                                    <span class="text-success">{{ payment.amount }} ريال</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="text-center mt-4 pt-3 border-top">
                    <small class="text-muted">
                        شكراً لك على ثقتك في نظام العيادة الطبية - {{ payment.payment_date|date:"d/m/Y H:i" }}
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Payment Status -->
        <div class="card shadow mb-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i> حالة الدفعة
                </h5>
            </div>
            <div class="card-body text-center">
                {% if payment.status == 'paid' %}
                    <div class="bg-success bg-opacity-10 rounded-circle p-3 d-inline-block mb-3">
                        <i class="bi bi-check-circle fs-1 text-success"></i>
                    </div>
                    <h6 class="text-success">تم الدفع بنجاح</h6>
                    <p class="text-muted mb-0">تم استلام المبلغ كاملاً</p>
                {% elif payment.status == 'pending' %}
                    <div class="bg-warning bg-opacity-10 rounded-circle p-3 d-inline-block mb-3">
                        <i class="bi bi-clock fs-1 text-warning"></i>
                    </div>
                    <h6 class="text-warning">في انتظار الدفع</h6>
                    <p class="text-muted mb-0">لم يتم استلام المبلغ بعد</p>
                {% elif payment.status == 'cancelled' %}
                    <div class="bg-danger bg-opacity-10 rounded-circle p-3 d-inline-block mb-3">
                        <i class="bi bi-x-circle fs-1 text-danger"></i>
                    </div>
                    <h6 class="text-danger">تم الإلغاء</h6>
                    <p class="text-muted mb-0">تم إلغاء هذه الدفعة</p>
                {% else %}
                    <div class="bg-secondary bg-opacity-10 rounded-circle p-3 d-inline-block mb-3">
                        <i class="bi bi-question-circle fs-1 text-secondary"></i>
                    </div>
                    <h6 class="text-secondary">حالة غير محددة</h6>
                    <p class="text-muted mb-0">يرجى مراجعة الإدارة</p>
                {% endif %}
            </div>
        </div>

        <!-- Patient Info -->
        <div class="card shadow mb-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person-badge"></i> معلومات المريض
                </h5>
            </div>
            <div class="card-body">
                <p><strong>الاسم:</strong><br>
                   <a href="{% url 'patient_detail' payment.patient.pk %}" class="text-decoration-none">
                       {{ payment.patient.full_name }}
                   </a>
                </p>
                <p><strong>العمر:</strong> {{ payment.patient.age }} سنة</p>
                <p><strong>الجنس:</strong> {{ payment.patient.get_gender_display }}</p>
                <p><strong>الهاتف:</strong> {{ payment.patient.phone }}</p>
                {% if payment.patient.email %}
                    <p><strong>البريد الإلكتروني:</strong> {{ payment.patient.email }}</p>
                {% endif %}
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card shadow mb-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning"></i> إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'payment_create' %}?patient_id={{ payment.patient.id }}"
                       class="btn btn-outline-success btn-sm">
                        <i class="bi bi-plus-circle"></i> دفعة جديدة
                    </a>
                    <a href="{% url 'appointment_create' %}?patient_id={{ payment.patient.id }}"
                       class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-calendar-plus"></i> حجز موعد
                    </a>
                    <a href="{% url 'prescription_create' %}?patient_id={{ payment.patient.id }}"
                       class="btn btn-outline-warning btn-sm">
                        <i class="bi bi-prescription2"></i> وصفة طبية
                    </a>
                </div>
            </div>
        </div>

        <!-- Payment Method Info -->
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-credit-card-2-front"></i> طريقة الدفع
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center">
                    {% if payment.payment_method == 'cash' %}
                        <i class="bi bi-cash-coin fs-2 text-success me-3"></i>
                        <div>
                            <h6 class="mb-1">دفع نقدي</h6>
                            <small class="text-muted">تم الدفع نقداً</small>
                        </div>
                    {% elif payment.payment_method == 'card' %}
                        <i class="bi bi-credit-card fs-2 text-primary me-3"></i>
                        <div>
                            <h6 class="mb-1">بطاقة ائتمان</h6>
                            <small class="text-muted">تم الدفع بالبطاقة</small>
                        </div>
                    {% elif payment.payment_method == 'bank_transfer' %}
                        <i class="bi bi-bank fs-2 text-info me-3"></i>
                        <div>
                            <h6 class="mb-1">تحويل بنكي</h6>
                            <small class="text-muted">تم التحويل البنكي</small>
                        </div>
                    {% elif payment.payment_method == 'insurance' %}
                        <i class="bi bi-shield-check fs-2 text-warning me-3"></i>
                        <div>
                            <h6 class="mb-1">تأمين طبي</h6>
                            <small class="text-muted">مغطى بالتأمين</small>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function printReceipt() {
    // Hide non-printable elements
    const sidebar = document.querySelector('.col-lg-4');
    const pageHeader = document.querySelector('.bg-gradient-success');

    if (sidebar) sidebar.style.display = 'none';
    if (pageHeader) pageHeader.style.display = 'none';

    // Print
    window.print();

    // Restore elements
    if (sidebar) sidebar.style.display = 'block';
    if (pageHeader) pageHeader.style.display = 'block';
}

// Print styles
const printStyles = `
    @media print {
        .col-lg-4, .bg-gradient-success { display: none !important; }
        .col-lg-8 { width: 100% !important; }
        .card { border: 1px solid #000 !important; box-shadow: none !important; }
        .card-header { background-color: #f8f9fa !important; color: #000 !important; }
        body { font-size: 12pt; }
        .h2, .h5, .h6 { font-size: 14pt; }
        .fs-5 { font-size: 16pt !important; }
        .badge { border: 1px solid #000; }
    }
`;

// Add print styles to head
const styleSheet = document.createElement('style');
styleSheet.textContent = printStyles;
document.head.appendChild(styleSheet);
</script>
{% endblock %}
