from django.urls import path
from . import views

app_name = 'medical_records'

urlpatterns = [
    # قائمة السجلات الطبية
    path('', views.record_list, name='record_list'),

    # تفاصيل السجل الطبي
    path('<int:record_id>/', views.record_detail, name='record_detail'),

    # إضافة سجل طبي جديد
    path('add/', views.add_record, name='add_record'),

    # تعديل سجل طبي
    path('<int:record_id>/edit/', views.edit_record, name='edit_record'),

    # إضافة تشخيص
    path('<int:record_id>/add-diagnosis/', views.add_diagnosis, name='add_diagnosis'),
    path('add-diagnosis/', views.add_diagnosis, name='add_diagnosis_standalone'),

    # إضافة علاج
    path('<int:record_id>/add-treatment/', views.add_treatment, name='add_treatment'),
    path('add-treatment/', views.add_treatment, name='add_treatment_standalone'),

    # إضافة فحص مخبري
    path('<int:record_id>/add-lab-test/', views.add_lab_test, name='add_lab_test'),
    path('add-lab-test/', views.add_lab_test, name='add_lab_test_standalone'),

    # إضافة دراسة إشعاعية
    path('<int:record_id>/add-radiology/', views.add_radiology, name='add_radiology'),
    path('add-radiology/', views.add_radiology, name='add_radiology_standalone'),

    # طباعة السجل
    path('<int:record_id>/print/', views.print_record, name='print_record'),

    # تصدير السجلات
    path('export/', views.export_records, name='export_records'),

    # البحث المتقدم
    path('search/', views.search_records, name='search'),
]
