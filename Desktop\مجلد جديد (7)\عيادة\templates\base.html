<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام العيادة الطبية{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">


    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
<!--    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">-->

    <!-- Google Fonts - Arabic -->
<!--    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">-->

    <style>


        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 2px 10px;
            transition: all 0.3s;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(-5px);
        }

        .main-content {
            margin-right: 250px;
            padding: 20px;
            transition: all 0.3s;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
            transition: transform 0.3s ease;
            margin-bottom: 20px;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-bottom: 15px;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .table-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .page-header {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
        }
        .btn-primaryn {
            background: #6c757d;
            border: none;
            border-radius: 2px;
            color: #fff;
        }
        .btn-primaryn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .navbar-brand {
            font-weight: bold;
            font-size: 1.3rem;
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .main-content {
                margin-right: 0;
            }
        }

        .quick-action {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s;
            text-decoration: none;
            color: #495057;
            display: block;
            margin-bottom: 10px;
        }

        .quick-action:hover {
            border-color: #667eea;
            color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }

        .quick-action i {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }
    </style>
<style>
    .add-radiology-container {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        min-height: 100vh;
        padding: 20px 0;
    }

    .radiology-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-bottom: 30px;
        overflow: hidden;
        max-width: 1000px;
        margin: 0 auto;
    }

    .radiology-header {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        color: white;
        padding: 30px;
        text-align: center;
    }

    .radiology-title {
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 10px;
    }

    .form-section {
        background: white;
        border-radius: 10px;
        padding: 30px;
        margin: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .section-header {
        margin-bottom: 25px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e9ecef;
    }

    .section-title {
        color: #495057;
        font-size: 1.3rem;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }

    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 12px 15px;
        transition: all 0.3s ease;
        font-size: 1rem;
    }

    .form-control:focus, .form-select:focus {
        border-color: #6f42c1;
        box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.25);
    }

    .required {
        color: #dc3545;
    }

    .radiology-type-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 15px;
    }

    .radiology-type-card {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
    }

    .radiology-type-card:hover {
        border-color: #6f42c1;
        background: #f8f9fa;
    }

    .radiology-type-card.selected {
        border-color: #6f42c1;
        background: #f3e5f5;
    }

    .radiology-icon {
        font-size: 2rem;
        color: #6f42c1;
        margin-bottom: 10px;
    }

    .radiology-name {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }

    .radiology-desc {
        font-size: 0.875rem;
        color: #6c757d;
    }

    .form-actions {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e9ecef;
        text-align: center;
    }

    .btn-primary-action {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 25px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-size: 1.1rem;
    }

    .btn-primary-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(111, 66, 193, 0.3);
        color: white;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 25px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-size: 1.1rem;
        margin-left: 15px;
    }

    .btn-secondary:hover {
        background: #5a6268;
        color: white;
        transform: translateY(-2px);
    }

    @media (max-width: 768px) {
        .radiology-type-grid {
            grid-template-columns: 1fr;
        }

        .form-actions .btn-primary-action,
        .form-actions .btn-secondary {
            display: block;
            width: 100%;
            margin: 10px 0;
        }
    }
</style>

    <style>

        .element{
            -webkit-transition: all 0.5s;
            -moz-transition: all 0.5s;
            -ms-transition: all 0.5s;
            transition: all 0.5s;
        }


        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            position: fixed;
            top: 56px;
            right: 0;
            width: 250px;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease-in-out;
        }
        .sidebar.show {
            transform: translateX(0);
        }
        .sidebar .nav-link {
            color: #adb5bd;
            padding: 0.75rem 1rem;
            border-radius: 0;
        }
        .sidebar .nav-link:hover {
            color: #fff;
            background-color: #495057;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #007bff;
        }
        .main-content {
            padding: 20px;
            transition: margin-right 0.3s ease-in-out;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
            margin-bottom: 1rem;
        }
        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }
        .table-responsive {
            border-radius: 0.375rem;
        }

        /* Mobile First Responsive Design */
        @media (min-width: 768px) {
            .sidebar {
                transform: translateX(0);
                position: fixed;
            }
            .main-content {
                margin-right: 250px;
            }
            .mobile-menu-btn {
                display: none !important;
            }
        }

        @media (max-width: 767.98px) {
            .sidebar {
                width: 100%;
                transform: translateX(100%);
            }
            .sidebar.show {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 0;
                padding: 15px;
            }
            .card {
                margin-bottom: 1rem;
            }
            .table-responsive {
                font-size: 0.875rem;
            }
            .btn-group .btn {
                padding: 0.25rem 0.5rem;
                font-size: 0.875rem;
            }
            .d-flex.justify-content-between {
                flex-direction: column;
                gap: 1rem;
            }
            .btn-toolbar {
                margin-bottom: 1rem !important;
            }
        }

        @media (max-width: 575.98px) {
            .main-content {
                padding: 10px;
            }
            .card-body {
                padding: 1rem;
            }
            .table-responsive {
                font-size: 0.8rem;
            }
            .btn-group .btn {
                padding: 0.2rem 0.4rem;
                font-size: 0.8rem;
            }
            .pagination {
                font-size: 0.875rem;
            }
            .h2 {
                font-size: 1.5rem;
            }
        }

        /* Overlay for mobile sidebar */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
        }
        .sidebar-overlay.show {
            display: block;
        }

        /* Custom Gradients and Page Headers */
        .bg-gradient-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }
        .bg-gradient-success {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        }
        .bg-gradient-info {
            background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
        }
        .bg-gradient-warning {
            background: linear-gradient(135deg, #ffc107 0%, #d39e00 100%);
        }
        .bg-gradient-danger {
            background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
        }

        .page-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border-radius: 0.75rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 123, 255, 0.15);
        }

        .page-header .icon-wrapper {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .stats-card {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.75rem 1.5rem rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'dashboard' %}">
                <i class="bi bi-hospital"></i>
                <span class="d-none d-sm-inline">نظام العيادة الطبية</span>
                <span class="d-inline d-sm-none">العيادة</span>
            </a>

            {% if user.is_authenticated %}
                <button class="btn btn-outline-light mobile-menu-btn d-md-none" type="button" onclick="toggleSidebar()">
                    <i class="bi bi-list"></i>
                </button>
            {% endif %}

            <div class="navbar-nav ms-auto">
                {% if user.is_authenticated %}
                    <span class="navbar-text me-3 d-none d-lg-inline">
                        مرحباً، {{ user.first_name|default:user.username }}
                    </span>
                    <a class="nav-link" href="{% url 'logout' %}">
                        <i class="bi bi-box-arrow-right"></i>
                        <span class="d-none d-sm-inline">تسجيل الخروج</span>
                    </a>
                {% else %}
                    <a class="nav-link" href="{% url 'login' %}">
                        <i class="bi bi-box-arrow-in-right"></i>
                        <span class="d-none d-sm-inline">تسجيل الدخول</span>
                    </a>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" onclick="toggleSidebar()"></div>

    <!-- Sidebar -->
    {% if user.is_authenticated %}
    <nav class="sidebar" id="sidebar">
        <div class="position-sticky pt-3">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'dashboard' %}" onclick="closeSidebarOnMobile()">
                        <i class="bi bi-speedometer2"></i> لوحة التحكم
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'patient_list' %}" onclick="closeSidebarOnMobile()">
                        <i class="bi bi-people"></i> المرضى
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'appointment_list' %}" onclick="closeSidebarOnMobile()">
                        <i class="bi bi-calendar-check"></i> المواعيد
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'diagnosis_list' %}" onclick="closeSidebarOnMobile()">
                        <i class="bi bi-clipboard-pulse"></i> التشخيصات
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'prescription_list' %}" onclick="closeSidebarOnMobile()">
                        <i class="bi bi-prescription2"></i> الوصفات الطبية
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'record_list' %}" onclick="closeSidebarOnMobile()">
                        <i class="bi bi-clipboard-data"></i> الفحوصات
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link" href="{% url 'payment_list' %}" onclick="closeSidebarOnMobile()">
                        <i class="bi bi-credit-card"></i> المدفوعات
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link" href="{% url 'rays_list' %}" onclick="closeSidebarOnMobile()">
                        <i class="bi bi-camera "></i>الاشعة
                    </a>
                </li>
            </ul>
        </div>
    </nav>
    {% endif %}

    <!-- Main content -->
    <main class="main-content">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert" style="margin-top: 40px">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}

        {% block content %}
        {% endblock %}
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Sidebar toggle functionality
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.querySelector('.sidebar-overlay');

            if (sidebar && overlay) {
                sidebar.classList.toggle('show');
                overlay.classList.toggle('show');
            }
        }

        function closeSidebarOnMobile() {
            if (window.innerWidth < 768) {
                const sidebar = document.getElementById('sidebar');
                const overlay = document.querySelector('.sidebar-overlay');

                if (sidebar && overlay) {
                    sidebar.classList.remove('show');
                    overlay.classList.remove('show');
                }
            }
        }

        // Close sidebar when window is resized to desktop
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 768) {
                const sidebar = document.getElementById('sidebar');
                const overlay = document.querySelector('.sidebar-overlay');

                if (sidebar && overlay) {
                    sidebar.classList.remove('show');
                    overlay.classList.remove('show');
                }
            }
        });

        // Handle active navigation link
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar .nav-link');

            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                } else {
                    link.classList.remove('active');
                }
            });
        });
    </script>

    {% block extra_js %}
    {% endblock %}
</body>
</html>
