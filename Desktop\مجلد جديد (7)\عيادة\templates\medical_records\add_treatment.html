{% extends 'control_panels/base_dashboard.html' %}
{% load static %}

{% block title %}إضافة علاج{% endblock %}

{% block extra_css %}
<style>
    .add-treatment-container {
        background: linear-gradient(135deg, #fd7e14 0%, #e55353 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .treatment-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-bottom: 30px;
        overflow: hidden;
        max-width: 1000px;
        margin: 0 auto;
    }
    
    .treatment-header {
        background: linear-gradient(135deg, #fd7e14 0%, #e55353 100%);
        color: white;
        padding: 30px;
        text-align: center;
    }
    
    .treatment-title {
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 10px;
    }
    
    .form-section {
        background: white;
        border-radius: 10px;
        padding: 30px;
        margin: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    
    .section-header {
        margin-bottom: 25px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e9ecef;
    }
    
    .section-title {
        color: #495057;
        font-size: 1.3rem;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }
    
    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 12px 15px;
        transition: all 0.3s ease;
        font-size: 1rem;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #fd7e14;
        box-shadow: 0 0 0 0.2rem rgba(253, 126, 20, 0.25);
    }
    
    .required {
        color: #dc3545;
    }
    
    .treatment-type-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 15px;
    }
    
    .treatment-type-card {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
    }
    
    .treatment-type-card:hover {
        border-color: #fd7e14;
        background: #f8f9fa;
    }
    
    .treatment-type-card.selected {
        border-color: #fd7e14;
        background: #fff3e0;
    }
    
    .treatment-icon {
        font-size: 2rem;
        color: #fd7e14;
        margin-bottom: 10px;
    }
    
    .treatment-name {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }
    
    .treatment-desc {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .medication-item {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .medication-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 10px;
    }
    
    .btn-add-medication {
        background: #28a745;
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 0.9rem;
    }
    
    .btn-remove-medication {
        background: #dc3545;
        color: white;
        border: none;
        padding: 5px 10px;
        border-radius: 3px;
        cursor: pointer;
        font-size: 0.8rem;
    }
    
    .form-actions {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e9ecef;
        text-align: center;
    }
    
    .btn-primary-action {
        background: linear-gradient(135deg, #fd7e14 0%, #e55353 100%);
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 25px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-size: 1.1rem;
    }
    
    .btn-primary-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(253, 126, 20, 0.3);
        color: white;
    }
    
    .btn-secondary {
        background: #6c757d;
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 25px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-size: 1.1rem;
        margin-left: 15px;
    }
    
    .btn-secondary:hover {
        background: #5a6268;
        color: white;
        transform: translateY(-2px);
    }
    
    @media (max-width: 768px) {
        .treatment-type-grid {
            grid-template-columns: 1fr;
        }
        
        .form-actions .btn-primary-action,
        .form-actions .btn-secondary {
            display: block;
            width: 100%;
            margin: 10px 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="add-treatment-container">
    <div class="container-fluid">
        <!-- رأس الصفحة -->
        <div class="treatment-card">
            <div class="treatment-header">
                <h1 class="treatment-title">
                    <i class="fas fa-pills me-3"></i>
                    إضافة علاج جديد
                </h1>
                <p>{% if record %}للسجل الطبي: {{ record.record_id }}{% else %}علاج مستقل{% endif %}</p>
            </div>
        </div>
        
        <!-- نموذج إضافة العلاج -->
        <div class="treatment-card">
            <div class="form-section">
                <form method="post" id="treatmentForm">
                    {% csrf_token %}
                    
                    <!-- نوع العلاج -->
                    <div class="section-header">
                        <h3 class="section-title">
                            <i class="fas fa-prescription-bottle-alt"></i>
                            نوع العلاج
                        </h3>
                    </div>
                    
                    <div class="treatment-type-grid">
                        <div class="treatment-type-card" onclick="selectTreatmentType('MEDICATION', this)">
                            <div class="treatment-icon">
                                <i class="fas fa-pills"></i>
                            </div>
                            <div class="treatment-name">دوائي</div>
                            <div class="treatment-desc">أدوية ووصفات</div>
                        </div>
                        
                        <div class="treatment-type-card" onclick="selectTreatmentType('SURGERY', this)">
                            <div class="treatment-icon">
                                <i class="fas fa-cut"></i>
                            </div>
                            <div class="treatment-name">جراحي</div>
                            <div class="treatment-desc">عمليات جراحية</div>
                        </div>
                        
                        <div class="treatment-type-card" onclick="selectTreatmentType('THERAPY', this)">
                            <div class="treatment-icon">
                                <i class="fas fa-dumbbell"></i>
                            </div>
                            <div class="treatment-name">علاج طبيعي</div>
                            <div class="treatment-desc">تأهيل وعلاج طبيعي</div>
                        </div>
                        
                        <div class="treatment-type-card" onclick="selectTreatmentType('PROCEDURE', this)">
                            <div class="treatment-icon">
                                <i class="fas fa-syringe"></i>
                            </div>
                            <div class="treatment-name">إجراء طبي</div>
                            <div class="treatment-desc">إجراءات وتدخلات</div>
                        </div>
                        
                        <div class="treatment-type-card" onclick="selectTreatmentType('LIFESTYLE', this)">
                            <div class="treatment-icon">
                                <i class="fas fa-heart"></i>
                            </div>
                            <div class="treatment-name">نمط حياة</div>
                            <div class="treatment-desc">تغييرات نمط الحياة</div>
                        </div>
                        
                        <div class="treatment-type-card" onclick="selectTreatmentType('FOLLOW_UP', this)">
                            <div class="treatment-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div class="treatment-name">متابعة</div>
                            <div class="treatment-desc">مواعيد متابعة</div>
                        </div>
                    </div>
                    
                    <input type="hidden" id="selected_treatment_type" name="treatment_type" required>
                    
                    <!-- تفاصيل العلاج -->
                    <div class="section-header mt-4">
                        <h3 class="section-title">
                            <i class="fas fa-clipboard-list"></i>
                            تفاصيل العلاج
                        </h3>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="treatment_name" class="form-label">اسم العلاج <span class="required">*</span></label>
                            <input type="text" class="form-control" id="treatment_name" name="treatment_name" required
                                   placeholder="مثال: أموكسيسيلين 500 مجم">
                        </div>
                        <div class="col-md-6">
                            <label for="priority" class="form-label">الأولوية <span class="required">*</span></label>
                            <select class="form-select" id="priority" name="priority" required>
                                <option value="NORMAL">عادي</option>
                                <option value="HIGH">عالي</option>
                                <option value="URGENT">عاجل</option>
                                <option value="IMMEDIATE">فوري</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="dosage" class="form-label">الجرعة</label>
                            <input type="text" class="form-control" id="dosage" name="dosage"
                                   placeholder="مثال: 500 مجم">
                        </div>
                        <div class="col-md-4">
                            <label for="frequency" class="form-label">التكرار</label>
                            <input type="text" class="form-control" id="frequency" name="frequency"
                                   placeholder="مثال: 3 مرات يومياً">
                        </div>
                        <div class="col-md-4">
                            <label for="duration" class="form-label">المدة</label>
                            <input type="text" class="form-control" id="duration" name="duration"
                                   placeholder="مثال: 7 أيام">
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="start_date" class="form-label">تاريخ البداية</label>
                            <input type="date" class="form-control" id="start_date" name="start_date">
                        </div>
                        <div class="col-md-6">
                            <label for="end_date" class="form-label">تاريخ النهاية</label>
                            <input type="date" class="form-control" id="end_date" name="end_date">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="instructions" class="form-label">تعليمات الاستخدام <span class="required">*</span></label>
                        <textarea class="form-control" id="instructions" name="instructions"
                                  rows="3" required placeholder="تعليمات مفصلة للمريض"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="side_effects" class="form-label">الآثار الجانبية المحتملة</label>
                        <textarea class="form-control" id="side_effects" name="side_effects"
                                  rows="2" placeholder="الآثار الجانبية التي يجب تنبيه المريض إليها"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="contraindications" class="form-label">موانع الاستعمال</label>
                        <textarea class="form-control" id="contraindications" name="contraindications"
                                  rows="2" placeholder="موانع الاستعمال والتحذيرات"></textarea>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="prescribed_by" class="form-label">وصف بواسطة</label>
                            <input type="text" class="form-control" id="prescribed_by" name="prescribed_by"
                                   value="{{ request.user.get_full_name }}" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="ACTIVE">نشط</option>
                                <option value="COMPLETED">مكتمل</option>
                                <option value="DISCONTINUED">متوقف</option>
                                <option value="ON_HOLD">معلق</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-control" id="notes" name="notes"
                                  rows="2" placeholder="أي ملاحظات إضافية"></textarea>
                    </div>
                    
                    <!-- إجراءات النموذج -->
                    <div class="form-actions">
                        <button type="submit" class="btn-primary-action">
                            <i class="fas fa-save"></i>
                            حفظ العلاج
                        </button>
                        <a href="{% if record %}{% url 'medical_records:record_detail' record.id %}{% else %}{% url 'medical_records:record_list' %}{% endif %}" class="btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// اختيار نوع العلاج
function selectTreatmentType(type, element) {
    // إزالة التحديد من جميع البطاقات
    document.querySelectorAll('.treatment-type-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // تحديد البطاقة المختارة
    element.classList.add('selected');
    document.getElementById('selected_treatment_type').value = type;
}

// التحقق من صحة النموذج
document.getElementById('treatmentForm').addEventListener('submit', function(event) {
    const treatmentType = document.getElementById('selected_treatment_type').value;

    if (!treatmentType) {
        event.preventDefault();
        alert('يرجى اختيار نوع العلاج');
        return false;
    }
});
</script>
{% endblock %}
