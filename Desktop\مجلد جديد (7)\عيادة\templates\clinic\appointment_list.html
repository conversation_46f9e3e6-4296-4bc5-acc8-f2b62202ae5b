{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة المواعيد - نظام العيادة الطبية{% endblock %}

{% block extra_css %}
<style>
    .appointment-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .stat-card {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border: none;
        border-radius: 15px;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    }

    .stat-card.scheduled {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .stat-card.completed {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }

    .stat-card.cancelled {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }

    .stat-card.today {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    }

    .search-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
    }

    .appointment-card {
        border: none;
        border-radius: 15px;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    }

    .appointment-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }

    .status-badge {
        padding: 8px 16px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.85rem;
    }

    .status-scheduled {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
    }

    .status-completed {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
    }

    .status-cancelled {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        color: white;
    }

    .status-no-show {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        color: white;
    }

    .quick-filter-btn {
        border-radius: 25px;
        padding: 8px 20px;
        margin: 5px;
        border: 2px solid transparent;
        transition: all 0.3s ease;
    }

    .quick-filter-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .appointment-time {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 10px;
        border-radius: 10px;
        text-align: center;
        font-weight: bold;
    }

    .patient-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 1.2rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="appointment-header  bg-gradient-primary text-white rounded-3 p-4 mb-4 shadow" style="margin-top: 50px;">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    <div class="  rounded-circle p-3 me-3">
                        <i class="bi bi-calendar-check fs-2 "></i>
                    </div>
                    <div>
                        <h1 class="h2 mb-1 ">إدارة المواعيد</h1>
                        <p class="mb-0 text-white-50">جدولة وإدارة مواعيد المرضى بكفاءة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{% url 'appointment_create' %}" class="btn btn-light btn-lg">
                    <i class="bi bi-calendar-plus"></i>
                    <span class="d-none d-sm-inline">حجز موعد جديد</span>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card  h-100">
            <div class="card-body text-center">
                <i class="bi bi-calendar-event fs-1 mb-2"></i>
                <h3 class="mb-1">{{ total_appointments }}</h3>
                <p class="mb-0">إجمالي المواعيد</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card scheduled  h-100">
            <div class="card-body text-center">
                <i class="bi bi-clock fs-1 mb-2"></i>
                <h3 class="mb-1">{{ scheduled_appointments }}</h3>
                <p class="mb-0">مواعيد مجدولة</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card completed  h-100">
            <div class="card-body text-center">
                <i class="bi bi-check-circle fs-1 mb-2"></i>
                <h3 class="mb-1">{{ completed_appointments }}</h3>
                <p class="mb-0">مواعيد مكتملة</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card today  h-100">
            <div class="card-body text-center">
                <i class="bi bi-calendar-day fs-1 mb-2"></i>
                <h3 class="mb-1">{{ today_appointments }}</h3>
                <p class="mb-0">مواعيد اليوم</p>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="card search-card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0 text-white">
            <i class="bi bi-search"></i> البحث والتصفية المتقدمة
        </h5>
    </div>
    <div class="card-body">
        <form method="get" id="searchForm" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label text-white">البحث</label>
                <input type="text" class="form-control" id="search" name="search"
                       value="{{ search_query }}" placeholder="ابحث عن مريض أو سبب الزيارة...">
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label text-white">حالة الموعد</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="date_filter" class="form-label text-white">التاريخ</label>
                <select class="form-select" id="date_filter" name="date_filter">
                    <option value="">جميع التواريخ</option>
                    <option value="today" {% if date_filter == 'today' %}selected{% endif %}>اليوم</option>
                    <option value="tomorrow" {% if date_filter == 'tomorrow' %}selected{% endif %}>غداً</option>
                    <option value="this_week" {% if date_filter == 'this_week' %}selected{% endif %}>هذا الأسبوع</option>
                    <option value="this_month" {% if date_filter == 'this_month' %}selected{% endif %}>هذا الشهر</option>
                    <option value="overdue" {% if date_filter == 'overdue' %}selected{% endif %}>متأخرة</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="doctor" class="form-label text-white">الطبيب</label>
                <select class="form-select" id="doctor" name="doctor">
                    <option value="">جميع الأطباء</option>
                    {% for doctor in doctors %}
                        <option value="{{ doctor.id }}" {% if doctor_filter == doctor.id|stringformat:"s" %}selected{% endif %}>
                            {{ doctor.first_name }} {{ doctor.last_name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="sort" class="form-label text-white">ترتيب</label>
                <select class="form-select" id="sort" name="sort">
                    <option value="-appointment_date" {% if sort_by == '-appointment_date' %}selected{% endif %}>الأحدث أولاً</option>
                    <option value="appointment_date" {% if sort_by == 'appointment_date' %}selected{% endif %}>الأقدم أولاً</option>
                    <option value="patient__first_name" {% if sort_by == 'patient__first_name' %}selected{% endif %}>اسم المريض أ-ي</option>
                    <option value="-patient__first_name" {% if sort_by == '-patient__first_name' %}selected{% endif %}>اسم المريض ي-أ</option>
                </select>
            </div>
            <div class="col-md-1">
                <label class="form-label text-white">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-light">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
        </form>

        <!-- Quick Filter Buttons -->
        <div class="mt-3 text-center">
            <a href="?date_filter=today" class="btn btn-outline-light quick-filter-btn">
                <i class="bi bi-calendar-day"></i> اليوم
            </a>
            <a href="?date_filter=tomorrow" class="btn btn-outline-light quick-filter-btn">
                <i class="bi bi-calendar-plus"></i> غداً
            </a>
            <a href="?status=scheduled" class="btn btn-outline-light quick-filter-btn">
                <i class="bi bi-clock"></i> مجدولة
            </a>
            <a href="?status=completed" class="btn btn-outline-light quick-filter-btn">
                <i class="bi bi-check-circle"></i> مكتملة
            </a>
            <a href="{% url 'appointment_list' %}" class="btn btn-outline-light quick-filter-btn">
                <i class="bi bi-arrow-clockwise"></i> إعادة تعيين
            </a>
        </div>
    </div>
</div>

<!-- Appointments List -->
{% if page_obj %}
    <!-- View Mode Toggle -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <span class="text-muted">عرض {{ page_obj.start_index }}-{{ page_obj.end_index }} من {{ page_obj.paginator.count }} موعد</span>
        </div>
        <div class="btn-group" role="group">
            <input type="radio" class="btn-check" name="viewMode" id="cardView" autocomplete="off" checked>
            <label class="btn btn-outline-primary" for="cardView">
                <i class="bi bi-grid-3x3-gap"></i> بطاقات
            </label>
            <input type="radio" class="btn-check" name="viewMode" id="tableView" autocomplete="off">
            <label class="btn btn-outline-primary" for="tableView">
                <i class="bi bi-table"></i> جدول
            </label>
        </div>
    </div>

    <!-- Card View -->
    <div id="cardViewContainer" class="row">
        {% for appointment in page_obj %}
            <div class="col-lg-6 col-xl-4 mb-4">
                <div class="card appointment-card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-start mb-3">
                            <div class="patient-avatar me-3">
                                {{ appointment.patient.first_name|first }}{{ appointment.patient.last_name|first }}
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="card-title mb-1">
                                    <a href="{% url 'patient_detail' appointment.patient.pk %}" class="text-decoration-none">
                                        {{ appointment.patient.full_name }}
                                    </a>
                                </h6>
                                <p class="text-muted mb-0">
                                    <i class="bi bi-telephone"></i> {{ appointment.patient.phone }}
                                </p>
                            </div>
                            <div class="text-end">
                                {% if appointment.status == 'scheduled' %}
                                    <span class="status-badge status-scheduled">مجدول</span>
                                {% elif appointment.status == 'completed' %}
                                    <span class="status-badge status-completed">مكتمل</span>
                                {% elif appointment.status == 'cancelled' %}
                                    <span class="status-badge status-cancelled">ملغي</span>
                                {% elif appointment.status == 'no_show' %}
                                    <span class="status-badge status-no-show">لم يحضر</span>
                                {% endif %}
                            </div>
                        </div>

                        <div class="appointment-time mb-3">
                            <i class="bi bi-calendar-event"></i> {{ appointment.appointment_date|date:"d/m/Y" }}
                            <br>
                            <i class="bi bi-clock"></i> {{ appointment.appointment_date|date:"H:i" }}
                            <small class="d-block mt-1">{{ appointment.duration }} دقيقة</small>
                        </div>

                        <p class="card-text mb-3">
                            <strong>سبب الزيارة:</strong><br>
                            {{ appointment.reason|truncatechars:80 }}
                        </p>

                        {% if appointment.notes %}
                            <p class="card-text text-muted mb-3">
                                <small><strong>ملاحظات:</strong> {{ appointment.notes|truncatechars:60 }}</small>
                            </p>
                        {% endif %}

                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="bi bi-person-badge"></i> {{ appointment.doctor.first_name }} {{ appointment.doctor.last_name }}
                            </small>
                            <div class="btn-group" role="group">
                                <a href="{% url 'appointment_detail' appointment.pk %}" class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{% url 'appointment_update' appointment.pk %}" class="btn btn-sm btn-outline-warning" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <a href="{% url 'appointment_delete' appointment.pk %}" class="btn btn-sm btn-outline-danger" title="حذف">
                                    <i class="bi bi-trash"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>

    <!-- Table View -->
    <div id="tableViewContainer" class="d-none">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>المريض</th>
                        <th>التاريخ والوقت</th>
                        <th>الطبيب</th>
                        <th>سبب الزيارة</th>
                        <th>المدة</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for appointment in page_obj %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="patient-avatar me-2" style="width: 35px; height: 35px; font-size: 0.9rem;">
                                        {{ appointment.patient.first_name|first }}{{ appointment.patient.last_name|first }}
                                    </div>
                                    <div>
                                        <a href="{% url 'patient_detail' appointment.patient.pk %}" class="text-decoration-none fw-bold">
                                            {{ appointment.patient.full_name }}
                                        </a>
                                        <br>
                                        <small class="text-muted">{{ appointment.patient.phone }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <strong>{{ appointment.appointment_date|date:"d/m/Y" }}</strong><br>
                                <small class="text-muted">{{ appointment.appointment_date|date:"H:i" }}</small>
                            </td>
                            <td>{{ appointment.doctor.first_name }} {{ appointment.doctor.last_name }}</td>
                            <td>{{ appointment.reason|truncatechars:50 }}</td>
                            <td>{{ appointment.duration }} دقيقة</td>
                            <td>
                                {% if appointment.status == 'scheduled' %}
                                    <span class="status-badge status-scheduled">مجدول</span>
                                {% elif appointment.status == 'completed' %}
                                    <span class="status-badge status-completed">مكتمل</span>
                                {% elif appointment.status == 'cancelled' %}
                                    <span class="status-badge status-cancelled">ملغي</span>
                                {% elif appointment.status == 'no_show' %}
                                    <span class="status-badge status-no-show">لم يحضر</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'appointment_detail' appointment.pk %}" class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{% url 'appointment_update' appointment.pk %}" class="btn btn-sm btn-outline-warning" title="تعديل">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <a href="{% url 'appointment_delete' appointment.pk %}" class="btn btn-sm btn-outline-danger" title="حذف">
                                        <i class="bi bi-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
        <nav aria-label="Appointment pagination" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                            <i class="bi bi-chevron-double-left"></i> الأولى
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                            <i class="bi bi-chevron-left"></i> السابقة
                        </a>
                    </li>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                {{ num }}
                            </a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                            التالية <i class="bi bi-chevron-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                            الأخيرة <i class="bi bi-chevron-double-right"></i>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    {% endif %}

{% else %}
    <div class="text-center py-5">
        <i class="bi bi-calendar-x fs-1 text-muted mb-3"></i>
        <h4 class="text-muted">لا توجد مواعيد</h4>
        <p class="text-muted">لم يتم العثور على مواعيد تطابق معايير البحث</p>
        <a href="{% url 'appointment_create' %}" class="btn btn-primary">
            <i class="bi bi-calendar-plus"></i> حجز أول موعد
        </a>
    </div>
{% endif %}

{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Toggle between card and table view
    $('input[name="viewMode"]').change(function() {
        if ($(this).attr('id') === 'cardView') {
            $('#cardViewContainer').removeClass('d-none');
            $('#tableViewContainer').addClass('d-none');
            localStorage.setItem('appointmentViewMode', 'card');
        } else {
            $('#cardViewContainer').addClass('d-none');
            $('#tableViewContainer').removeClass('d-none');
            localStorage.setItem('appointmentViewMode', 'table');
        }
    });

    // Restore view mode from localStorage
    const savedViewMode = localStorage.getItem('appointmentViewMode');
    if (savedViewMode === 'table') {
        $('#tableView').prop('checked', true);
        $('#cardViewContainer').addClass('d-none');
        $('#tableViewContainer').removeClass('d-none');
    }

    // Auto-submit search form on filter change
    $('#searchForm select').change(function() {
        $('#searchForm').submit();
    });

    // Enhanced search with debounce
    let searchTimeout;
    $('input[name="search"]').on('input', function() {
        clearTimeout(searchTimeout);
        const searchValue = $(this).val();

        searchTimeout = setTimeout(function() {
            if (searchValue.length >= 3 || searchValue.length === 0) {
                $('#searchForm').submit();
            }
        }, 500);
    });

    // Animate statistics cards on page load
    $('.stat-card').each(function(index) {
        $(this).css('opacity', '0').css('transform', 'translateY(20px)');
        $(this).delay(index * 100).animate({
            opacity: 1
        }, 500).css('transform', 'translateY(0)');
    });

    // Animate appointment cards on page load
    $('.appointment-card').each(function(index) {
        $(this).css('opacity', '0').css('transform', 'translateY(20px)');
        $(this).delay(index * 50).animate({
            opacity: 1
        }, 300).css('transform', 'translateY(0)');
    });

    // Add loading state to buttons
    $('.btn').click(function() {
        const $btn = $(this);
        if (!$btn.hasClass('dropdown-toggle') && !$btn.hasClass('btn-check')) {
            const originalText = $btn.html();
            $btn.html('<i class="bi bi-hourglass-split"></i> جاري التحميل...');
            $btn.prop('disabled', true);

            setTimeout(function() {
                $btn.html(originalText);
                $btn.prop('disabled', false);
            }, 2000);
        }
    });

    // Add tooltips to action buttons
    $('[title]').tooltip();

    // Smooth scroll to top
    $(window).scroll(function() {
        if ($(this).scrollTop() > 100) {
            if ($('#scrollToTop').length === 0) {
                $('body').append('<button id="scrollToTop" class="btn btn-primary position-fixed" style="bottom: 20px; right: 20px; z-index: 1000; border-radius: 50%; width: 50px; height: 50px;"><i class="bi bi-arrow-up"></i></button>');
            }
        } else {
            $('#scrollToTop').remove();
        }
    });

    $(document).on('click', '#scrollToTop', function() {
        $('html, body').animate({scrollTop: 0}, 500);
    });

    // Real-time clock for current time display
    function updateClock() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('ar-EG');
        const dateString = now.toLocaleDateString('ar-EG');

        if ($('#currentTime').length === 0) {
            $('.appointment-header .container-fluid').append(
                '<div id="currentTime" class="position-absolute" style="top: 10px; left: 20px; font-size: 0.9rem; opacity: 0.8;">' +
                '<i class="bi bi-clock"></i> ' + timeString + '<br>' +
                '<i class="bi bi-calendar"></i> ' + dateString +
                '</div>'
            );
        } else {
            $('#currentTime').html(
                '<i class="bi bi-clock"></i> ' + timeString + '<br>' +
                '<i class="bi bi-calendar"></i> ' + dateString
            );
        }
    }

    updateClock();
    setInterval(updateClock, 1000);
});
</script>
{% endblock %}
