from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from django.http import JsonResponse, HttpResponse
from .models import MedicalRecord
from patients.models import Patient
from departments.models import Department
from staff.models import Staff


@login_required
def record_list(request):
    """قائمة السجلات الطبية"""
    records = MedicalRecord.objects.all().order_by('-created_at')

    # البحث
    search_query = request.GET.get('search', '')
    if search_query:
        records = records.filter(
            Q(record_id__icontains=search_query) |
            Q(patient__first_name__icontains=search_query) |
            Q(patient__last_name__icontains=search_query) |
            Q(doctor__first_name__icontains=search_query) |
            Q(doctor__last_name__icontains=search_query)
        )

    # تصفية حسب النوع
    record_type = request.GET.get('record_type', '')
    if record_type:
        records = records.filter(record_type=record_type)

    # تصفية حسب الأولوية
    priority = request.GET.get('priority', '')
    if priority:
        records = records.filter(priority=priority)

    # تصفية حسب التاريخ
    date_from = request.GET.get('date_from', '')
    if date_from:
        records = records.filter(created_at__date__gte=date_from)

    # التصفح
    paginator = Paginator(records, 20)
    page_number = request.GET.get('page')
    records = paginator.get_page(page_number)

    # الإحصائيات
    total_records = MedicalRecord.objects.count()
    completed_records = MedicalRecord.objects.filter(is_completed=True).count()
    pending_records = MedicalRecord.objects.filter(is_completed=False).count()
    urgent_records = MedicalRecord.objects.filter(priority__in=['URGENT', 'CRITICAL']).count()

    context = {
        'title': 'السجلات الطبية',
        'records': records,
        'search_query': search_query,
        'record_types': MedicalRecord.RECORD_TYPES,
        'priorities': MedicalRecord.PRIORITY_LEVELS,
        'type_filter': record_type,
        'priority_filter': priority,
        'date_from': date_from,
        'total_records': total_records,
        'completed_records': completed_records,
        'pending_records': pending_records,
        'urgent_records': urgent_records,
    }

    return render(request, 'medical_records/record_list.html', context)


@login_required
def record_detail(request, record_id):
    """تفاصيل السجل الطبي"""
    record = get_object_or_404(MedicalRecord, pk=record_id)

    # التحقق من الصلاحيات
    if record.is_confidential and not request.user.is_staff:
        messages.error(request, 'ليس لديك صلاحية لعرض هذا السجل السري')
        return redirect('medical_records:record_list')

    context = {
        'title': f'السجل الطبي - {record.record_id}',
        'record': record,
        'latest_vitals': None,  # سيتم إضافتها لاحقاً
        'recent_notes': [],     # سيتم إضافتها لاحقاً
    }

    return render(request, 'medical_records/record_detail.html', context)


@login_required
def add_record(request):
    """إضافة سجل طبي جديد"""
    if request.method == 'POST':
        try:
            # الحصول على البيانات من النموذج
            patient_id = request.POST.get('patient_id')
            patient = get_object_or_404(Patient, pk=patient_id)

            # الحصول على الطبيب الحالي
            try:
                doctor = Staff.objects.get(user=request.user)
            except Staff.DoesNotExist:
                messages.error(request, 'يجب أن تكون مسجلاً كطبيب لإنشاء سجل طبي')
                return redirect('medical_records:record_list')

            # إنشاء السجل الطبي
            record = MedicalRecord.objects.create(
                patient=patient,
                doctor=doctor,
                department_id=request.POST.get('department_id'),
                record_type=request.POST.get('record_type'),
                priority=request.POST.get('priority', 'NORMAL'),
                chief_complaint=request.POST.get('chief_complaint'),
                history_of_present_illness=request.POST.get('history_of_present_illness'),
                past_medical_history=request.POST.get('past_medical_history', ''),
                family_history=request.POST.get('family_history', ''),
                social_history=request.POST.get('social_history', ''),
                physical_examination=request.POST.get('physical_examination'),
                assessment=request.POST.get('assessment'),
                plan=request.POST.get('plan'),
                is_confidential=request.POST.get('is_confidential') == 'on',
                is_completed=request.POST.get('is_completed') == 'on',
            )

            messages.success(request, f'تم إنشاء السجل الطبي {record.record_id} بنجاح')
            return redirect('medical_records:record_detail', record_id=record.pk)

        except Exception as e:
            messages.error(request, f'حدث خطأ في إنشاء السجل: {str(e)}')

    # عرض النموذج
    departments = Department.objects.all()

    context = {
        'title': 'إضافة سجل طبي جديد',
        'departments': departments,
    }

    return render(request, 'medical_records/add_record.html', context)


@login_required
def edit_record(request, record_id):
    """تعديل سجل طبي"""
    record = get_object_or_404(MedicalRecord, pk=record_id)

    # التحقق من الصلاحيات
    if record.is_completed and not request.user.is_superuser:
        messages.error(request, 'لا يمكن تعديل سجل مكتمل')
        return redirect('medical_records:record_detail', record_id=record.pk)

    if request.method == 'POST':
        try:
            # تحديث بيانات السجل
            record.record_type = request.POST.get('record_type')
            record.priority = request.POST.get('priority')
            record.chief_complaint = request.POST.get('chief_complaint')
            record.history_of_present_illness = request.POST.get('history_of_present_illness')
            record.past_medical_history = request.POST.get('past_medical_history', '')
            record.family_history = request.POST.get('family_history', '')
            record.social_history = request.POST.get('social_history', '')
            record.physical_examination = request.POST.get('physical_examination')
            record.vital_signs_temperature = request.POST.get('vital_signs_temperature') or None
            record.vital_signs_blood_pressure = request.POST.get('vital_signs_blood_pressure', '')
            record.vital_signs_heart_rate = request.POST.get('vital_signs_heart_rate') or None
            record.vital_signs_respiratory_rate = request.POST.get('vital_signs_respiratory_rate') or None
            record.assessment = request.POST.get('assessment', '')
            record.plan = request.POST.get('plan', '')
            record.notes = request.POST.get('notes', '')
            record.is_completed = request.POST.get('is_completed') == 'on'
            record.follow_up_date = request.POST.get('follow_up_date') or None

            record.save()
            messages.success(request, f'تم تحديث السجل الطبي {record.record_id} بنجاح')
            return redirect('medical_records:record_detail', record_id=record.pk)

        except Exception as e:
            messages.error(request, f'حدث خطأ في تحديث السجل: {str(e)}')

    context = {
        'title': f'تعديل السجل الطبي - {record.record_id}',
        'record': record,
    }

    return render(request, 'medical_records/edit_record.html', context)


@login_required
def add_diagnosis(request, record_id=None):
    """إضافة تشخيص"""
    record = None
    if record_id:
        record = get_object_or_404(MedicalRecord, pk=record_id)

    if request.method == 'POST':
        try:
            # معالجة البيانات المرسلة
            diagnosis_type = request.POST.get('diagnosis_type')
            diagnosis_name = request.POST.get('diagnosis_name')
            severity = request.POST.get('severity')
            status = request.POST.get('status')
            description = request.POST.get('description')

            # التحقق من البيانات المطلوبة
            if not diagnosis_type or not diagnosis_name or not description:
                messages.error(request, 'يرجى ملء جميع الحقول المطلوبة')
                return redirect(request.path)

            # هنا يمكن إضافة منطق حفظ التشخيص
            # سيتم تطوير هذا لاحقاً عند إنشاء نموذج Diagnosis

            messages.success(request, 'تم حفظ التشخيص بنجاح')
            return redirect('medical_records:record_list')

        except Exception as e:
            messages.error(request, f'حدث خطأ: {str(e)}')

    context = {
        'title': f'إضافة تشخيص{" - " + record.record_id if record else ""}',
        'record': record,
    }

    return render(request, 'medical_records/add_diagnosis.html', context)


@login_required
def add_treatment(request, record_id=None):
    """إضافة علاج"""
    record = None
    if record_id:
        record = get_object_or_404(MedicalRecord, pk=record_id)

    if request.method == 'POST':
        try:
            # معالجة البيانات المرسلة
            treatment_type = request.POST.get('treatment_type')
            treatment_name = request.POST.get('treatment_name')
            priority = request.POST.get('priority')
            instructions = request.POST.get('instructions')

            # التحقق من البيانات المطلوبة
            if not treatment_type or not treatment_name or not instructions:
                messages.error(request, 'يرجى ملء جميع الحقول المطلوبة')
                return redirect(request.path)

            # هنا يمكن إضافة منطق حفظ العلاج
            # سيتم تطوير هذا لاحقاً عند إنشاء نموذج Treatment

            messages.success(request, 'تم حفظ العلاج بنجاح')
            return redirect('medical_records:record_list')

        except Exception as e:
            messages.error(request, f'حدث خطأ: {str(e)}')

    context = {
        'title': f'إضافة علاج{" - " + record.record_id if record else ""}',
        'record': record,
    }

    return render(request, 'medical_records/add_treatment.html', context)


@login_required
def add_lab_test(request, record_id=None):
    """إضافة فحص مخبري"""
    record = None
    if record_id:
        record = get_object_or_404(MedicalRecord, pk=record_id)

    # الحصول على الأقسام
    from departments.models import Department
    departments = Department.objects.all()

    if request.method == 'POST':
        try:
            # معالجة البيانات المرسلة
            patient_id = request.POST.get('patient_id')
            test_type = request.POST.get('test_type')
            test_name = request.POST.get('test_name')
            priority = request.POST.get('priority')
            clinical_notes = request.POST.get('clinical_notes', '')
            special_instructions = request.POST.get('special_instructions', '')
            department_id = request.POST.get('department_id')

            # التحقق من البيانات المطلوبة
            if not patient_id or not test_type or not test_name:
                messages.error(request, 'يرجى ملء جميع الحقول المطلوبة')
                return redirect(request.path)

            # هنا يمكن إضافة منطق حفظ طلب الفحص المختبري
            # سيتم تطوير هذا لاحقاً عند إنشاء نموذج LabTest

            messages.success(request, 'تم إرسال طلب الفحص المختبري بنجاح')
            return redirect('medical_records:record_list')

        except Exception as e:
            messages.error(request, f'حدث خطأ: {str(e)}')

    context = {
        'title': f'طلب فحص مخبري{" - " + record.record_id if record else ""}',
        'record': record,
        'departments': departments,
    }

    return render(request, 'medical_records/add_lab_test.html', context)


@login_required
def add_radiology(request, record_id=None):
    """إضافة دراسة إشعاعية"""
    record = None
    if record_id:
        record = get_object_or_404(MedicalRecord, pk=record_id)

    # الحصول على الأقسام
    from departments.models import Department
    departments = Department.objects.all()

    if request.method == 'POST':
        try:
            # معالجة البيانات المرسلة
            study_type = request.POST.get('study_type')
            study_name = request.POST.get('study_name')
            body_part = request.POST.get('body_part')
            clinical_indication = request.POST.get('clinical_indication')

            # التحقق من البيانات المطلوبة
            if not study_type or not study_name or not body_part or not clinical_indication:
                messages.error(request, 'يرجى ملء جميع الحقول المطلوبة')
                return redirect(request.path)

            # هنا يمكن إضافة منطق حفظ طلب الدراسة الإشعاعية
            # سيتم تطوير هذا لاحقاً عند إنشاء نموذج RadiologyStudy

            messages.success(request, 'تم إرسال طلب الدراسة الإشعاعية بنجاح')
            return redirect('medical_records:record_list')

        except Exception as e:
            messages.error(request, f'حدث خطأ: {str(e)}')

    context = {
        'title': f'طلب دراسة إشعاعية{" - " + record.record_id if record else ""}',
        'record': record,
        'departments': departments,
    }

    return render(request, 'medical_records/add_radiology.html', context)


@login_required
def print_record(request, record_id):
    """طباعة السجل الطبي"""
    record = get_object_or_404(MedicalRecord, pk=record_id)

    context = {
        'record': record,
    }

    return render(request, 'medical_records/print_record.html', context)


@login_required
def export_records(request):
    """تصدير السجلات"""
    # سيتم تطوير هذه الوظيفة لاحقاً
    messages.info(request, 'سيتم إضافة وظيفة التصدير قريباً')
    return redirect('medical_records:record_list')


@login_required
def search_records(request):
    """البحث المتقدم"""
    context = {
        'title': 'البحث المتقدم في السجلات الطبية',
    }

    return render(request, 'medical_records/search.html', context)
