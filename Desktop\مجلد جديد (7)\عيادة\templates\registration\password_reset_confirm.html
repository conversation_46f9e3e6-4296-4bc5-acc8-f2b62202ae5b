<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين كلمة المرور - نظام العيادة الطبية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .reset-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card reset-card">
                    <div class="card-body p-5">
                        {% if validlink %}
                            <!-- Valid Link - Show Reset Form -->
                            <div class="text-center mb-4">
                                <div class="bg-success bg-opacity-10 rounded-circle p-3 d-inline-block mb-3">
                                    <i class="bi bi-shield-check fs-1 text-success"></i>
                                </div>
                                <h2 class="fw-bold text-dark">إعادة تعيين كلمة المرور</h2>
                                <p class="text-muted">أدخل كلمة المرور الجديدة لحساب {{ user.username }}</p>
                            </div>

                            <!-- Messages -->
                            {% if messages %}
                                {% for message in messages %}
                                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                        <i class="bi bi-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}x-circle{% else %}info-circle{% endif %}"></i>
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}

                            <!-- Reset Form -->
                            <form method="post" novalidate>
                                {% csrf_token %}
                                
                                <div class="mb-3">
                                    <label for="password1" class="form-label">
                                        <i class="bi bi-lock"></i> كلمة المرور الجديدة
                                    </label>
                                    <input type="password" class="form-control" id="password1" name="password1" required>
                                    <div class="form-text">
                                        <small>كلمة المرور يجب أن تكون 8 أحرف على الأقل</small>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label for="password2" class="form-label">
                                        <i class="bi bi-lock-fill"></i> تأكيد كلمة المرور
                                    </label>
                                    <input type="password" class="form-control" id="password2" name="password2" required>
                                </div>

                                <div class="d-grid mb-4">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="bi bi-check-circle"></i> تحديث كلمة المرور
                                    </button>
                                </div>
                            </form>

                            <!-- Security Tips -->
                            <div class="alert alert-info">
                                <h6><i class="bi bi-shield-exclamation"></i> نصائح الأمان:</h6>
                                <ul class="mb-0 small">
                                    <li>استخدم كلمة مرور قوية ومعقدة</li>
                                    <li>لا تشارك كلمة المرور مع أحد</li>
                                    <li>استخدم مزيج من الأحرف والأرقام والرموز</li>
                                    <li>تجنب استخدام معلومات شخصية</li>
                                </ul>
                            </div>

                        {% else %}
                            <!-- Invalid Link -->
                            <div class="text-center mb-4">
                                <div class="bg-danger bg-opacity-10 rounded-circle p-3 d-inline-block mb-3">
                                    <i class="bi bi-x-circle fs-1 text-danger"></i>
                                </div>
                                <h2 class="fw-bold text-dark">رابط غير صالح</h2>
                                <p class="text-muted">عذراً، هذا الرابط غير صالح أو منتهي الصلاحية</p>
                            </div>

                            <div class="alert alert-danger">
                                <h6><i class="bi bi-exclamation-triangle"></i> لماذا حدث هذا؟</h6>
                                <ul class="mb-0">
                                    <li>الرابط منتهي الصلاحية (صالح لمدة 24 ساعة فقط)</li>
                                    <li>تم استخدام الرابط من قبل</li>
                                    <li>الرابط تالف أو غير مكتمل</li>
                                </ul>
                            </div>

                            <div class="d-grid mb-3">
                                <a href="{% url 'password_reset_request' %}" class="btn btn-primary btn-lg">
                                    <i class="bi bi-arrow-clockwise"></i> طلب رابط جديد
                                </a>
                            </div>
                        {% endif %}

                        <!-- Back to Login -->
                        <div class="text-center">
                            <p class="text-muted mb-0">
                                <a href="{% url 'login' %}" class="text-decoration-none fw-bold">
                                    <i class="bi bi-arrow-right"></i> العودة لتسجيل الدخول
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    {% if validlink %}
    <script>
        // Password validation
        document.addEventListener('DOMContentLoaded', function() {
            const password1 = document.getElementById('password1');
            const password2 = document.getElementById('password2');
            
            if (password1) {
                password1.addEventListener('input', function() {
                    const value = this.value;
                    let strength = 0;
                    
                    if (value.length >= 8) strength++;
                    if (/[a-z]/.test(value)) strength++;
                    if (/[A-Z]/.test(value)) strength++;
                    if (/[0-9]/.test(value)) strength++;
                    if (/[^A-Za-z0-9]/.test(value)) strength++;
                    
                    // Remove existing strength indicator
                    const existing = this.parentNode.querySelector('.password-strength');
                    if (existing) existing.remove();
                    
                    if (value.length > 0) {
                        const indicator = document.createElement('div');
                        indicator.className = 'password-strength mt-1';
                        
                        let color = 'danger';
                        let text = 'ضعيفة';
                        
                        if (strength >= 3) {
                            color = 'success';
                            text = 'قوية';
                        } else if (strength >= 2) {
                            color = 'warning';
                            text = 'متوسطة';
                        }
                        
                        indicator.innerHTML = `<small class="text-${color}">قوة كلمة المرور: ${text}</small>`;
                        this.parentNode.appendChild(indicator);
                    }
                });
            }
            
            // Password confirmation check
            if (password2) {
                password2.addEventListener('input', function() {
                    const existing = this.parentNode.querySelector('.password-match');
                    if (existing) existing.remove();
                    
                    if (this.value.length > 0) {
                        const indicator = document.createElement('div');
                        indicator.className = 'password-match mt-1';
                        
                        if (this.value === password1.value) {
                            indicator.innerHTML = '<small class="text-success"><i class="bi bi-check-circle"></i> كلمتا المرور متطابقتان</small>';
                        } else {
                            indicator.innerHTML = '<small class="text-danger"><i class="bi bi-x-circle"></i> كلمتا المرور غير متطابقتان</small>';
                        }
                        
                        this.parentNode.appendChild(indicator);
                    }
                });
            }
        });
    </script>
    {% endif %}
</body>
</html>
