@echo off
chcp 65001 >nul
title 🏥 نظام العيادة الطبية - تشغيل سريع
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🏥 نظام العيادة الطبية                    ║
echo ║                      تشغيل سريع                             ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 جاري تشغيل النظام...
echo.

REM التحقق من Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت
    echo 📥 يرجى تحميل Python من: https://www.python.org/downloads/
    pause
    exit
)

REM تشغيل النظام
python run_clinic.py

REM في حالة الخطأ
if %errorlevel% neq 0 (
    echo.
    echo ❌ حدث خطأ. جاري المحاولة مرة أخرى...
    echo 📦 تثبيت المكتبات...
    pip install -r requirements.txt >nul 2>&1
    echo 🗄️ إعداد قاعدة البيانات...
    python manage.py makemigrations >nul 2>&1
    python manage.py migrate >nul 2>&1
    echo 🔄 إعادة التشغيل...
    python run_clinic.py
)

pause
