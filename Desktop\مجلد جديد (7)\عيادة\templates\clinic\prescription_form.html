{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }} - نظام العيادة الطبية{% endblock %}

{% block extra_css %}
<style>
.prescription-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    transition: all 0.3s ease;
}

.prescription-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.form-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 1.5rem;
}

.info-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: none;
    color: white;
}

.drug-guide-card {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border: none;
    color: #333;
}

.safety-card {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border: none;
    color: #333;
}
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom" style="margin-top: 50px;">
    <h1 class="h2">
        <i class="bi bi-prescription2"></i>
        {{ title }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'prescription_list' %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right"></i> العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card prescription-card">
            <div class="card-header form-section">
                <h5 class="card-title mb-0">
                    <i class="bi bi-prescription2"></i> كتابة وصفة طبية جديدة
                </h5>
            </div>
            <div class="card-body">
                <form method="post" id="prescriptionForm">
                    {% csrf_token %}
                    {% crispy form %}

                    <div class="form-group">
        {% comment %} <label>أوقات الاستخدام:</label> {% endcomment %}
        <div id="times-container">
            {% for value, label in form.times.field.choices %}
                <div class="time-item">
                    <label>
                        <input type="checkbox" name="times" value="{{ value }}">
                        {{ label }}
                    </label>
                    <input type="text"
                           name="notes_{{ value }}"
                           class="form-control notes-input"
                           placeholder="ملاحظات {{ label }}"
                           style="display: none;">
                </div>
            {% endfor %}
        </div>
    </div>
    {% comment %}  {% endcomment %}
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card prescription-card info-card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i> إرشادات الوصفة الطبية
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-light">
                    <h6><i class="bi bi-lightbulb text-warning"></i> نصائح مهمة:</h6>
                    <ul class="mb-0 small">
                        <li>اكتب أسماء الأدوية بوضوح ودقة</li>
                        <li>حدد الجرعات والتركيزات بدقة</li>
                        <li>اذكر طريقة الاستخدام والتوقيت</li>
                        <li>حدد مدة العلاج بوضوح</li>
                        <li>أضف تحذيرات وتعليمات خاصة</li>
                    </ul>
                </div>

                <div class="alert alert-light">
                    <h6><i class="bi bi-exclamation-triangle text-warning"></i> تذكير:</h6>
                    <p class="mb-0 small">
                        تأكد من فحص الحساسيات والتداخلات الدوائية
                        قبل وصف أي دواء للمريض.
                    </p>
                </div>
            </div>
        </div>

        <!-- أدوية شائعة -->
        <div class="card prescription-card drug-guide-card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-capsule"></i> أدوية شائعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12 mb-2">
                        <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="addMedication('باراسيتامول 500 مجم')">
                            <i class="bi bi-plus-circle"></i> باراسيتامول 500 مجم
                        </button>
                    </div>
                    <div class="col-12 mb-2">
                        <button type="button" class="btn btn-outline-success btn-sm w-100" onclick="addMedication('إيبوبروفين 400 مجم')">
                            <i class="bi bi-plus-circle"></i> إيبوبروفين 400 مجم
                        </button>
                    </div>
                    <div class="col-12 mb-2">
                        <button type="button" class="btn btn-outline-warning btn-sm w-100" onclick="addMedication('أموكسيسيلين 500 مجم')">
                            <i class="bi bi-plus-circle"></i> أموكسيسيلين 500 مجم
                        </button>
                    </div>
                    <div class="col-12 mb-2">
                        <button type="button" class="btn btn-outline-info btn-sm w-100" onclick="addMedication('أوميبرازول 20 مجم')">
                            <i class="bi bi-plus-circle"></i> أوميبرازول 20 مجم
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- فحوصات السلامة -->
        <div class="card prescription-card safety-card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-shield-check"></i> فحوصات السلامة
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="bi bi-check-circle"></i> قائمة فحص:</h6>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="allergyCheck">
                        <label class="form-check-label" for="allergyCheck">
                            تم فحص الحساسيات
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="interactionCheck">
                        <label class="form-check-label" for="interactionCheck">
                            تم فحص التداخلات الدوائية
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="dosageCheck">
                        <label class="form-check-label" for="dosageCheck">
                            تم التأكد من الجرعات
                        </label>
                    </div>
                </div>
            </div>
        </div>

        {% if prescription %}
            <div class="card shadow mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history"></i> معلومات الوصفة
                    </h5>
                </div>
                <div class="card-body">
                    <p><strong>تاريخ الوصفة:</strong><br>
                       {{ prescription.prescription_date|date:"d/m/Y H:i" }}</p>
                    <p><strong>الطبيب:</strong><br>
                       {{ prescription.doctor.first_name }} {{ prescription.doctor.last_name }}</p>
                    {% if prescription.diagnosis %}
                        <p><strong>التشخيص المرتبط:</strong><br>
                           <a href="{% url 'diagnosis_detail' prescription.diagnosis.pk %}">
                               {{ prescription.diagnosis.diagnosis|truncatechars:30 }}
                           </a>
                        </p>
                    {% endif %}
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-expand textareas
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(textarea => {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    });

    // Enhanced medication field formatting
    const medicationsField = document.querySelector('textarea[name="medications"]');
    if (medicationsField) {
        medicationsField.placeholder = `مثال على كتابة الأدوية:
1. باراسيتامول 500 مجم - قرص واحد كل 8 ساعات لمدة 5 أيام
2. إيبوبروفين 400 مجم - قرص واحد عند الحاجة (لا يتجاوز 3 مرات يومياً)
3. أموكسيسيلين 500 مجم - كبسولة كل 12 ساعة لمدة 7 أيام
4. فيتامين د 1000 وحدة - قرص واحد يومياً`;
    }

    // Enhanced instructions field
    const instructionsField = document.querySelector('textarea[name="instructions"]');
    if (instructionsField) {
        instructionsField.placeholder = `تعليمات مهمة للمريض:
• تناول الأدوية في المواعيد المحددة
• تناول المضادات الحيوية مع الطعام
• لا تتوقف عن تناول المضاد الحيوي حتى لو تحسنت الأعراض
• في حالة ظهور طفح جلدي أو صعوبة في التنفس، توقف فوراً واتصل بالطبيب
• احتفظ بالأدوية في مكان بارد وجاف`;
    }

    // Follow-up date validation
    const followUpCheckbox = document.querySelector('input[name="follow_up_required"]');
    const followUpDate = document.querySelector('input[name="follow_up_date"]');

    if (followUpCheckbox && followUpDate) {
        followUpCheckbox.addEventListener('change', function() {
            if (this.checked) {
                followUpDate.required = true;
                followUpDate.style.borderColor = '#007bff';

                // Set minimum date to tomorrow
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);
                followUpDate.min = tomorrow.toISOString().split('T')[0];
            } else {
                followUpDate.required = false;
                followUpDate.style.borderColor = '';
            }
        });
    }

    // Prescription type change handler
    const prescriptionType = document.querySelector('select[name="prescription_type"]');
    if (prescriptionType) {
        prescriptionType.addEventListener('change', function() {
            const durationField = document.querySelector('input[name="duration"]');
            if (durationField) {
                switch(this.value) {
                    case 'acute':
                        durationField.placeholder = 'مثال: 5-7 أيام';
                        break;
                    case 'chronic':
                        durationField.placeholder = 'مثال: 3 أشهر مع المراجعة';
                        break;
                    case 'preventive':
                        durationField.placeholder = 'مثال: حسب الحاجة';
                        break;
                }
            }
        });
    }

    // Safety checks validation
    const allergyCheck = document.querySelector('input[name="allergies_checked"]');
    const interactionCheck = document.querySelector('input[name="drug_interactions_checked"]');
    const form = document.querySelector('#prescriptionForm');

    if (form) {
        form.addEventListener('submit', function(e) {
            const warnings = [];

            if (allergyCheck && !allergyCheck.checked) {
                warnings.push('لم يتم فحص الحساسيات');
            }

            if (interactionCheck && !interactionCheck.checked) {
                warnings.push('لم يتم فحص التداخلات الدوائية');
            }

            if (warnings.length > 0) {
                const proceed = confirm('تحذير:\n' + warnings.join('\n') + '\n\nهل تريد المتابعة؟');
                if (!proceed) {
                    e.preventDefault();
                    return false;
                }
            }
        });
    }

    // Character counter for text fields
    const textFields = document.querySelectorAll('textarea[name="medications"], textarea[name="instructions"], textarea[name="notes"]');
    textFields.forEach(field => {
        const maxLength = field.name === 'medications' ? 1000 : 500;
        const counter = document.createElement('small');
        counter.className = 'text-muted';
        field.parentNode.appendChild(counter);

        function updateCounter() {
            const remaining = maxLength - field.value.length;
            counter.textContent = `${field.value.length}/${maxLength} حرف`;

            if (remaining < 50) {
                counter.className = 'text-warning';
            } else if (remaining < 0) {
                counter.className = 'text-danger';
            } else {
                counter.className = 'text-muted';
            }
        }

        field.addEventListener('input', updateCounter);
        updateCounter();
    });
});

// Enhanced medication addition function
function addMedication(medication) {
    const medicationsField = document.querySelector('textarea[name="medications"]');
    if (medicationsField) {
        const currentValue = medicationsField.value;
        const lines = currentValue.split('\n').filter(line => line.trim() !== '');
        const nextNumber = lines.length + 1;

        const newMedication = `${nextNumber}. ${medication} - [اكتب الجرعة والتعليمات]`;

        if (currentValue.trim() === '') {
            medicationsField.value = newMedication;
        } else {
            medicationsField.value = currentValue + '\n' + newMedication;
        }

        // Focus and select the placeholder text
        medicationsField.focus();
        const startPos = medicationsField.value.lastIndexOf('[');
        const endPos = medicationsField.value.lastIndexOf(']') + 1;
        medicationsField.setSelectionRange(startPos, endPos);

        // Trigger input event to resize textarea
        medicationsField.dispatchEvent(new Event('input'));
    }
}

// Enhanced drug interaction checker
function checkDrugInteractions() {
    const medicationsField = document.querySelector('textarea[name="medications"]');
    if (medicationsField) {
        const medications = medicationsField.value.toLowerCase();
        const interactions = [];

        // Common drug interactions
        const drugInteractions = [
            {
                drugs: ['وارفارين', 'أسبرين'],
                warning: 'تحذير: زيادة خطر النزيف - مراقبة INR مطلوبة'
            },
            {
                drugs: ['ديجوكسين', 'فوروسيميد'],
                warning: 'تحذير: مراقبة مستوى البوتاسيوم والديجوكسين'
            },
            {
                drugs: ['ليثيوم', 'إيبوبروفين'],
                warning: 'تحذير: زيادة مستوى الليثيوم - مراقبة مطلوبة'
            },
            {
                drugs: ['سيمفاستاتين', 'كلاريثروميسين'],
                warning: 'تحذير: زيادة خطر اعتلال العضلات'
            }
        ];

        drugInteractions.forEach(interaction => {
            const foundDrugs = interaction.drugs.filter(drug => medications.includes(drug));
            if (foundDrugs.length >= 2) {
                interactions.push(interaction.warning);
            }
        });

        if (interactions.length > 0) {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-warning mt-2';
            alertDiv.innerHTML = `
                <h6><i class="bi bi-exclamation-triangle"></i> تحذيرات التفاعل الدوائي:</h6>
                <ul class="mb-0">
                    ${interactions.map(warning => `<li>${warning}</li>`).join('')}
                </ul>
            `;

            // Remove existing alerts
            const existingAlert = medicationsField.parentNode.querySelector('.alert-warning');
            if (existingAlert) {
                existingAlert.remove();
            }

            medicationsField.parentNode.appendChild(alertDiv);
        }
    }
}

// Add interaction checker on blur
document.addEventListener('DOMContentLoaded', function() {
    const medicationsField = document.querySelector('textarea[name="medications"]');
    if (medicationsField) {
        medicationsField.addEventListener('blur', checkDrugInteractions);
    }
});
</script>
<script>
document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        const notesInput = this.closest('.time-item').querySelector('.notes-input');
        notesInput.style.display = this.checked ? 'block' : 'none';
    });
});
</script>
{% endblock %}
