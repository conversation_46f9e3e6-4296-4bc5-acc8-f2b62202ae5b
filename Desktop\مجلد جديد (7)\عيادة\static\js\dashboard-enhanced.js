// Enhanced Dashboard JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize dashboard enhancements
    initializeAnimations();
    initializeCounters();
    initializeProgressBars();
    initializeRealTimeUpdates();
    initializeTooltips();
    initializeSearchFeatures();
});

// Animation Initialization
function initializeAnimations() {
    // Stagger card animations
    const cards = document.querySelectorAll('.dashboard-card, .stat-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });

    // Add hover effects to activity items
    const activityItems = document.querySelectorAll('.activity-item');
    activityItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(5px)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });
}

// Counter Animation
function initializeCounters() {
    const counters = document.querySelectorAll('[data-counter]');
    
    const animateCounter = (counter) => {
        const target = parseInt(counter.getAttribute('data-counter'));
        const current = parseInt(counter.textContent) || 0;
        const increment = target / 50;
        
        if (current < target) {
            counter.textContent = Math.ceil(current + increment);
            setTimeout(() => animateCounter(counter), 20);
        } else {
            counter.textContent = target;
        }
    };

    // Intersection Observer for counter animation
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    });

    counters.forEach(counter => {
        observer.observe(counter);
    });
}

// Progress Bar Animation
function initializeProgressBars() {
    const progressBars = document.querySelectorAll('.progress-bar');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const progressBar = entry.target;
                const width = progressBar.getAttribute('aria-valuenow');
                progressBar.style.width = '0%';
                
                setTimeout(() => {
                    progressBar.style.width = width + '%';
                }, 200);
                
                observer.unobserve(progressBar);
            }
        });
    });

    progressBars.forEach(bar => {
        observer.observe(bar);
    });
}

// Real-time Updates
function initializeRealTimeUpdates() {
    // Update time every minute
    updateCurrentTime();
    setInterval(updateCurrentTime, 60000);

    // Auto-refresh data every 5 minutes
    setInterval(refreshDashboardData, 300000);
}

function updateCurrentTime() {
    const timeElements = document.querySelectorAll('.current-time');
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-EG', {
        hour: '2-digit',
        minute: '2-digit'
    });
    
    timeElements.forEach(element => {
        element.textContent = timeString;
    });
}

function refreshDashboardData() {
    // Show loading indicator
    showLoadingIndicator();
    
    // Simulate data refresh (replace with actual AJAX call)
    setTimeout(() => {
        hideLoadingIndicator();
        showNotification('تم تحديث البيانات بنجاح', 'success');
    }, 2000);
}

// Tooltip Initialization
function initializeTooltips() {
    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Search Features
function initializeSearchFeatures() {
    const searchInput = document.getElementById('quickSearch');
    if (searchInput) {
        searchInput.addEventListener('input', handleQuickSearch);
    }
}

function handleQuickSearch(event) {
    const query = event.target.value.toLowerCase();
    const searchableItems = document.querySelectorAll('.activity-item');
    
    searchableItems.forEach(item => {
        const text = item.textContent.toLowerCase();
        if (text.includes(query)) {
            item.style.display = 'block';
            item.style.animation = 'fadeInUp 0.3s ease';
        } else {
            item.style.display = 'none';
        }
    });
}

// Utility Functions
function showLoadingIndicator() {
    const indicators = document.querySelectorAll('.loading-indicator');
    indicators.forEach(indicator => {
        indicator.style.display = 'block';
    });
}

function hideLoadingIndicator() {
    const indicators = document.querySelectorAll('.loading-indicator');
    indicators.forEach(indicator => {
        indicator.style.display = 'none';
    });
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Performance Monitoring
function initializePerformanceMonitoring() {
    // Monitor page load time
    window.addEventListener('load', function() {
        const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
        console.log(`Dashboard loaded in ${loadTime}ms`);
        
        if (loadTime > 3000) {
            showNotification('تحميل الصفحة بطيء. يرجى التحقق من الاتصال.', 'warning');
        }
    });
}

// Keyboard Shortcuts
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(event) {
        // Ctrl + / for search focus
        if (event.ctrlKey && event.key === '/') {
            event.preventDefault();
            const searchInput = document.getElementById('quickSearch');
            if (searchInput) {
                searchInput.focus();
            }
        }
        
        // Ctrl + R for refresh
        if (event.ctrlKey && event.key === 'r') {
            event.preventDefault();
            refreshDashboardData();
        }
    });
}

// Export functions for external use
window.DashboardEnhanced = {
    refreshData: refreshDashboardData,
    showNotification: showNotification,
    updateCounters: initializeCounters
};

// Initialize additional features
initializePerformanceMonitoring();
initializeKeyboardShortcuts();
