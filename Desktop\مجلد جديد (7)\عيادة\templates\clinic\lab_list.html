{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}قائمة الفحوصات المخبرية - نظام العيادة الطبية{% endblock %}

{% block extra_css %}
<style>
.lab-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.lab-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
}

.status-ordered { background-color: #17a2b8; color: white; }
.status-in_progress { background-color: #ffc107; color: black; }
.status-completed { background-color: #28a745; color: white; }
.status-cancelled { background-color: #dc3545; color: white; }

.priority-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 15px;
}

.priority-normal { background-color: #6c757d; }
.priority-urgent { background-color: #fd7e14; }
.priority-stat { background-color: #dc3545; }

.search-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.stats-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border: none;
}

.filter-section {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
}
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom" style="margin-top: 50px;">
    <h1 class="h2">
        <i class="bi bi-clipboard-data"></i>
        قائمة الفحوصات المخبرية
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'lab' %}" class="btn btn-primary me-2">
            <i class="bi bi-plus-circle"></i> إضافة فحص جديد
        </a>
        <button class="btn btn-outline-secondary" onclick="window.print()">
            <i class="bi bi-printer"></i> طباعة
        </button>
    </div>
</div>

<!-- Search and Filter Section -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card search-card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <input type="text" name="search" class="form-control"
                               placeholder="البحث بالاسم أو رقم الفحص..."
                               value="{{ request.GET.search }}">
                    </div>
                    <div class="col-md-3">
                        <select name="status" class="form-control">
                            <option value="">جميع الحالات</option>
                            <option value="ordered" {% if request.GET.status == 'ordered' %}selected{% endif %}>مطلوب</option>
                            <option value="in_progress" {% if request.GET.status == 'in_progress' %}selected{% endif %}>قيد التنفيذ</option>
                            <option value="completed" {% if request.GET.status == 'completed' %}selected{% endif %}>مكتمل</option>
                            <option value="cancelled" {% if request.GET.status == 'cancelled' %}selected{% endif %}>ملغي</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select name="category" class="form-control">
                            <option value="">جميع الفئات</option>
                            <option value="فحص الدم" {% if request.GET.category == 'فحص الدم' %}selected{% endif %}>فحص الدم</option>
                            <option value="فحص البول" {% if request.GET.category == 'فحص البول' %}selected{% endif %}>فحص البول</option>
                            <option value="فحص البراز" {% if request.GET.category == 'فحص البراز' %}selected{% endif %}>فحص البراز</option>
                            <option value="كيمياء حيوية" {% if request.GET.category == 'كيمياء حيوية' %}selected{% endif %}>كيمياء حيوية</option>
                            <option value="هرمونات" {% if request.GET.category == 'هرمونات' %}selected{% endif %}>هرمونات</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-light w-100">
                            <i class="bi bi-search"></i> بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h5><i class="bi bi-clipboard-data"></i> إحصائيات سريعة</h5>
                <div class="row">
                    <div class="col-6">
                        <h3>{{ total_tests }}</h3>
                        <small>إجمالي الفحوصات</small>
                    </div>
                    <div class="col-6">
                        <h3>{{ pending_tests }}</h3>
                        <small>فحوصات معلقة</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Lab Tests List -->
<div class="row">
    {% for test in tests %}
    <div class="col-lg-6 col-xl-4 mb-3">
        <div class="card lab-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="bi bi-clipboard-data"></i>
                    {{ test.test_id }}
                </h6>
                <div>
                    <span class="badge status-{{ test.status }}">
                        {{ test.get_status_display }}
                    </span>
                </div>
            </div>
            <div class="card-body">
                <h6 class="card-title">{{ test.test_name }}</h6>
                <p class="card-text">
                    <strong>المريض:</strong> {{ test.patient.full_name }}<br>
                    <strong>الفئة:</strong> {{ test.test_category }}<br>
                    <strong>تاريخ الطلب:</strong> {{ test.ordered_date|date:"d/m/Y" }}
                </p>

                {% if test.result_date %}
                <p class="text-success">
                    <i class="bi bi-check-circle"></i>
                    <strong>تاريخ النتيجة:</strong> {{ test.result_date|date:"d/m/Y" }}
                </p>
                {% endif %}

                {% if test.is_abnormal %}
                <div class="alert alert-warning py-1">
                    <i class="bi bi-exclamation-triangle"></i>
                    نتيجة غير طبيعية
                </div>
                {% endif %}
            </div>
            <div class="card-footer">
                <div class="btn-group w-100" role="group">
                    <a href="{% url 'lab_detail' test.pk %}" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-eye"></i> عرض
                    </a>
                    <a href="{% url 'lab_update' test.pk %}" class="btn btn-outline-warning btn-sm">
                        <i class="bi bi-pencil"></i> تعديل
                    </a>
                    {% if test.status != 'completed' %}
                    <a href="{% url 'lab_test_result_create' test.pk %}" class="btn btn-outline-success btn-sm">
                        <i class="bi bi-plus"></i> نتائج
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-12">
        <div class="card lab-card text-center">
            <div class="card-body py-5">
                <i class="bi bi-clipboard-x display-1 text-muted"></i>
                <h4 class="mt-3">لا توجد فحوصات مخبرية</h4>
                <p class="text-muted">لم يتم العثور على أي فحوصات مخبرية مطابقة للبحث</p>
                <a href="{% url 'lab' %}" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> إضافة فحص جديد
                </a>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if is_paginated %}
<nav aria-label="تنقل الصفحات">
    <ul class="pagination justify-content-center">
        {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}">السابق</a>
            </li>
        {% endif %}

        {% for num in page_obj.paginator.page_range %}
            {% if page_obj.number == num %}
                <li class="page-item active">
                    <span class="page-link">{{ num }}</span>
                </li>
            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ num }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}">{{ num }}</a>
                </li>
            {% endif %}
        {% endfor %}

        {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}">التالي</a>
            </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add animation to cards
    const cards = document.querySelectorAll('.lab-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 50);
    });

    // Auto-submit search form on select change
    const filterSelects = document.querySelectorAll('select[name="status"], select[name="category"]');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });

    // Clear filters button
    const clearBtn = document.createElement('button');
    clearBtn.type = 'button';
    clearBtn.className = 'btn-sm ms-2 btn btn-outline-success btn-sm';
    clearBtn.innerHTML = '<i class="bi bi-x-circle"></i> مسح الفلاتر';
    clearBtn.onclick = function() {
        window.location.href = window.location.pathname;
    };

    const searchForm = document.querySelector('form');
    if (searchForm) {
        searchForm.appendChild(clearBtn);
    }
});
</script>
{% endblock %}
