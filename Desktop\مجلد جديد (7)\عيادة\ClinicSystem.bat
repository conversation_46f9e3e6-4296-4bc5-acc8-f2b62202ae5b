@echo off
chcp 65001 >nul
title نظام العيادة الطبية - Medical Clinic System
color 0A

:MAIN_MENU
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🏥 نظام العيادة الطبية                    ║
echo ║                Medical Clinic Management System              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo ┌──────────────────────────────────────────────────────────────┐
echo │                        القائمة الرئيسية                        │
echo ├──────────────────────────────────────────────────────────────┤
echo │  [1] تشغيل النظام                                             │
echo │  [2] إعداد النظام (أول مرة)                                   │
echo │  [3] تحديث النظام                                             │
echo │  [4] نسخ احتياطي لقاعدة البيانات                              │
echo │  [5] استعادة نسخة احتياطية                                    │
echo │  [6] معلومات النظام                                           │
echo │  [0] خروج                                                     │
echo └──────────────────────────────────────────────────────────────┘
echo.
set /p choice="اختر رقم العملية: "

if "%choice%"=="1" goto START_SYSTEM
if "%choice%"=="2" goto SETUP_SYSTEM
if "%choice%"=="3" goto UPDATE_SYSTEM
if "%choice%"=="4" goto BACKUP_DB
if "%choice%"=="5" goto RESTORE_DB
if "%choice%"=="6" goto SYSTEM_INFO
if "%choice%"=="0" goto EXIT
goto MAIN_MENU

:START_SYSTEM
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      🚀 تشغيل النظام                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 التحقق من Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo 📥 يرجى تحميل Python من: https://www.python.org/downloads/
    echo ⚠️  تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    pause
    goto MAIN_MENU
)
echo ✅ Python متوفر

echo.
echo 📦 التحقق من المكتبات المطلوبة...
python -c "import django" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Django غير مثبت. جاري التثبيت...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المكتبات
        pause
        goto MAIN_MENU
    )
)
echo ✅ المكتبات متوفرة

echo.
echo 🗄️  التحقق من قاعدة البيانات...
if not exist "db.sqlite3" (
    echo ⚠️  قاعدة البيانات غير موجودة. جاري الإنشاء...
    python manage.py makemigrations
    python manage.py migrate
    echo ✅ تم إنشاء قاعدة البيانات
)

echo.
echo 👤 التحقق من حساب المدير...
python -c "from django.contrib.auth.models import User; import django; import os; os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'clinic_system.settings'); django.setup(); exit(0 if User.objects.filter(is_superuser=True).exists() else 1)" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  لا يوجد حساب مدير. جاري الإنشاء...
    python -c "import django; import os; os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'clinic_system.settings'); django.setup(); from django.contrib.auth.models import User; User.objects.create_superuser('admin', '<EMAIL>', 'admin123', first_name='مدير', last_name='النظام')" >nul 2>&1
    echo ✅ تم إنشاء حساب المدير
    echo    📧 اسم المستخدم: admin
    echo    🔑 كلمة المرور: admin123
)

echo.
echo 🌐 بدء تشغيل الخادم...
echo ⏳ يرجى الانتظار...
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║  ✅ النظام يعمل الآن!                                        ║
echo ║  🌐 الرابط: http://127.0.0.1:8000                          ║
echo ║  👤 اسم المستخدم: admin                                     ║
echo ║  🔑 كلمة المرور: admin123                                   ║
echo ║                                                              ║
echo ║  ⚠️  لإيقاف النظام: اضغط Ctrl+C                             ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM فتح المتصفح
timeout /t 3 /nobreak >nul
start http://127.0.0.1:8000

REM تشغيل الخادم
python manage.py runserver 127.0.0.1:8000
goto MAIN_MENU

:SETUP_SYSTEM
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      ⚙️ إعداد النظام                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📦 تثبيت المكتبات المطلوبة...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المكتبات
    pause
    goto MAIN_MENU
)

echo.
echo 🗄️  إنشاء قاعدة البيانات...
python manage.py makemigrations
python manage.py migrate

echo.
echo 👤 إنشاء حساب المدير...
python manage.py createsuperuser

echo.
echo ✅ تم إعداد النظام بنجاح!
pause
goto MAIN_MENU

:UPDATE_SYSTEM
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      🔄 تحديث النظام                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📦 تحديث المكتبات...
pip install --upgrade -r requirements.txt

echo.
echo 🗄️  تحديث قاعدة البيانات...
python manage.py makemigrations
python manage.py migrate

echo.
echo ✅ تم تحديث النظام بنجاح!
pause
goto MAIN_MENU

:BACKUP_DB
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                   💾 نسخ احتياطي للبيانات                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

if not exist "backups" mkdir backups

for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "datestamp=%YYYY%-%MM%-%DD%_%HH%-%Min%-%Sec%"

copy "db.sqlite3" "backups\backup_%datestamp%.sqlite3"
if %errorlevel% equ 0 (
    echo ✅ تم إنشاء النسخة الاحتياطية: backup_%datestamp%.sqlite3
) else (
    echo ❌ فشل في إنشاء النسخة الاحتياطية
)

pause
goto MAIN_MENU

:RESTORE_DB
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                   📁 استعادة نسخة احتياطية                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

if not exist "backups" (
    echo ❌ لا توجد نسخ احتياطية
    pause
    goto MAIN_MENU
)

echo النسخ الاحتياطية المتوفرة:
echo.
dir /b backups\*.sqlite3
echo.
set /p backup_file="أدخل اسم الملف للاستعادة: "

if exist "backups\%backup_file%" (
    copy "backups\%backup_file%" "db.sqlite3"
    echo ✅ تم استعادة النسخة الاحتياطية بنجاح
) else (
    echo ❌ الملف غير موجود
)

pause
goto MAIN_MENU

:SYSTEM_INFO
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      ℹ️ معلومات النظام                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🏥 اسم النظام: نظام إدارة العيادة الطبية
echo 📅 الإصدار: 1.0.0
echo 🐍 Python: 
python --version
echo 🌐 Django: 
python -c "import django; print(django.get_version())" 2>nul
echo 💾 قاعدة البيانات: SQLite
echo 📁 مسار المشروع: %CD%
echo.
echo 📊 إحصائيات قاعدة البيانات:
python -c "import django; import os; os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'clinic_system.settings'); django.setup(); from clinic.models import Patient, Appointment, Diagnosis, Prescription; print(f'المرضى: {Patient.objects.count()}'); print(f'المواعيد: {Appointment.objects.count()}'); print(f'التشخيصات: {Diagnosis.objects.count()}'); print(f'الوصفات: {Prescription.objects.count()}')" 2>nul
echo.
pause
goto MAIN_MENU

:EXIT
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      👋 شكراً لاستخدام النظام                ║
echo ║                   Medical Clinic System                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
timeout /t 2 /nobreak >nul
exit
