{% extends 'base.html' %}

{% block title %}قائمة الاشعة - نظام العيادة الطبية{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="bg-gradient-warning text-dark rounded-3 p-4 mb-4 shadow" style="margin-top: 50px;">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    <div class="bg-white bg-opacity-25 rounded-circle p-3 me-3">
                        <i class="bi bi-camera fs-2 text-dark"></i>
                    </div>
                    <div>
                        <h1 class="h2 mb-1 text-dark">قائمة الاشعة</h1>
                        <p class="mb-0 text-dark opacity-75">إدارة وعرض الاشعة الطبية</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{% url 'rays' %}" class="btn btn-dark btn-lg">
                    <i class="bi bi-camera"></i>
                    <span class="d-none d-sm-inline">إضافة اشعة جديد</span>
                    <span class="d-inline d-sm-none">إضافة اشعة</span>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Search Form -->
<div class="row mb-3">
    <div class="col-md-6">
        <form method="get" class="d-flex ">
            <input type="text" name="search" class="form-control"
                   placeholder="البحث بالمريض..."
                   value="{{ search_query }}">
            <button type="submit" class="btn btn-outline-secondary">
                <i class="bi bi-search"></i> بحث
            </button>
        </form>
    </div>
</div>

<!-- Diagnoses Table -->
<div class="card shadow">
    <div class="card-body">
        {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>المريض</th>
                            <th>التاريخ</th>
                            <th>الطبيب</th>
                            <th>اسم الاشعة</th>
                            <th>الجزء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for diagnosis in page_obj %}
                            <tr>
                                <td>
                                    <a href="{% url 'patient_detail' diagnosis.patient.pk %}" class="text-decoration-none">
                                        {{ diagnosis.patient.full_name }}
                                    </a>
                                </td>
                                <td>{{ diagnosis.ordered_date|date:"d/m/Y H:i" }}</td>
                                <td>{{ diagnosis.doctor.first_name }} {{ diagnosis.doctor.last_name }}</td>
                                <td>{{ diagnosis.rays_name|truncatechars:50 }}</td>
                                <td>{{ diagnosis.part|truncatechars:40 }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'rays_detail' diagnosis.pk %}"
                                           class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="{% url 'rays_update' diagnosis.pk %}"
                                           class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="{% url 'rays_delete' diagnosis.pk %}"
                                           class="btn btn-sm btn-outline-danger" title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
                <nav aria-label="Diagnosis pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}">
                                    الأولى
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">
                                    السابقة
                                </a>
                            </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}">
                                        {{ num }}
                                    </a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">
                                    التالية
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}">
                                    الأخيرة
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}

        {% else %}
            <div class="text-center py-5">
                <i class="bi bi-camera display-1 text-muted"></i>
                <h4 class="mt-3">لا توجد اشعة</h4>
                <p class="text-muted">لم يتم العثور على أي اشعة{% if search_query %} للبحث "{{ search_query }}"{% endif %}</p>
                <a href="{% url 'rays' %}" class="btn btn-primary">
                    <i class="bi bi-camera"></i> إضافة أول اشعة
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
