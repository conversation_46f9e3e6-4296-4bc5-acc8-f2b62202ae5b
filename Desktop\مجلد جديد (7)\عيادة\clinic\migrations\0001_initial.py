# Generated by Django 4.2.17 on 2025-06-01 13:25

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Appointment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "appointment_date",
                    models.DateTimeField(verbose_name="تاريخ ووقت الموعد"),
                ),
                (
                    "duration",
                    models.PositiveIntegerField(
                        default=30, verbose_name="مدة الموعد (بالدقائق)"
                    ),
                ),
                ("reason", models.TextField(verbose_name="سبب الزيارة")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("scheduled", "مجدول"),
                            ("completed", "مكتمل"),
                            ("cancelled", "ملغي"),
                            ("no_show", "لم يحضر"),
                        ],
                        default="scheduled",
                        max_length=20,
                        verbose_name="حالة الموعد",
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="ملاحظات"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "doctor",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="الطبيب",
                    ),
                ),
            ],
            options={
                "verbose_name": "موعد",
                "verbose_name_plural": "المواعيد",
                "ordering": ["-appointment_date"],
            },
        ),
        migrations.CreateModel(
            name="Diagnosis",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "diagnosis_date",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ التشخيص"
                    ),
                ),
                ("symptoms", models.TextField(verbose_name="الأعراض")),
                ("diagnosis", models.TextField(verbose_name="التشخيص")),
                ("treatment_plan", models.TextField(verbose_name="خطة العلاج")),
                (
                    "follow_up_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="تاريخ المتابعة"
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="ملاحظات"),
                ),
                (
                    "appointment",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="clinic.appointment",
                        verbose_name="الموعد",
                    ),
                ),
                (
                    "doctor",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="الطبيب",
                    ),
                ),
            ],
            options={
                "verbose_name": "تشخيص",
                "verbose_name_plural": "التشخيصات",
                "ordering": ["-diagnosis_date"],
            },
        ),
        migrations.CreateModel(
            name="Patient",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(max_length=100, verbose_name="الاسم الأول"),
                ),
                (
                    "last_name",
                    models.CharField(max_length=100, verbose_name="اسم العائلة"),
                ),
                ("date_of_birth", models.DateField(verbose_name="تاريخ الميلاد")),
                (
                    "gender",
                    models.CharField(
                        choices=[("M", "ذكر"), ("F", "أنثى")],
                        max_length=1,
                        verbose_name="الجنس",
                    ),
                ),
                ("phone", models.CharField(max_length=15, verbose_name="رقم الهاتف")),
                (
                    "email",
                    models.EmailField(
                        blank=True,
                        max_length=254,
                        null=True,
                        verbose_name="البريد الإلكتروني",
                    ),
                ),
                ("address", models.TextField(verbose_name="العنوان")),
                (
                    "emergency_contact",
                    models.CharField(
                        max_length=100, verbose_name="جهة الاتصال في الطوارئ"
                    ),
                ),
                (
                    "emergency_phone",
                    models.CharField(max_length=15, verbose_name="هاتف الطوارئ"),
                ),
                (
                    "medical_history",
                    models.TextField(
                        blank=True, null=True, verbose_name="التاريخ المرضي"
                    ),
                ),
                (
                    "allergies",
                    models.TextField(blank=True, null=True, verbose_name="الحساسيات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
            ],
            options={
                "verbose_name": "مريض",
                "verbose_name_plural": "المرضى",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Prescription",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "prescription_date",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الوصفة"
                    ),
                ),
                ("medications", models.TextField(verbose_name="الأدوية والجرعات")),
                ("instructions", models.TextField(verbose_name="تعليمات الاستخدام")),
                (
                    "duration",
                    models.CharField(max_length=100, verbose_name="مدة العلاج"),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="ملاحظات"),
                ),
                (
                    "diagnosis",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="clinic.diagnosis",
                        verbose_name="التشخيص",
                    ),
                ),
                (
                    "doctor",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="الطبيب",
                    ),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="clinic.patient",
                        verbose_name="المريض",
                    ),
                ),
            ],
            options={
                "verbose_name": "وصفة طبية",
                "verbose_name_plural": "الوصفات الطبية",
                "ordering": ["-prescription_date"],
            },
        ),
        migrations.CreateModel(
            name="Payment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="المبلغ"
                    ),
                ),
                (
                    "payment_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الدفع"),
                ),
                (
                    "payment_method",
                    models.CharField(
                        choices=[
                            ("cash", "نقدي"),
                            ("card", "بطاقة ائتمان"),
                            ("bank_transfer", "تحويل بنكي"),
                            ("insurance", "تأمين"),
                        ],
                        max_length=20,
                        verbose_name="طريقة الدفع",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "في الانتظار"),
                            ("paid", "مدفوع"),
                            ("cancelled", "ملغي"),
                            ("refunded", "مسترد"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="حالة الدفع",
                    ),
                ),
                ("description", models.TextField(verbose_name="وصف الخدمة")),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="ملاحظات"),
                ),
                (
                    "receipt_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="رقم الإيصال"
                    ),
                ),
                (
                    "appointment",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="clinic.appointment",
                        verbose_name="الموعد",
                    ),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="clinic.patient",
                        verbose_name="المريض",
                    ),
                ),
            ],
            options={
                "verbose_name": "دفعة",
                "verbose_name_plural": "المدفوعات",
                "ordering": ["-payment_date"],
            },
        ),
        migrations.AddField(
            model_name="diagnosis",
            name="patient",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="clinic.patient",
                verbose_name="المريض",
            ),
        ),
        migrations.AddField(
            model_name="appointment",
            name="patient",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="clinic.patient",
                verbose_name="المريض",
            ),
        ),
    ]
