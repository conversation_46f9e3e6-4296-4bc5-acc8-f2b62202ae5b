# Generated by Django 4.2.7 on 2025-06-25 19:43

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("clinic", "0006_patient_is_active"),
    ]

    operations = [
        migrations.CreateModel(
            name="LabTest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "test_id",
                    models.CharField(
                        max_length=20, unique=True, verbose_name="رقم الفحص"
                    ),
                ),
                (
                    "test_name",
                    models.CharField(max_length=200, verbose_name="اسم الفحص"),
                ),
                (
                    "test_category",
                    models.CharField(max_length=100, verbose_name="فئة الفحص"),
                ),
                (
                    "instructions",
                    models.TextField(blank=True, verbose_name="تعليمات الفحص"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("ordered", "مطلوب"),
                            ("in_progress", "قيد التنفيذ"),
                            ("completed", "مكتمل"),
                            ("cancelled", "ملغي"),
                        ],
                        default="ordered",
                        max_length=15,
                        verbose_name="الحالة",
                    ),
                ),
                (
                    "ordered_date",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="تاريخ الطلب"
                    ),
                ),
                (
                    "sample_collected_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ أخذ العينة"
                    ),
                ),
                (
                    "result_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ النتيجة"
                    ),
                ),
                ("results", models.TextField(blank=True, verbose_name="النتائج")),
                (
                    "normal_range",
                    models.CharField(
                        blank=True, max_length=200, verbose_name="المعدل الطبيعي"
                    ),
                ),
                (
                    "is_abnormal",
                    models.BooleanField(default=False, verbose_name="غير طبيعي"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "doctor",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="الطبيب الطالب",
                    ),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="clinic.patient",
                        verbose_name="المريض",
                    ),
                ),
                (
                    "technician",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="lab_tests_performed",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="فني المختبر",
                    ),
                ),
            ],
            options={
                "verbose_name": "الفحص المخبري",
                "verbose_name_plural": "الفحوصات المخبرية",
                "ordering": ["-ordered_date"],
            },
        ),
    ]
