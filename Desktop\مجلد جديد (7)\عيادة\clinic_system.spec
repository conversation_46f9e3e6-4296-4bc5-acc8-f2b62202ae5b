# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from pathlib import Path

# مسار المشروع
project_path = Path(__file__).parent

# إضافة مسارات Django
django_paths = [
    str(project_path),
    str(project_path / 'clinic'),
    str(project_path / 'clinic_system'),
]

# البيانات المطلوبة (templates, static files, etc.)
datas = [
    (str(project_path / 'templates'), 'templates'),
    (str(project_path / 'clinic' / 'templates'), 'clinic/templates'),
    (str(project_path / 'static'), 'static'),
    (str(project_path / 'db.sqlite3'), '.'),  # قاعدة البيانات إذا كانت موجودة
]

# المكتبات المخفية المطلوبة
hiddenimports = [
    'django',
    'django.core',
    'django.core.management',
    'django.core.management.commands',
    'django.core.management.commands.runserver',
    'django.contrib',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.db',
    'django.db.backends',
    'django.db.backends.sqlite3',
    'clinic',
    'clinic.models',
    'clinic.views',
    'clinic.forms',
    'clinic.urls',
    'clinic.auth_views',
    'clinic_system',
    'clinic_system.settings',
    'clinic_system.urls',
    'clinic_system.wsgi',
    'crispy_forms',
    'crispy_bootstrap5',
]

# الملفات المستبعدة
excludes = [
    'tkinter',
    'matplotlib',
    'numpy',
    'scipy',
    'pandas',
    'jupyter',
    'IPython',
]

block_cipher = None

a = Analysis(
    ['run_clinic.py'],
    pathex=django_paths,
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='ClinicSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # يمكن إضافة أيقونة هنا
)
