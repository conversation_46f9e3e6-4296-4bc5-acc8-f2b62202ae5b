{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}تسجيل الدخول - نظام العيادة الطبية{% endblock %}

{% block content %}
<div class="row justify-content-center" style="margin-top: 50px;">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center">
                <h4><i class="bi bi-hospital"></i> نظام العيادة الطبية</h4>
                <p class="mb-0">تسجيل الدخول</p>
            </div>
            <div class="card-body">
                {% if form.errors %}
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            {% for field, errors in form.errors.items %}
                                {% for error in errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}

                <form method="post">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="{{ form.username.id_for_label }}" class="form-label">
                            <i class="bi bi-person"></i> اسم المستخدم
                        </label>
                        <input type="text" name="username" class="form-control"
                               id="{{ form.username.id_for_label }}"
                               value="{{ form.username.value|default:'' }}" required>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.password.id_for_label }}" class="form-label">
                            <i class="bi bi-lock"></i> كلمة المرور
                        </label>
                        <input type="password" name="password" class="form-control"
                               id="{{ form.password.id_for_label }}" required>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-box-arrow-in-right"></i> تسجيل الدخول
                        </button>
                    </div>
                </form>

                <!-- Additional Links -->
                <div class="text-center mt-3">
                    <p class="mb-2">
                        <a href="{% url 'password_reset_request' %}" class="text-decoration-none text-muted">
                            <i class="bi bi-key"></i> نسيت كلمة المرور؟
                        </a>
                    </p>
                    <p class="mb-0">
                        ليس لديك حساب؟
                        <a href="{% url 'register' %}" class="text-decoration-none fw-bold">
                            إنشاء حساب جديد
                        </a>
                    </p>
                </div>
            </div>
        </div>

        <div class="text-center mt-3">
            <small class="text-muted">
                نظام إدارة العيادة الطبية - جميع الحقوق محفوظة
            </small>
        </div>
    </div>
</div>
{% endblock %}
