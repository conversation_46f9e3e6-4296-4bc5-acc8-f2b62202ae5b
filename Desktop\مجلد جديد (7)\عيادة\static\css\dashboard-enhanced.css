/* Enhanced Dashboard Styles */

/* Animation Keyframes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Dashboard Cards */
.dashboard-card {
    animation: fadeInUp 0.6s ease-out;
    transition: all 0.3s ease;
    border: none;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Stat Cards */
.stat-card {
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.stat-card .card-body {
    position: relative;
    z-index: 2;
}

/* Progress Bars */
.progress-enhanced {
    height: 8px;
    border-radius: 10px;
    background-color: #e9ecef;
    overflow: hidden;
}

.progress-enhanced .progress-bar {
    border-radius: 10px;
    transition: width 1s ease-in-out;
}

/* Quick Actions */
.quick-action-btn {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 20px;
    text-decoration: none;
    color: #495057;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 120px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.quick-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
}

.quick-action-btn:hover::before {
    left: 100%;
}

.quick-action-btn:hover {
    border-color: #667eea;
    color: #667eea;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.quick-action-btn i {
    font-size: 2.5rem;
    margin-bottom: 10px;
    transition: transform 0.3s ease;
}

.quick-action-btn:hover i {
    transform: scale(1.1);
    animation: pulse 1s infinite;
}

/* Activity Items */
.activity-item {
    border-left: 4px solid transparent;
    transition: all 0.3s ease;
}

.activity-item:hover {
    border-left-color: #667eea;
    background-color: #f8f9fc;
    transform: translateX(5px);
}

/* Performance Circles */
.performance-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    position: relative;
    overflow: hidden;
}

.performance-circle::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: rotate 3s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Revenue Card */
.revenue-card {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 15px;
}

.revenue-card .card-body {
    position: relative;
    z-index: 2;
}

.revenue-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

/* Header Enhancements */
.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .stat-card {
        margin-bottom: 20px;
    }
    
    .quick-action-btn {
        height: 100px;
        padding: 15px;
    }
    
    .quick-action-btn i {
        font-size: 2rem;
    }
    
    .performance-circle {
        width: 60px;
        height: 60px;
    }
}

/* Loading Animation */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .dashboard-card {
        background-color: #2d3748;
        color: #e2e8f0;
    }
    
    .quick-action-btn {
        background-color: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
    
    .activity-item:hover {
        background-color: #4a5568;
    }
}

/* Print Styles */
@media print {
    .quick-stats,
    .quick-action-btn {
        display: none;
    }
    
    .dashboard-card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
