{% extends 'control_panels/base_dashboard.html' %}
{% load static %}

{% block title %}إضافة تشخيص{% endblock %}

{% block extra_css %}
<style>
    .add-diagnosis-container {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .diagnosis-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-bottom: 30px;
        overflow: hidden;
        max-width: 1000px;
        margin: 0 auto;
    }
    
    .diagnosis-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 30px;
        text-align: center;
    }
    
    .diagnosis-title {
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 10px;
    }
    
    .form-section {
        background: white;
        border-radius: 10px;
        padding: 30px;
        margin: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    
    .section-header {
        margin-bottom: 25px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e9ecef;
    }
    
    .section-title {
        color: #495057;
        font-size: 1.3rem;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }
    
    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 12px 15px;
        transition: all 0.3s ease;
        font-size: 1rem;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
    
    .required {
        color: #dc3545;
    }
    
    .diagnosis-type-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 15px;
    }
    
    .diagnosis-type-card {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
    }
    
    .diagnosis-type-card:hover {
        border-color: #28a745;
        background: #f8f9fa;
    }
    
    .diagnosis-type-card.selected {
        border-color: #28a745;
        background: #d4edda;
    }
    
    .diagnosis-icon {
        font-size: 2rem;
        color: #28a745;
        margin-bottom: 10px;
    }
    
    .diagnosis-name {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }
    
    .diagnosis-desc {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .form-actions {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e9ecef;
        text-align: center;
    }
    
    .btn-primary-action {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 25px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-size: 1.1rem;
    }
    
    .btn-primary-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(40, 167, 69, 0.3);
        color: white;
    }
    
    .btn-secondary {
        background: #6c757d;
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 25px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-size: 1.1rem;
        margin-left: 15px;
    }
    
    .btn-secondary:hover {
        background: #5a6268;
        color: white;
        transform: translateY(-2px);
    }
    
    @media (max-width: 768px) {
        .diagnosis-type-grid {
            grid-template-columns: 1fr;
        }
        
        .form-actions .btn-primary-action,
        .form-actions .btn-secondary {
            display: block;
            width: 100%;
            margin: 10px 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="add-diagnosis-container">
    <div class="container-fluid">
        <!-- رأس الصفحة -->
        <div class="diagnosis-card">
            <div class="diagnosis-header">
                <h1 class="diagnosis-title">
                    <i class="fas fa-stethoscope me-3"></i>
                    إضافة تشخيص جديد
                </h1>
                <p>{% if record %}للسجل الطبي: {{ record.record_id }}{% else %}تشخيص مستقل{% endif %}</p>
            </div>
        </div>
        
        <!-- نموذج إضافة التشخيص -->
        <div class="diagnosis-card">
            <div class="form-section">
                <form method="post" id="diagnosisForm">
                    {% csrf_token %}
                    
                    <!-- نوع التشخيص -->
                    <div class="section-header">
                        <h3 class="section-title">
                            <i class="fas fa-clipboard-check"></i>
                            نوع التشخيص
                        </h3>
                    </div>
                    
                    <div class="diagnosis-type-grid">
                        <div class="diagnosis-type-card" onclick="selectDiagnosisType('PRIMARY', this)">
                            <div class="diagnosis-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="diagnosis-name">تشخيص أولي</div>
                            <div class="diagnosis-desc">التشخيص الرئيسي</div>
                        </div>
                        
                        <div class="diagnosis-type-card" onclick="selectDiagnosisType('SECONDARY', this)">
                            <div class="diagnosis-icon">
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="diagnosis-name">تشخيص ثانوي</div>
                            <div class="diagnosis-desc">تشخيص إضافي</div>
                        </div>
                        
                        <div class="diagnosis-type-card" onclick="selectDiagnosisType('DIFFERENTIAL', this)">
                            <div class="diagnosis-icon">
                                <i class="fas fa-question"></i>
                            </div>
                            <div class="diagnosis-name">تشخيص تفريقي</div>
                            <div class="diagnosis-desc">احتمالات متعددة</div>
                        </div>
                        
                        <div class="diagnosis-type-card" onclick="selectDiagnosisType('PROVISIONAL', this)">
                            <div class="diagnosis-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="diagnosis-name">تشخيص مؤقت</div>
                            <div class="diagnosis-desc">في انتظار التأكيد</div>
                        </div>
                    </div>
                    
                    <input type="hidden" id="selected_diagnosis_type" name="diagnosis_type" required>
                    
                    <!-- تفاصيل التشخيص -->
                    <div class="section-header mt-4">
                        <h3 class="section-title">
                            <i class="fas fa-clipboard-list"></i>
                            تفاصيل التشخيص
                        </h3>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="diagnosis_code" class="form-label">رمز التشخيص (ICD-10)</label>
                            <input type="text" class="form-control" id="diagnosis_code" name="diagnosis_code"
                                   placeholder="مثال: J44.1">
                        </div>
                        <div class="col-md-6">
                            <label for="diagnosis_name" class="form-label">اسم التشخيص <span class="required">*</span></label>
                            <input type="text" class="form-control" id="diagnosis_name" name="diagnosis_name" required
                                   placeholder="مثال: التهاب الشعب الهوائية المزمن">
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="severity" class="form-label">الشدة <span class="required">*</span></label>
                            <select class="form-select" id="severity" name="severity" required>
                                <option value="">اختر الشدة</option>
                                <option value="MILD">خفيف</option>
                                <option value="MODERATE">متوسط</option>
                                <option value="SEVERE">شديد</option>
                                <option value="CRITICAL">حرج</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="status" class="form-label">الحالة <span class="required">*</span></label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="ACTIVE">نشط</option>
                                <option value="RESOLVED">محلول</option>
                                <option value="CHRONIC">مزمن</option>
                                <option value="RECURRENT">متكرر</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف التشخيص <span class="required">*</span></label>
                        <textarea class="form-control" id="description" name="description"
                                  rows="4" required placeholder="وصف مفصل للتشخيص والأعراض"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="clinical_findings" class="form-label">الموجودات السريرية</label>
                        <textarea class="form-control" id="clinical_findings" name="clinical_findings"
                                  rows="3" placeholder="الموجودات السريرية المؤيدة للتشخيص"></textarea>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="onset_date" class="form-label">تاريخ البداية</label>
                            <input type="date" class="form-control" id="onset_date" name="onset_date">
                        </div>
                        <div class="col-md-6">
                            <label for="diagnosed_by" class="form-label">شخص بواسطة</label>
                            <input type="text" class="form-control" id="diagnosed_by" name="diagnosed_by"
                                   value="{{ request.user.get_full_name }}" readonly>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="treatment_plan" class="form-label">خطة العلاج</label>
                        <textarea class="form-control" id="treatment_plan" name="treatment_plan"
                                  rows="3" placeholder="الخطة العلاجية المقترحة"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-control" id="notes" name="notes"
                                  rows="2" placeholder="أي ملاحظات إضافية"></textarea>
                    </div>
                    
                    <!-- إجراءات النموذج -->
                    <div class="form-actions">
                        <button type="submit" class="btn-primary-action">
                            <i class="fas fa-save"></i>
                            حفظ التشخيص
                        </button>
                        <a href="{% if record %}{% url 'medical_records:record_detail' record.id %}{% else %}{% url 'medical_records:record_list' %}{% endif %}" class="btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// اختيار نوع التشخيص
function selectDiagnosisType(type, element) {
    // إزالة التحديد من جميع البطاقات
    document.querySelectorAll('.diagnosis-type-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // تحديد البطاقة المختارة
    element.classList.add('selected');
    document.getElementById('selected_diagnosis_type').value = type;
}

// التحقق من صحة النموذج
document.getElementById('diagnosisForm').addEventListener('submit', function(event) {
    const diagnosisType = document.getElementById('selected_diagnosis_type').value;

    if (!diagnosisType) {
        event.preventDefault();
        alert('يرجى اختيار نوع التشخيص');
        return false;
    }
});
</script>
{% endblock %}
