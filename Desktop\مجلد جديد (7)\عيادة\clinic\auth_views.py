from django.shortcuts import render, redirect
from django.contrib.auth import login
from django.contrib.auth.models import User
from django.contrib import messages
from django.contrib.auth.forms import UserCreationForm
from django.core.mail import send_mail
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.encoding import force_bytes, force_str
from django.template.loader import render_to_string
from django.conf import settings
from django import forms


class CustomUserCreationForm(UserCreationForm):
    """نموذج إنشاء حساب مخصص"""
    email = forms.EmailField(required=True, label="البريد الإلكتروني")
    first_name = forms.CharField(max_length=30, required=True, label="الاسم الأول")
    last_name = forms.CharField(max_length=30, required=True, label="اسم العائلة")

    class Meta:
        model = User
        fields = ("username", "first_name", "last_name", "email", "password1", "password2")

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['username'].label = "اسم المستخدم"
        self.fields['password1'].label = "كلمة المرور"
        self.fields['password2'].label = "تأكيد كلمة المرور"
        
        # Add CSS classes
        for field_name, field in self.fields.items():
            field.widget.attrs['class'] = 'form-control'

    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data["email"]
        user.first_name = self.cleaned_data["first_name"]
        user.last_name = self.cleaned_data["last_name"]
        if commit:
            user.save()
        return user


class PasswordResetRequestForm(forms.Form):
    """نموذج طلب إعادة تعيين كلمة المرور"""
    email = forms.EmailField(
        label="البريد الإلكتروني",
        widget=forms.EmailInput(attrs={'class': 'form-control'})
    )


def register_view(request):
    """عرض إنشاء حساب جديد"""
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            login(request, user)
            messages.success(request, 'تم إنشاء حسابك بنجاح! مرحباً بك في نظام العيادة الطبية.')
            return redirect('dashboard')
    else:
        form = CustomUserCreationForm()
    
    return render(request, 'registration/register.html', {'form': form})


def password_reset_request(request):
    """طلب إعادة تعيين كلمة المرور"""
    if request.method == 'POST':
        form = PasswordResetRequestForm(request.POST)
        if form.is_valid():
            email = form.cleaned_data['email']
            try:
                user = User.objects.get(email=email)
                
                # Generate token
                token = default_token_generator.make_token(user)
                uid = urlsafe_base64_encode(force_bytes(user.pk))
                
                # Create reset link
                reset_link = request.build_absolute_uri(
                    f'/accounts/password-reset-confirm/{uid}/{token}/'
                )
                
                # Send email
                subject = 'إعادة تعيين كلمة المرور - نظام العيادة الطبية'
                message = render_to_string('registration/password_reset_email.html', {
                    'user': user,
                    'reset_link': reset_link,
                })
                
                try:
                    send_mail(
                        subject,
                        message,
                        settings.DEFAULT_FROM_EMAIL,
                        [email],
                        fail_silently=False,
                    )
                    messages.success(
                        request, 
                        'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني.'
                    )
                except Exception as e:
                    messages.error(
                        request, 
                        'حدث خطأ في إرسال البريد الإلكتروني. يرجى المحاولة مرة أخرى.'
                    )
                
            except User.DoesNotExist:
                messages.error(request, 'لا يوجد حساب مرتبط بهذا البريد الإلكتروني.')
                
            return redirect('password_reset_request')
    else:
        form = PasswordResetRequestForm()
    
    return render(request, 'registration/password_reset_request.html', {'form': form})


def password_reset_confirm(request, uidb64, token):
    """تأكيد إعادة تعيين كلمة المرور"""
    try:
        uid = force_str(urlsafe_base64_decode(uidb64))
        user = User.objects.get(pk=uid)
    except (TypeError, ValueError, OverflowError, User.DoesNotExist):
        user = None

    if user is not None and default_token_generator.check_token(user, token):
        if request.method == 'POST':
            password1 = request.POST.get('password1')
            password2 = request.POST.get('password2')
            
            if password1 and password2:
                if password1 == password2:
                    if len(password1) >= 8:
                        user.set_password(password1)
                        user.save()
                        messages.success(request, 'تم تغيير كلمة المرور بنجاح! يمكنك الآن تسجيل الدخول.')
                        return redirect('login')
                    else:
                        messages.error(request, 'كلمة المرور يجب أن تكون 8 أحرف على الأقل.')
                else:
                    messages.error(request, 'كلمتا المرور غير متطابقتين.')
            else:
                messages.error(request, 'يرجى إدخال كلمة المرور وتأكيدها.')
        
        return render(request, 'registration/password_reset_confirm.html', {
            'validlink': True,
            'user': user
        })
    else:
        return render(request, 'registration/password_reset_confirm.html', {
            'validlink': False
        })
