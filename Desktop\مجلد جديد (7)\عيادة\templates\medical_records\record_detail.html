{% extends 'control_panels/base_dashboard.html' %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="record-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-1">
                            <i class="fas fa-file-medical text-primary me-2"></i>
                            السجل الطبي - {{ record.record_id }}
                        </h2>
                        <p class="text-muted mb-0">{{ record.patient.full_name }} - {{ record.get_record_type_display }}</p>
                    </div>
                    <div class="header-actions">
                        {% if not record.is_completed %}
                        <a href="{% url 'medical_records:edit_record' record.pk %}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>
                            تعديل السجل
                        </a>
                        {% endif %}
                        <a href="{% url 'medical_records:print_record' record.pk %}" class="btn btn-outline-light">
                            <i class="fas fa-print me-2"></i>
                            طباعة
                        </a>
                        <a href="{% url 'medical_records:record_list' %}" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left me-2"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Record Info Cards -->
    <div class="row mb-4">
        <!-- Basic Info -->
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="info-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات السجل
                    </h5>
                </div>
                <div class="card-body">
                    <div class="info-item">
                        <strong>رقم السجل:</strong>
                        <span>{{ record.record_id }}</span>
                    </div>
                    <div class="info-item">
                        <strong>نوع السجل:</strong>
                        <span class="type-badge type-{{ record.record_type|lower }}">
                            {{ record.get_record_type_display }}
                        </span>
                    </div>
                    <div class="info-item">
                        <strong>الأولوية:</strong>
                        <span class="priority-badge priority-{{ record.priority|lower }}">
                            {{ record.get_priority_display }}
                        </span>
                    </div>
                    <div class="info-item">
                        <strong>الطبيب:</strong>
                        <span>{{ record.doctor.full_name }}</span>
                    </div>
                    <div class="info-item">
                        <strong>القسم:</strong>
                        <span>{{ record.department.name }}</span>
                    </div>
                    <div class="info-item">
                        <strong>تاريخ الإنشاء:</strong>
                        <span>{{ record.created_at|date:"Y-m-d H:i" }}</span>
                    </div>
                    <div class="info-item">
                        <strong>الحالة:</strong>
                        <span>
                            {% if record.is_completed %}
                                <span class="badge bg-success">مكتمل</span>
                            {% else %}
                                <span class="badge bg-warning">قيد التحرير</span>
                            {% endif %}
                            {% if record.is_confidential %}
                                <span class="badge bg-danger ms-1">سري</span>
                            {% endif %}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Patient Info -->
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="info-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        معلومات المريض
                    </h5>
                </div>
                <div class="card-body">
                    <div class="info-item">
                        <strong>الاسم:</strong>
                        <span>{{ record.patient.full_name }}</span>
                    </div>
                    <div class="info-item">
                        <strong>رقم المريض:</strong>
                        <span>{{ record.patient.patient_id }}</span>
                    </div>
                    <div class="info-item">
                        <strong>العمر:</strong>
                        <span>{{ record.patient.age }} سنة</span>
                    </div>
                    <div class="info-item">
                        <strong>الجنس:</strong>
                        <span>{{ record.patient.get_gender_display }}</span>
                    </div>
                    <div class="info-item">
                        <strong>فصيلة الدم:</strong>
                        <span>{{ record.patient.blood_type|default:"غير محدد" }}</span>
                    </div>
                    <div class="info-item">
                        <strong>الهاتف:</strong>
                        <span>{{ record.patient.phone }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-lg-4 col-md-12 mb-3">
            <div class="info-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 mb-2">
                            <a href="{% url 'medical_records:add_diagnosis' record.pk %}" class="quick-action-btn">
                                <i class="fas fa-stethoscope"></i>
                                <span>إضافة تشخيص</span>
                            </a>
                        </div>
                        <div class="col-6 mb-2">
                            <a href="{% url 'medical_records:add_treatment' record.pk %}" class="quick-action-btn">
                                <i class="fas fa-pills"></i>
                                <span>إضافة علاج</span>
                            </a>
                        </div>
                        <div class="col-6 mb-2">
                            <a href="{% url 'medical_records:add_lab_test' record.pk %}" class="quick-action-btn">
                                <i class="fas fa-vial"></i>
                                <span>طلب فحص</span>
                            </a>
                        </div>
                        <div class="col-6 mb-2">
                            <a href="{% url 'medical_records:add_radiology' record.pk %}" class="quick-action-btn">
                                <i class="fas fa-x-ray"></i>
                                <span>طلب أشعة</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Medical Record Content -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="content-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clipboard-list me-2"></i>
                        محتوى السجل الطبي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <h6><i class="fas fa-comment-medical me-2"></i>الشكوى الرئيسية</h6>
                            <p class="medical-content">{{ record.chief_complaint }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6><i class="fas fa-history me-2"></i>تاريخ المرض الحالي</h6>
                            <p class="medical-content">{{ record.history_of_present_illness }}</p>
                        </div>
                    </div>
                    
                    {% if record.past_medical_history %}
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <h6><i class="fas fa-file-medical-alt me-2"></i>التاريخ الطبي السابق</h6>
                            <p class="medical-content">{{ record.past_medical_history }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6><i class="fas fa-users me-2"></i>التاريخ العائلي</h6>
                            <p class="medical-content">{{ record.family_history|default:"لا يوجد" }}</p>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <h6><i class="fas fa-search me-2"></i>الفحص السريري</h6>
                            <p class="medical-content">{{ record.physical_examination }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6><i class="fas fa-clipboard-check me-2"></i>التقييم</h6>
                            <p class="medical-content">{{ record.assessment }}</p>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12 mb-3">
                            <h6><i class="fas fa-tasks me-2"></i>الخطة العلاجية</h6>
                            <p class="medical-content">{{ record.plan }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Diagnoses, Treatments, Tests -->
    <div class="row">
        <!-- Diagnoses -->
        <div class="col-lg-4 mb-4">
            <div class="section-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-stethoscope me-2"></i>
                        التشخيصات
                    </h5>
                    <a href="{% url 'medical_records:add_diagnosis' record.pk %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i>
                    </a>
                </div>
                <div class="card-body">
                    {% for diagnosis in record.diagnoses.all %}
                    <div class="item-card">
                        <div class="d-flex justify-content-between">
                            <strong>{{ diagnosis.diagnosis_name }}</strong>
                            <span class="badge bg-{{ diagnosis.diagnosis_type|lower }}">{{ diagnosis.get_diagnosis_type_display }}</span>
                        </div>
                        <p class="mb-1">{{ diagnosis.description|truncatewords:15 }}</p>
                        <small class="text-muted">{{ diagnosis.diagnosed_at|date:"Y-m-d H:i" }}</small>
                    </div>
                    {% empty %}
                    <p class="text-muted text-center">لا توجد تشخيصات</p>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Treatments -->
        <div class="col-lg-4 mb-4">
            <div class="section-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-pills me-2"></i>
                        العلاجات
                    </h5>
                    <a href="{% url 'medical_records:add_treatment' record.pk %}" class="btn btn-sm btn-success">
                        <i class="fas fa-plus"></i>
                    </a>
                </div>
                <div class="card-body">
                    {% for treatment in record.treatments.all %}
                    <div class="item-card">
                        <div class="d-flex justify-content-between">
                            <strong>{{ treatment.treatment_name }}</strong>
                            <span class="badge bg-{{ treatment.status|lower }}">{{ treatment.get_status_display }}</span>
                        </div>
                        <p class="mb-1">{{ treatment.description|truncatewords:10 }}</p>
                        {% if treatment.dosage %}
                        <small class="text-muted">الجرعة: {{ treatment.dosage }}</small>
                        {% endif %}
                    </div>
                    {% empty %}
                    <p class="text-muted text-center">لا توجد علاجات</p>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Lab Tests -->
        <div class="col-lg-4 mb-4">
            <div class="section-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-vial me-2"></i>
                        الفحوصات المخبرية
                    </h5>
                    <a href="{% url 'medical_records:add_lab_test' record.pk %}" class="btn btn-sm btn-warning">
                        <i class="fas fa-plus"></i>
                    </a>
                </div>
                <div class="card-body">
                    {% for test in record.lab_tests.all %}
                    <div class="item-card">
                        <div class="d-flex justify-content-between">
                            <strong>{{ test.test_name }}</strong>
                            <span class="badge bg-{{ test.status|lower }}">{{ test.get_status_display }}</span>
                        </div>
                        {% if test.result_value %}
                        <p class="mb-1">النتيجة: {{ test.result_value }}</p>
                        {% endif %}
                        <small class="text-muted">{{ test.created_at|date:"Y-m-d" }}</small>
                    </div>
                    {% empty %}
                    <p class="text-muted text-center">لا توجد فحوصات</p>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.record-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-radius: 15px;
    padding: 2rem;
    color: white;
    margin-bottom: 1rem;
}

.header-actions .btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    margin-left: 0.5rem;
}

.header-actions .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
}

.info-card, .content-card, .section-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    height: 100%;
}

.info-card .card-header, .content-card .card-header, .section-card .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item strong {
    color: #495057;
    min-width: 120px;
}

.medical-content {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #28a745;
    margin-bottom: 0;
    white-space: pre-wrap;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem 0.5rem;
    background: #f8f9fa;
    border-radius: 10px;
    text-decoration: none;
    color: #495057;
    transition: all 0.3s ease;
    height: 80px;
    justify-content: center;
}

.quick-action-btn:hover {
    background: #28a745;
    color: white;
    transform: translateY(-2px);
    text-decoration: none;
}

.quick-action-btn i {
    font-size: 1.2rem;
    margin-bottom: 0.25rem;
}

.quick-action-btn span {
    font-size: 0.8rem;
    text-align: center;
}

.item-card {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border-left: 4px solid #28a745;
}

.item-card:last-child {
    margin-bottom: 0;
}

.type-badge, .priority-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 500;
}

.type-badge.type-consultation {
    background: #e3f2fd;
    color: #1976d2;
}

.type-badge.type-diagnosis {
    background: #f3e5f5;
    color: #7b1fa2;
}

.type-badge.type-treatment {
    background: #e8f5e8;
    color: #388e3c;
}

.type-badge.type-surgery {
    background: #fff3e0;
    color: #f57c00;
}

.type-badge.type-emergency {
    background: #ffebee;
    color: #d32f2f;
}

.priority-badge.priority-low {
    background: #e8f5e8;
    color: #388e3c;
}

.priority-badge.priority-normal {
    background: #e3f2fd;
    color: #1976d2;
}

.priority-badge.priority-high {
    background: #fff3e0;
    color: #f57c00;
}

.priority-badge.priority-urgent {
    background: #ffebee;
    color: #d32f2f;
}

.priority-badge.priority-critical {
    background: #fce4ec;
    color: #c2185b;
}

@media (max-width: 768px) {
    .record-header {
        text-align: center;
    }
    
    .record-header .d-flex {
        flex-direction: column;
        gap: 1rem;
    }
    
    .info-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .info-item strong {
        min-width: auto;
        margin-bottom: 0.25rem;
    }
    
    .quick-action-btn {
        height: 70px;
        padding: 0.75rem 0.5rem;
    }
}
</style>
{% endblock %}
