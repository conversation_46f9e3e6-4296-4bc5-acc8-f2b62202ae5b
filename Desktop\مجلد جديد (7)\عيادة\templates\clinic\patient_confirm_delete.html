{% extends 'base.html' %}

{% block title %}حذف المريض - نظام العيادة الطبية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom" style="margin-top: 50px;">
    <h1 class="h2">
        <i class="bi bi-person-x text-danger"></i>
        حذف المريض
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'patient_detail' patient.pk %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right"></i> العودة
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-exclamation-triangle"></i> تأكيد الحذف
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <h6><i class="bi bi-exclamation-triangle"></i> تحذير مهم:</h6>
                    <p class="mb-0">
                        هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع البيانات المرتبطة بهذا المريض
                        بما في ذلك المواعيد والتشخيصات والوصفات الطبية والمدفوعات.
                    </p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6>بيانات المريض المراد حذفه:</h6>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>الاسم الكامل:</strong></td>
                                <td>{{ patient.full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>العمر:</strong></td>
                                <td>{{ patient.age }} سنة</td>
                            </tr>
                            <tr>
                                <td><strong>الجنس:</strong></td>
                                <td>{{ patient.get_gender_display }}</td>
                            </tr>
                            <tr>
                                <td><strong>رقم الهاتف:</strong></td>
                                <td>{{ patient.phone }}</td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ التسجيل:</strong></td>
                                <td>{{ patient.created_at|date:"d/m/Y" }}</td>
                            </tr>
                        </table>
                    </div>

                    <div class="col-md-6">
                        <h6>الإحصائيات المرتبطة:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                المواعيد
                                <span class="badge bg-primary rounded-pill">{{ patient.appointment_set.count }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                التشخيصات
                                <span class="badge bg-info rounded-pill">{{ patient.diagnosis_set.count }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                الوصفات الطبية
                                <span class="badge bg-warning rounded-pill">{{ patient.prescription_set.count }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                المدفوعات
                                <span class="badge bg-success rounded-pill">{{ patient.payment_set.count }}</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <hr>

                <div class="text-center">
                    <p class="text-muted mb-4">
                        هل أنت متأكد من رغبتك في حذف هذا المريض وجميع البيانات المرتبطة به؟
                    </p>

                    <form method="post" class="d-inline">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-danger me-3">
                            <i class="bi bi-trash"></i> نعم، احذف المريض
                        </button>
                    </form>

                    <a href="{% url 'patient_detail' patient.pk %}" class="btn btn-secondary">
                        <i class="bi bi-x-circle"></i> إلغاء
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add confirmation dialog
    const deleteForm = document.querySelector('form');
    if (deleteForm) {
        deleteForm.addEventListener('submit', function(e) {
            if (!confirm('هل أنت متأكد من رغبتك في حذف هذا المريض؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                e.preventDefault();
            }
        });
    }
});
</script>
{% endblock %}
